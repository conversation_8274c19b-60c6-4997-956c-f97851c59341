2025-08-04 17:20:36.290 [INFO] 20704 [kground-preinit] o.h.validator.internal.util.Version     [Version.java:21] : HV000001: Hibernate Validator 6.2.3.Final
2025-08-04 17:20:36.326 [INFO] 20704 [           main] c.w.w.c.WebotConnectorApplication       [StartupInfoLogger.java:55] : Starting WebotConnectorApplication using Java 1.8.0_301 on QH20071510L with PID 20704 (D:\code\bot\bot-server-connector\webot-app\bot-server-connector\target\classes started by leon.li in D:\code\bot\bot-server-connector)
2025-08-04 17:20:36.328 [INFO] 20704 [           main] c.w.w.c.WebotConnectorApplication       [SpringApplication.java:640] : No active profile set, falling back to 1 default profile: "default"
2025-08-04 17:20:37.234 [INFO] 20704 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:262] : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 17:20:37.235 [INFO] 20704 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:132] : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04 17:20:37.417 [INFO] 20704 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:201] : Finished Spring Data repository scanning in 175 ms. Found 28 JPA repository interfaces.
2025-08-04 17:20:37.419 [INFO] 20704 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:262] : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 17:20:37.419 [INFO] 20704 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:132] : Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 17:20:37.440 [INFO] 20704 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:201] : Finished Spring Data repository scanning in 20 ms. Found 6 MongoDB repository interfaces.
2025-08-04 17:20:37.495 [INFO] 20704 [           main] c.w.w.d.redis.config.SmartRedisRegister [SmartRedisRegister.java:131] : Registration redis (bot) !
2025-08-04 17:20:37.498 [INFO] 20704 [           main] c.w.w.d.redis.config.SmartRedisRegister [SmartRedisRegister.java:131] : Registration redis (cs) !
2025-08-04 17:20:37.498 [INFO] 20704 [           main] c.w.w.d.redis.config.SmartRedisRegister [SmartRedisRegister.java:136] : Registration redis completed !
2025-08-04 17:20:37.712 [INFO] 20704 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:262] : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 17:20:37.713 [INFO] 20704 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:132] : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 17:20:37.736 [INFO] 20704 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:201] : Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-08-04 17:20:38.023 [INFO] 20704 [           main] c.w.p.i.j.DataSourceBeanPostProcessor   [DataSourceBeanPostProcessor.java:37] : 开启加密datasource增强
2025-08-04 17:20:38.036 [INFO] 20704 [           main] com.welab.privacy.config.PrivacyConfigs [PrivacyConfigs.java:34] : Init welab privacy config info.
2025-08-04 17:20:38.594 [INFO] 20704 [           main] com.welab.privacy.util.http.HttpClients [HttpClients.java:199] : Http client get Request url is 'https://japi-fat.wolaidai.com/privacy/api/v2/config-info?appId=bot-server-connector&secret=rpiGKd8%2FqKSqltodfVUweUtACVyTVcSmVKiWnr3J%2Fn0%3D'.
2025-08-04 17:20:39.034 [INFO] 20704 [           main] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:251] : Welab privacy config cache info key set : [cs_bot:black_list, cs_bot:face_detection, cs_bot:session_detail_report, qa_bot:user, qa_bot:organization, qa_bot:admin_user, cs_bot:queue_list, cs_bot:session_list, cs_bot:message_event, webot-v2:conversations, webot-v2:histories, webot-v2:feedbacks, cs_bot:call_detail, qa_bot:call_detail, cs_bot:black_list_test, cs_bot:user_verification_history]
2025-08-04 17:20:39.035 [INFO] 20704 [           main] trationDelegate$BeanPostProcessorChecker[PostProcessorRegistrationDelegate.java:376] : Bean 'privacyConfigs' of type [com.welab.privacy.config.PrivacyConfigs] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:20:39.280 [INFO] 20704 [           main] org.eclipse.jetty.util.log              [Log.java:170] : Logging initialized @10187ms to org.eclipse.jetty.util.log.Slf4jLog
2025-08-04 17:20:39.512 [INFO] 20704 [           main] o.s.b.w.e.j.JettyServletWebServerFactory[JettyServletWebServerFactory.java:166] : Server initialized with port: 9595
2025-08-04 17:20:39.514 [INFO] 20704 [           main] org.eclipse.jetty.server.Server         [Server.java:375] : jetty-9.4.45.v20220203; built: 2022-02-03T09:14:34.105Z; git: 4a0c91c0be53805e3fcffdcdcc9587d5301863db; jvm 1.8.0_301-b09
2025-08-04 17:20:39.538 [INFO] 20704 [           main] o.e.j.s.h.ContextHandler.application    [ContextHandler.java:2368] : Initializing Spring embedded WebApplicationContext
2025-08-04 17:20:39.539 [INFO] 20704 [           main] w.s.c.ServletWebServerApplicationContext[ServletWebServerApplicationContext.java:290] : Root WebApplicationContext: initialization completed in 3172 ms
2025-08-04 17:20:39.615 [INFO] 20704 [           main] org.eclipse.jetty.server.session        [DefaultSessionIdManager.java:334] : DefaultSessionIdManager workerName=node0
2025-08-04 17:20:39.615 [INFO] 20704 [           main] org.eclipse.jetty.server.session        [DefaultSessionIdManager.java:339] : No SessionScavenger set, using defaults
2025-08-04 17:20:39.616 [INFO] 20704 [           main] org.eclipse.jetty.server.session        [HouseKeeper.java:132] : node0 Scavenging every 600000ms
2025-08-04 17:20:39.623 [INFO] 20704 [           main] o.e.jetty.server.handler.ContextHandler [ContextHandler.java:921] : Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@71e7c13f{application,/api,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.9595.1141596011606792823/, jar:file:/D:/software/maven/apache-maven-3.8.6-bin/apache-maven-3.8.6/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar!/META-INF/resources],AVAILABLE}
2025-08-04 17:20:39.625 [INFO] 20704 [           main] org.eclipse.jetty.server.Server         [Server.java:415] : Started @10531ms
2025-08-04 17:20:39.654 [INFO] 20704 [           main] c.w.p.i.j.DataSourceBeanPostProcessor   [DataSourceBeanPostProcessor.java:54] : enhance HikariDataSource (null)
2025-08-04 17:20:39.709 [INFO] 20704 [           main] com.zaxxer.hikari.HikariDataSource      [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-08-04 17:20:40.052 [INFO] 20704 [           main] com.zaxxer.hikari.HikariDataSource      [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-08-04 17:20:40.097 [INFO] 20704 [           main] o.hibernate.jpa.internal.util.LogHelper [LogHelper.java:31] : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04 17:20:40.139 [INFO] 20704 [           main] org.hibernate.Version                   [Version.java:44] : HHH000412: Hibernate ORM core version 5.6.7.Final
2025-08-04 17:20:40.301 [INFO] 20704 [           main] o.hibernate.annotations.common.Version  [JavaReflectionManager.java:56] : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-04 17:20:40.405 [INFO] 20704 [           main] org.hibernate.dialect.Dialect           [Dialect.java:175] : HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-08-04 17:20:40.939 [INFO] 20704 [           main] Hibernate Types                         [Configuration.java:304] : This framework is proudly powered by:

>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
 _    _                           _     _
| |  | |                         (_)   | |
| |__| |_   _ _ __   ___ _ __ ___ _ ___| |_ ___ _ __   ___ ___
|  __  | | | | '_ \ / _ \ '__/ __| / __| __/ _ \ '_ \ / __/ _ \
| |  | | |_| | |_) |  __/ |  \__ \ \__ \ ||  __/ | | | (_|  __/
|_|  |_|\__, | .__/ \___|_|  |___/_|___/\__\___|_| |_|\___\___|
         __/ | |
        |___/|_|

At Hypersistence, we only build amazing tools, like Hibernate Types, Flexy Pool, or Hypersistence Optimizer.

What if there were a tool that could automatically detect JPA and Hibernate performance issues?

Hypersistence Optimizer is that tool! For more details, go to: 

https://vladmihalcea.com/hypersistence-optimizer/
<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

2025-08-04 17:20:41.562 [INFO] 20704 [           main] o.h.e.t.j.p.i.JtaPlatformInitiator      [JtaPlatformInitiator.java:52] : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-04 17:20:41.576 [INFO] 20704 [           main] j.LocalContainerEntityManagerFactoryBean[AbstractEntityManagerFactoryBean.java:437] : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04 17:20:41.595 [INFO] 20704 [           main] f.a.AutowiredAnnotationBeanPostProcessor[AutowiredAnnotationBeanPostProcessor.java:502] : Autowired annotation should only be used on methods with parameters: public void com.wolaidai.webot.connector.config.AppConfig.init()
2025-08-04 17:20:42.358 [INFO] 20704 [           main] org.mongodb.driver.client               [SLF4JLogger.java:71] : MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.6.1"}, "os": {"type": "Windows", "name": "Windows 10", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Oracle Corporation/1.8.0_301-b09"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='test', source='webot-v2', password=<hidden>, mechanismProperties=<hidden>}, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@1c0455c4]}, clusterSettings={hosts=[**********:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
2025-08-04 17:20:42.403 [INFO] 20704 [*********:27017] org.mongodb.driver.connection           [SLF4JLogger.java:71] : Opened connection [connectionId{localValue:1, serverValue:12291}] to **********:27017
2025-08-04 17:20:42.403 [INFO] 20704 [*********:27017] org.mongodb.driver.connection           [SLF4JLogger.java:71] : Opened connection [connectionId{localValue:2, serverValue:12292}] to **********:27017
2025-08-04 17:20:42.404 [INFO] 20704 [*********:27017] org.mongodb.driver.cluster              [SLF4JLogger.java:71] : Monitor thread successfully connected to server with description ServerDescription{address=**********:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=7, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=31812400, setName='rs', canonicalAddress=**********:27017, hosts=[**********:27017], passives=[], arbiters=[], primary='**********:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000000e, setVersion=54862, topologyVersion=null, lastWriteDate=Mon Aug 04 17:20:34 CST 2025, lastUpdateTimeNanos=29198714636200}
2025-08-04 17:20:42.763 [INFO] 20704 [           main] org.redisson.Version                    [Version.java:41] : Redisson 3.17.3
2025-08-04 17:20:43.695 [INFO] 20704 [isson-netty-2-9] o.r.c.pool.MasterPubSubConnectionPool   [ConnectionPool.java:158] : 1 connections initialized for **********/**********:6379
2025-08-04 17:20:44.077 [INFO] 20704 [sson-netty-2-19] o.r.c.pool.MasterConnectionPool         [ConnectionPool.java:158] : 24 connections initialized for **********/**********:6379
2025-08-04 17:20:44.912 [INFO] 20704 [           main] org.mongodb.driver.connection           [SLF4JLogger.java:71] : Opened connection [connectionId{localValue:3, serverValue:12293}] to **********:27017
2025-08-04 17:20:45.980 [WARN] 20704 [           main] o.s.b.a.f.FreeMarkerAutoConfiguration   [FreeMarkerAutoConfiguration.java:65] : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-08-04 17:20:46.054 [INFO] 20704 [           main] o.e.j.s.h.ContextHandler.application    [ContextHandler.java:2368] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 17:20:46.054 [INFO] 20704 [           main] o.s.web.servlet.DispatcherServlet       [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-08-04 17:20:46.055 [INFO] 20704 [           main] o.s.web.servlet.DispatcherServlet       [FrameworkServlet.java:547] : Completed initialization in 1 ms
2025-08-04 17:20:46.063 [INFO] 20704 [           main] o.e.jetty.server.AbstractConnector      [AbstractConnector.java:333] : Started ServerConnector@42e202d7{HTTP/1.1, (http/1.1)}{0.0.0.0:9595}
2025-08-04 17:20:46.064 [INFO] 20704 [           main] o.s.b.web.embedded.jetty.JettyWebServer [JettyWebServer.java:172] : Jetty started on port(s) 9595 (http/1.1) with context path '/api'
2025-08-04 17:20:46.742 [INFO] 20704 [           main] c.w.w.c.WebotConnectorApplication       [StartupInfoLogger.java:61] : Started WebotConnectorApplication in 10.938 seconds (JVM running for 17.649)
2025-08-04 17:20:46.745 [INFO] 20704 [           main] org.eclipse.jetty.server.Server         [Server.java:375] : jetty-9.4.45.v20220203; built: 2022-02-03T09:14:34.105Z; git: 4a0c91c0be53805e3fcffdcdcc9587d5301863db; jvm 1.8.0_301-b09
2025-08-04 17:20:46.746 [INFO] 20704 [           main] org.eclipse.jetty.server.session        [DefaultSessionIdManager.java:334] : DefaultSessionIdManager workerName=node0
2025-08-04 17:20:46.746 [INFO] 20704 [           main] org.eclipse.jetty.server.session        [DefaultSessionIdManager.java:339] : No SessionScavenger set, using defaults
2025-08-04 17:20:46.746 [INFO] 20704 [           main] org.eclipse.jetty.server.session        [HouseKeeper.java:132] : node0 Scavenging every 660000ms
2025-08-04 17:20:46.747 [INFO] 20704 [           main] o.e.jetty.server.handler.ContextHandler [ContextHandler.java:921] : Started o.e.j.s.ServletContextHandler@7fab8e9a{/,null,AVAILABLE}
2025-08-04 17:20:46.750 [INFO] 20704 [           main] o.e.jetty.server.AbstractConnector      [AbstractConnector.java:333] : Started ServerConnector@4a8c4dae{HTTP/1.1, (http/1.1)}{0.0.0.0:9093}
2025-08-04 17:20:46.750 [INFO] 20704 [           main] org.eclipse.jetty.server.Server         [Server.java:415] : Started @17657ms
2025-08-04 17:20:46.750 [INFO] 20704 [           main] c.w.w.connector.config.EngineIoConfig   [EngineIoConfig.java:75] : Socket server started.
2025-08-04 17:20:46.752 [INFO] 20704 [           main] .s.a.AnnotationAsyncExecutionInterceptor[AsyncExecutionAspectSupport.java:243] : More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [threadPool, taskScheduler]
2025-08-04 17:20:48.536 [INFO] 20704 [           main] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:82] : welab privacy config info already initialized.
2025-08-04 17:20:48.539 [INFO] 20704 [           main] com.welab.privacy.util.http.HttpClients [HttpClients.java:199] : Http client get Request url is 'https://japi-fat.wolaidai.com/privacy/api/v2/config-info?appId=bot-server-connector&secret=rpiGKd8%2FqKSqltodfVUweUtACVyTVcSmVKiWnr3J%2Fn0%3D'.
2025-08-04 17:21:39.042 [INFO] 20704 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:277] : Refresh welab privacy config info local cache.
2025-08-04 17:21:39.042 [INFO] 20704 [ConfigScheduler] c.welab.privacy.manager.PrivacyManager  [PrivacyManager.java:504] : Clear privacy element manager local cache. 
2025-08-04 17:21:39.043 [INFO] 20704 [ConfigScheduler] com.welab.privacy.util.http.HttpClients [HttpClients.java:199] : Http client get Request url is 'https://japi-fat.wolaidai.com/privacy/api/v2/config-info?appId=bot-server-connector&secret=rpiGKd8%2FqKSqltodfVUweUtACVyTVcSmVKiWnr3J%2Fn0%3D'.
2025-08-04 17:21:39.172 [INFO] 20704 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:251] : Welab privacy config cache info key set : [cs_bot:black_list, cs_bot:face_detection, cs_bot:session_detail_report, qa_bot:user, qa_bot:organization, qa_bot:admin_user, cs_bot:queue_list, cs_bot:session_list, cs_bot:message_event, webot-v2:conversations, webot-v2:histories, webot-v2:feedbacks, cs_bot:call_detail, qa_bot:call_detail, cs_bot:black_list_test, cs_bot:user_verification_history]
2025-08-04 17:22:39.182 [INFO] 20704 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:277] : Refresh welab privacy config info local cache.
2025-08-04 17:22:39.183 [INFO] 20704 [ConfigScheduler] c.welab.privacy.manager.PrivacyManager  [PrivacyManager.java:504] : Clear privacy element manager local cache. 
2025-08-04 17:22:39.185 [INFO] 20704 [ConfigScheduler] com.welab.privacy.util.http.HttpClients [HttpClients.java:199] : Http client get Request url is 'https://japi-fat.wolaidai.com/privacy/api/v2/config-info?appId=bot-server-connector&secret=rpiGKd8%2FqKSqltodfVUweUtACVyTVcSmVKiWnr3J%2Fn0%3D'.
2025-08-04 17:22:39.292 [INFO] 20704 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:251] : Welab privacy config cache info key set : [cs_bot:black_list, cs_bot:face_detection, cs_bot:session_detail_report, qa_bot:user, qa_bot:organization, qa_bot:admin_user, cs_bot:queue_list, cs_bot:session_list, cs_bot:message_event, webot-v2:conversations, webot-v2:histories, webot-v2:feedbacks, cs_bot:call_detail, qa_bot:call_detail, cs_bot:black_list_test, cs_bot:user_verification_history]
2025-08-04 17:23:39.305 [INFO] 20704 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:277] : Refresh welab privacy config info local cache.
2025-08-04 17:23:39.305 [INFO] 20704 [ConfigScheduler] c.welab.privacy.manager.PrivacyManager  [PrivacyManager.java:504] : Clear privacy element manager local cache. 
2025-08-04 17:23:39.306 [INFO] 20704 [ConfigScheduler] com.welab.privacy.util.http.HttpClients [HttpClients.java:199] : Http client get Request url is 'https://japi-fat.wolaidai.com/privacy/api/v2/config-info?appId=bot-server-connector&secret=rpiGKd8%2FqKSqltodfVUweUtACVyTVcSmVKiWnr3J%2Fn0%3D'.
2025-08-04 17:23:39.492 [INFO] 20704 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:251] : Welab privacy config cache info key set : [cs_bot:black_list, cs_bot:face_detection, cs_bot:session_detail_report, qa_bot:user, qa_bot:organization, qa_bot:admin_user, cs_bot:queue_list, cs_bot:session_list, cs_bot:message_event, webot-v2:conversations, webot-v2:histories, webot-v2:feedbacks, cs_bot:call_detail, qa_bot:call_detail, cs_bot:black_list_test, cs_bot:user_verification_history]
2025-08-04 17:24:39.497 [INFO] 20704 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:277] : Refresh welab privacy config info local cache.
2025-08-04 17:24:39.497 [INFO] 20704 [ConfigScheduler] c.welab.privacy.manager.PrivacyManager  [PrivacyManager.java:504] : Clear privacy element manager local cache. 
2025-08-04 17:24:39.498 [INFO] 20704 [ConfigScheduler] com.welab.privacy.util.http.HttpClients [HttpClients.java:199] : Http client get Request url is 'https://japi-fat.wolaidai.com/privacy/api/v2/config-info?appId=bot-server-connector&secret=rpiGKd8%2FqKSqltodfVUweUtACVyTVcSmVKiWnr3J%2Fn0%3D'.
2025-08-04 17:24:39.975 [INFO] 20704 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:251] : Welab privacy config cache info key set : [cs_bot:black_list, cs_bot:face_detection, cs_bot:session_detail_report, qa_bot:user, qa_bot:organization, qa_bot:admin_user, cs_bot:queue_list, cs_bot:session_list, cs_bot:message_event, webot-v2:conversations, webot-v2:histories, webot-v2:feedbacks, cs_bot:call_detail, qa_bot:call_detail, cs_bot:black_list_test, cs_bot:user_verification_history]
2025-08-04 17:24:50.971 [INFO] 20704 [tp1090885142-39] c.w.w.c.advice.WebControllerAdvice      [WebControllerAdvice.java:47] : Before request:[uri=/api/quality-inspection/callback;method=POST;client=127.0.0.1]
2025-08-04 17:25:12.781 [INFO] 20704 [tp1090885142-39] c.w.w.c.c.QualityInspectionController   [QualityInspectionController.java:30] : 收到质量检查回调, taskId:null, status:null, sessionKey:null
2025-08-04 17:25:12.781 [ERROR] 20704 [tp1090885142-39] c.w.w.c.c.QualityInspectionController   [QualityInspectionController.java:35] : 质量检查回调参数错误: taskId为空
2025-08-04 17:25:12.788 [INFO] 20704 [tp1090885142-39] c.w.w.c.advice.WebControllerAdvice      [WebControllerAdvice.java:54] : After request:[uri=/api/quality-inspection/callback;method=POST;client=127.0.0.1;responseBody={"msg":"taskId不能为空","ret":1}],cost:21817ms
2025-08-04 17:25:18.247 [INFO] 20704 [ionShutdownHook] o.e.jetty.server.AbstractConnector      [AbstractConnector.java:383] : Stopped ServerConnector@42e202d7{HTTP/1.1, (http/1.1)}{0.0.0.0:9595}
2025-08-04 17:25:18.248 [INFO] 20704 [ionShutdownHook] org.eclipse.jetty.server.session        [HouseKeeper.java:149] : node0 Stopped scavenging
2025-08-04 17:25:18.250 [INFO] 20704 [ionShutdownHook] o.e.j.s.h.ContextHandler.application    [ContextHandler.java:2368] : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04 17:25:18.251 [INFO] 20704 [ionShutdownHook] o.e.jetty.server.handler.ContextHandler [ContextHandler.java:1159] : Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@71e7c13f{application,/api,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.9595.1141596011606792823/, jar:file:/D:/software/maven/apache-maven-3.8.6-bin/apache-maven-3.8.6/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar!/META-INF/resources],STOPPED}
2025-08-04 17:25:18.508 [ERROR] 20704 [cTaskExecutor-1] c.w.w.c.s.business.KeyExpireService     [KeyExpireService.java:78] : checkExpireKey error
java.lang.IllegalStateException: LettuceConnectionFactory was destroyed and cannot be used anymore
	at org.springframework.util.Assert.state(Assert.java:76)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.assertInitialized(LettuceConnectionFactory.java:1263)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:414)
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:210)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:190)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultZSetOperations.rangeByScoreWithScores(DefaultZSetOperations.java:357)
	at com.wolaidai.webot.connector.service.business.KeyExpireService.checkExpireKey(KeyExpireService.java:67)
	at com.wolaidai.webot.connector.service.business.KeyExpireService$$FastClassBySpringCGLIB$$754593e5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.lang.Thread.run(Thread.java:748)
2025-08-04 17:25:18.691 [INFO] 20704 [ionShutdownHook] j.LocalContainerEntityManagerFactoryBean[AbstractEntityManagerFactoryBean.java:651] : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04 17:25:18.695 [INFO] 20704 [ionShutdownHook] com.zaxxer.hikari.HikariDataSource      [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-08-04 17:25:18.705 [INFO] 20704 [ionShutdownHook] com.zaxxer.hikari.HikariDataSource      [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
