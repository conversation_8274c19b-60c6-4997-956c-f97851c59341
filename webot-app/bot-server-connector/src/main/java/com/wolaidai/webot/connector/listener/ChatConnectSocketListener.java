package com.wolaidai.webot.connector.listener;

import com.wolaidai.webot.connector.constant.RedisConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.DataProcessService;
import com.wolaidai.webot.connector.service.chat.*;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@AllArgsConstructor
@Component
public class ChatConnectSocketListener extends BaseEmitterListener {
    private final RedisTemplate csRedisTemplate;
    private final ApplicationContext applicationContext;

    @Override
    public void call(Object... args) {
        super.call(args);
        SocketIoSocket socket = (SocketIoSocket) args[0];
        addEvent(socket,SocketEvent.MESSAGE, ChatMessageService.class);
        addEvent(socket,SocketEvent.EVENT, ChatEventService.class);
        addEvent(socket,SocketEvent.DISCONNECT, ChatDisconnectService.class);
        addEvent(socket,SocketEvent.PING, ChatPingService.class);
        addEvent(socket,SocketEvent.TYPING, TypingService.class);
        addEvent(socket,SocketEvent.INFO, ChatInfoService.class);
    }

    private void addEvent(SocketIoSocket socket, String event, Class<? extends DataProcessService> clazz){
        DataProcessService dataProcessService = applicationContext.getBean(clazz);
        socket.on(event,args -> {
            Object data = get(args,0);
            SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback;
            if(data instanceof SocketIoSocket.ReceivedByLocalAcknowledgementCallback){
                callback = (SocketIoSocket.ReceivedByLocalAcknowledgementCallback)data;
                data = null;
            }else {
                callback = getCallback(args);
            }
            List<SocketPacketModel> socketPacketModel = dataProcessService.process(new MessageModel(event, System.currentTimeMillis(), socket.getNamespace().getName(), socket.getId(), getClientIp(socket), getUa(socket), data, callback));
            if(!CollectionUtils.isEmpty(socketPacketModel)) {
                csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, socket.getNamespace().getName()), socketPacketModel);
            }
        });
    }
}
