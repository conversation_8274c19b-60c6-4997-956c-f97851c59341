package com.wolaidai.webot.connector.service.business;

import com.wolaidai.webot.connector.service.BaseService;
import com.wolaidai.webot.connector.utils.MsgIdUtils;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.entity.TestChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.repo.ChatHistoryRepo;
import com.wolaidai.webot.data.mongodb.repo.TestChatHistoryRepo;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class ChatHistoryService extends BaseService {

    private final ChatHistoryRepo chatHistoryRepo;
    private final TestChatHistoryRepo testChatHistoryRepo;

    public ChatHistoryEntity createChatHistory(String msgId,String clientId,Integer clientType,String origin,String scene,String sender,String type,String eventKey,String account,Integer orgId,Integer botId,Integer skillGroupId,String gcid,String cid,String content,Map<String,Object> media,Map<String,Object> manual,boolean save){
        return createChatHistory(msgId,clientId,clientType,origin,scene,sender,type,eventKey,account,orgId,botId,skillGroupId,gcid,cid,content,null,null,media,null, manual, save);
    }

    public ChatHistoryEntity createChatHistory(String clientId, Integer clientType, String scene, String sender, String type, String eventKey, String account, Integer orgId, Integer botId, Integer skillGroupId, String gcid, String content, boolean save){
        return createChatHistory(null,clientId,clientType,scene,sender,type,eventKey,account,orgId,botId,skillGroupId,gcid,null,content,null,null,null,null, null, save);
    }

    public ChatHistoryEntity createChatHistory(String clientId, Integer clientType, String scene, String sender, String type, String eventKey, String account, Integer orgId, Integer botId, Integer skillGroupId, String gcid, String content, Map<String,Object> extend, Map<String,Object> extra, Map<String,Object> manual, boolean save){
        return createChatHistory(null,clientId,clientType,scene,sender,type,eventKey,account,orgId,botId,skillGroupId,gcid,null,content,extend,extra,null,null, manual, save);
    }

    public ChatHistoryEntity createChatHistory(String msgId, String clientId, Integer clientType, String scene, String sender, String type, String eventKey, String account, Integer orgId, Integer botId, Integer skillGroupId, String gcid, String cid, String content, Map<String,Object> extend, Map<String,Object> extra, Map<String,Object> media, Map<String,Object> menu, Map<String,Object> manual, boolean save){
        return createChatHistory(msgId,clientId,clientType,null, scene,sender,type,eventKey,account,orgId,botId,skillGroupId,gcid,cid,content,extend,extra,media,menu, manual, save);
    }

    public ChatHistoryEntity createChatHistory(String msgId, String clientId, Integer clientType, String scene, String sender, String type, String eventKey, String account, Integer orgId, Integer botId, Integer skillGroupId, String gcid, String cid, String content, Map<String,Object> extend, boolean save){
        return createChatHistory(msgId,clientId,clientType,null, scene,sender,type,eventKey,account,orgId,botId,skillGroupId,gcid,cid,content,extend,null,null,null, null, save);
    }

    public ChatHistoryEntity createChatHistory(String msgId, String clientId, Integer clientType, String origin, String scene, String sender, String type, String eventKey, String account, Integer orgId, Integer botId, Integer skillGroupId, String gcid, String cid, String content, Map<String,Object> extend, Map<String,Object> extra, Map<String,Object> media, Map<String,Object> menu, Map<String,Object> manual, boolean save){
        ChatHistoryEntity chatHistoryEntity = new ChatHistoryEntity();
        chatHistoryEntity.setAccount(account);
        chatHistoryEntity.setBotId(botId);
        chatHistoryEntity.setSkillGroupId(skillGroupId);
        chatHistoryEntity.setClientId(clientId);
        chatHistoryEntity.setClientType(clientType);
        chatHistoryEntity.setOrigin(origin);
        chatHistoryEntity.setOrgId(orgId);
        chatHistoryEntity.setGcid(gcid);
        chatHistoryEntity.setCid(cid);
        if(msgId==null){
            msgId = MsgIdUtils.generateMsgId();
        }
        chatHistoryEntity.setMsgId(msgId);
        chatHistoryEntity.setScene(scene);
        chatHistoryEntity.setSender(sender);
        chatHistoryEntity.setType(type);
        chatHistoryEntity.setEventKey(eventKey);
        chatHistoryEntity.setContent(content);
        chatHistoryEntity.setDate(new Date());
        chatHistoryEntity.setExtend(extend);
        chatHistoryEntity.setExtra(extra);
        chatHistoryEntity.setMedia(media);
        chatHistoryEntity.setMenu(menu);
        chatHistoryEntity.setManual(manual);
        if(save) {
            saveChatHistory(chatHistoryEntity);
        }
        return chatHistoryEntity;
    }

    public TestChatHistoryEntity createTestChatHistory(String msgId, String clientId, Integer clientType, String sender, String type, String account, Integer botId, Integer skillGroupId, Integer skillId, String content, Map<String,Object> extra, Map<String,Object> media, Map<String,Object> menu, boolean save){
        TestChatHistoryEntity chatHistoryEntity = new TestChatHistoryEntity();
        chatHistoryEntity.setAccount(account);
        chatHistoryEntity.setBotId(botId);
        chatHistoryEntity.setSkillGroupId(skillGroupId);
        chatHistoryEntity.setSkillId(skillId);
        chatHistoryEntity.setClientId(clientId);
        chatHistoryEntity.setClientType(clientType);
        if(msgId==null){
            msgId = MsgIdUtils.generateMsgId();
        }
        chatHistoryEntity.setMsgId(msgId);
        chatHistoryEntity.setSender(sender);
        chatHistoryEntity.setType(type);
        chatHistoryEntity.setContent(content);
        chatHistoryEntity.setDate(new Date());
        chatHistoryEntity.setExtra(extra);
        chatHistoryEntity.setMedia(media);
        chatHistoryEntity.setMenu(menu);
        if(save) {
            testChatHistoryRepo.save(chatHistoryEntity);
        }
        return chatHistoryEntity;
    }

    public ChatHistoryEntity getLastChatHistory(String clientId,String origin,Integer botId){
        Page<ChatHistoryEntity> page = chatHistoryRepo.findLastChatHistory(clientId, origin, botId, PageRequest.of(0, 1));
        if(page.hasContent()){
            List<ChatHistoryEntity> content = page.getContent();
            if(!content.isEmpty()){
                ChatHistoryEntity chatHistory = content.get(0);
                if("user".equals(chatHistory.getSender())){
                    return chatHistory;
                }
            }
        }
        return null;
    }

    public void saveChatHistory(ChatHistoryEntity chatHistory){
        chatHistoryRepo.save(chatHistory);
    }
}
