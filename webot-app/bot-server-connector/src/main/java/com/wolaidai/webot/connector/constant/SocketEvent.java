package com.wolaidai.webot.connector.constant;

public class SocketEvent {
    public static final String MESSAGE = "message";
    public static final String EVENT = "event";
    public static final String DISCONNECT = "disconnect";
    public static final String PING = "ping";
    public static final String TYPING = "typing";
    public static final String INFO = "info";

    public static final String INIT_TYPE = "init";
    public static final String INQUEUE_TYPE = "queue:inqueue";

    public static final String DEQUEUE_TYPE = "queue:dequeue";
    public static final String SESSION_SWITCH_TYPE = "session:switch";
    public static final String SATISFACTION_COMMIT_TYPE = "satisfaction:commit";
    public static final String SATISFACTION_TYPE = "satisfaction";
    public static final String RECALL_TYPE = "recall";
    public static final String SESSION_TRANS_TYPE = "session:trans";
    public static final String SESSION_TRANS_REMAINING_TYPE = "session:trans:remaining";
    public static final String SESSION_TRANS_RESULT_TYPE = "session:trans:result";
    public static final String CLIENT_STATUS_TYPE = "client:status";
    public static final String SESSION_CLOSE_TYPE = "session:close";
    public static final String TYPE_FACE_DETECTION = "facedetection";
    //内部聊天
    public static final String INNER_SESSION_INIT_TYPE = "inner:session:init";  //发起聊天
    public static final String INNER_SESSION_JOIN_TYPE = "inner:session:join";  //加入群聊
    public static final String INNER_SESSION_KICK_TYPE = "inner:session:kick";  //踢出群聊
    public static final String INNER_SESSION_EXIT_TYPE = "inner:session:exit";  //退出群聊
    public static final String INNER_SESSION_UPD_TYPE = "inner:session:upd";  //更新群聊
    public static final String INNER_SESSION_CUT_TYPE = "inner:session:cut";  //切换会话
    public static final String INNER_SESSION_RELOAD_TYPE = "inner:session:reload";  //重新加载

    public static final String LIKE_COMMIT_TYPE = "like:commit";

    public static final String VERIFY_TYPE = "verify";
}
