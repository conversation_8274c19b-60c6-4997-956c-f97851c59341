package com.wolaidai.webot.connector.service.event.chat;

import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.business.ManualChatService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.data.mysql.entity.bot.BotEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SatisfactionDataEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.repo.BotRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@AllArgsConstructor
public class SatisfactionEventService extends BaseEventService {

    private final SessionListRepo sessionListRepo;
    private final BotRepo botRepo;
    private final ManualChatService manualChatService;
    private final StringRedisTemplate csStringRedisTemplate;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject) model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        String source = data.getString("source");
        SessionListEntity sessionList = null;
        Integer satisfactionType = null;
        if (Objects.equals(source, "cs")) {
            JSONObject manual = data.getJSONObject("manual");
            if (manual != null) {
                String sessionKey = manual.getString("sessionKey");
                sessionList = sessionListRepo.findBySessionKey(sessionKey);
                satisfactionType = SatisfactionDataEntity.TYPE_SERVICEUSER;
            }
        }else  if (Objects.equals(source, "H5")) {
            String clientId = data.getString("clientId");
            String accessKey = data.getString("accessKey");
            HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
            BotEntity bot = botRepo.findByCode(accessKey);
            if (bot == null) return null;
            String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, bot.getId());
            String sessionKey = hashOperations.get(conversationKey, "sessionKey");
            if(sessionKey!=null){
                sessionList = sessionListRepo.findBySessionKey(sessionKey);
                satisfactionType = SatisfactionDataEntity.TYPE_CUSTOMER;
            }
        }
        if (sessionList != null) {
            boolean satisfactionSuccess = manualChatService.sendSatisfaction(sessionList, satisfactionType, models, null);
            if (satisfactionSuccess) {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret", 0).put("msg", "满意度下发成功").put("timestamp", System.currentTimeMillis()));
                }
            } else {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", "请勿重复下发满意度").put("timestamp", System.currentTimeMillis()));
                }
            }
        }
        return models;
    }
}
