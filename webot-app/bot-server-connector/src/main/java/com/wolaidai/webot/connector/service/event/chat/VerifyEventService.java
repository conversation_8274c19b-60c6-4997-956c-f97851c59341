package com.wolaidai.webot.connector.service.event.chat;

import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.business.ChatHistoryService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.connector.wechat.service.WechatCpService;
import com.wolaidai.webot.connector.wechat.service.WechatMessageService;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.UserVerification;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.mysql.repo.UserVerificationRepo;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@AllArgsConstructor
public class VerifyEventService extends BaseEventService {

    private final AppPropertyConfig appPropertyConfig;

    private WechatMessageService wechatMessageService;

    private WechatCpService wechatCpService;

    private ChatHistoryService chatHistoryService;

    private final SessionListRepo sessionListRepo;

    private final UserVerificationRepo userVerificationRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> modelList = new ArrayList<>();
        JSONObject data = (JSONObject) model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        JSONObject manual = data.getJSONObject("manual");
        if(manual != null) {
            String sessionKey = manual.getString("sessionKey");
            SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
            String info = this.isRequestValid(sessionKey, sessionList);
            if(StringUtils.isNotEmpty(info)) {
                if(callback != null) {
                    callback.sendAcknowledgement(new org.json.JSONObject().put("ret", -1).put("msg", info).put("timestamp", System.currentTimeMillis()));
                }
                return null;
            }

            String token = UUID.randomUUID().toString();
            LocalDateTime now = LocalDateTime.now();
            Integer clientType = sessionList.getClientTypeId();
            String clientId = sessionList.getClientId();
            Integer botId = sessionList.getBotId();
            UserVerification userVerification = new UserVerification();
            userVerification.setOrgId(sessionList.getOrgId());
            userVerification.setToken(token);
            userVerification.setClientType(clientType);
            userVerification.setClientId(clientId);
            userVerification.setSession(sessionList);
            userVerification.setServiceUser(sessionList.getLastServiceUser());
            userVerification.setStatus(UserVerification.STATUS_NOT_VERIFIED);
            userVerification.setFailureTimes(0);
            userVerification.setCreateTime(now);
            userVerification.setUpdateTime(now);
            userVerificationRepo.save(userVerification);

            String verifyUrl = appPropertyConfig.getVerifyUrl() + "?token=" + token + "&clientType=" + clientType;
            String message = "请点击以下链接进行身份验证<br><a href=\"" + verifyUrl + "\">身份验证</a>";
            if(wechatMessageService.isWechat(clientId, botId)) {
                wechatMessageService.sendWechatTextMsg(clientId, botId, message);
            } else if(wechatCpService.isWorkWechat(clientId, botId)) {
                wechatCpService.sendWechatTextMsg(clientId, botId, message);
            }
            com.alibaba.fastjson.JSONObject manualInfo = new com.alibaba.fastjson.JSONObject()
                    .fluentPut("sessionKey", sessionKey)
                    .fluentPut("verifyToken", token);
            ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(clientId, clientType,
                    "cs", "system", "text", null, null,
                    sessionList.getOrgId(), botId, sessionList.getBusinessId(), sessionList.getGcid(),
                    message, null, null, manualInfo, true);
            SocketPacketModel socketPacketModel = new SocketPacketModel(sessionKey, null, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory));
            modelList.add(socketPacketModel);
            if(callback != null) {
                JSONObject callbackInfo = new JSONObject().put("ret", 0).put("msg", "身份验证下发成功").put("timestamp", System.currentTimeMillis());
                callback.sendAcknowledgement(callbackInfo);
            }
        }
        return modelList;
    }

    private String isRequestValid(String sessionKey, SessionListEntity sessionList) {
        String info = "";
        if (null == sessionList) {
            info = "会话不存在";
            LOGGER.info("身份验证，会话不存在, sessionKey:{}", sessionKey);
            return info;
        }
        if (sessionList.getStatus() == SessionListEntity.STATUS_OFFLINE) {
            info = "会话已结束";
            LOGGER.info("身份验证，会话已结束, sessionKey:{}", sessionKey);
            return info;
        }

        UserVerification userVerification = userVerificationRepo.findFirstBySessionIdOrderByCreateTimeDesc(sessionList.getId());
        //允许多次验证
        if(userVerification != null) {
            Integer status = userVerification.getStatus();
            int failTimes = userVerification.getFailureTimes();
            if(status == UserVerification.STATUS_NOT_VERIFIED || (status == UserVerification.STATUS_VERIFY_FAILED && failTimes < 3)) {
                info = "请勿重复下发身份验证";
                LOGGER.info("身份验证，已发验证，不允许重复发送, sessionKey={}, verifyId={}, failTimes={}", sessionKey, userVerification.getId(), failTimes);
                return info;
            }
        }
        return info;
    }
}
