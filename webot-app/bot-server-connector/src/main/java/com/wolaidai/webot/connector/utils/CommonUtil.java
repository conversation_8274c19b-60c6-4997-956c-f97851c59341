package com.wolaidai.webot.connector.utils;

import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtil {

    public static void processUserSensitiveInfo(ChatHistoryEntity chatHistory) {
        if("text".equals(chatHistory.getType()) && "user".equals(chatHistory.getSender())) {
            String content = chatHistory.getContent();
            Date todayStart = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            Boolean hisFlag = false;
            if(chatHistory.getDate().before(todayStart)) {
                hisFlag = true;
            }
            content = processUserSensitiveInfo(content, hisFlag);
            chatHistory.setContent(content);
        }
    }

    public static String processUserSensitiveInfo(String content, Boolean hisFlag) {
        if (StringUtils.isNotBlank(content) && content.length() > 10) {
            if(content.length()>17 && hisFlag) {
                content = CommonUtil.maskIdNumbers(content);
            }
            if(content.length()>15) {
                content = CommonUtil.maskCardNumbers(content);
            }
            if(hisFlag) {
                content = CommonUtil.maskPhoneNumbers(content);
            }
        }
        return content;
    }

    public static String maskPhoneNumbers(String info) {
        String regex = "(?<!\\d)(1[3-9]\\d{9})(?!\\d)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(info);
        StringBuffer sb = new StringBuffer();

        while (matcher.find() && matcher.group().length()==11) {
            String matchedNumber = matcher.group();
            String maskedNumber = matchedNumber.substring(0, 4) + "*****" + matchedNumber.substring(9);
            matcher.appendReplacement(sb, maskedNumber);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static String maskCardNumbers(String info) {
        // 银行卡号（16位或19位）
        String regex = "(?<!\\d)([1-9])(\\d{3})(\\d{8}||\\d{11})(\\d{4})(?!\\d)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(info);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String matchedNumber = matcher.group();
            String mask = "***********";
            if(matchedNumber.length()==16) {
                mask = "********";
            }

            String maskedNumber = matcher.group(1) + matcher.group(2) + mask + matcher.group(4);
            matcher.appendReplacement(sb, maskedNumber);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static String maskIdNumbers(String info) {
        String regex = "(?<!\\d)[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}([0-9Xx])(?!\\d)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(info);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String matchedNumber = matcher.group();
            String maskedNumber = matchedNumber.substring(0, 6) + "********" + matchedNumber.substring(14);
            matcher.appendReplacement(sb, maskedNumber);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static String removeHtmlTagsAndNewlines(String input) {
        // Step 1: 去除 HTML 标签
        String withoutHtml = input.replaceAll("<.*?>", "");

        // Step 2: 去除换行符
        String cleaned = withoutHtml.replaceAll("\\r|\\n", "");

        return cleaned;
    }
}
