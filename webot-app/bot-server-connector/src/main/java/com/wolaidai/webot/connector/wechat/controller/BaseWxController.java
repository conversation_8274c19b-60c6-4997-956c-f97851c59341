package com.wolaidai.webot.connector.wechat.controller;

import com.wolaidai.webot.connector.controller.BaseController;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.open.api.WxOpenService;
import me.chanjar.weixin.open.api.impl.WxOpenMessageRouter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;

public abstract class BaseWxController extends BaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired(required = false)
    protected WxOpenService wxOpenService;
    @Autowired(required = false)
    protected WxMpService wxMpService;
    @Autowired(required = false)
    protected WxOpenMessageRouter wxOpenMessageRouter;
    @Autowired(required = false)
    protected WxMpMessageRouter wxMpMessageRouter;
    @Autowired
    protected HttpServletRequest request;

    protected WxMpService getWxMpServiceByAppid(String appid){
        return wxMpService.switchoverTo(appid);

    }
    protected WxMpService getWxOpenMpServiceByAppid(String appid){
        return wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appid);
    }
}
