package com.wolaidai.webot.connector.service.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.model.ConversationContextModel;
import com.wolaidai.webot.connector.service.BaseService;
import com.wolaidai.webot.data.mongodb.entity.ConversationEntity;
import com.wolaidai.webot.data.mongodb.repo.ComplexConversationRepo;
import com.wolaidai.webot.data.mongodb.repo.ConversationRepo;
import com.wolaidai.webot.data.mysql.entity.bot.BotEntity;
import com.wolaidai.webot.data.mysql.entity.bot.BotSkillGroupEntity;
import com.wolaidai.webot.data.mysql.entity.bot.SkillGroupEntity;
import com.wolaidai.webot.data.mysql.repo.BotRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@AllArgsConstructor
public class ConversationService extends BaseService {
    private final RedisTemplate csStringRedisTemplate;

    private final RedissonClient redissonClient;

    private final ConversationRepo conversationRepo;

    private final ChatHistoryService chatHistoryService;

    private final BotRepo botRepo;

    private final ComplexConversationRepo complexConversationRepo;
    
    private final ApiService apiService;

    private final KeyExpireService keyExpireService;
    
    private final AppPropertyConfig appPropertyConfig;

    public long incrementKey(String clientId,Integer botId,String key){
        RLock lock = redissonClient.getLock(String.format(RedisKey.LOCK_KEY, "conversation:" + clientId));
        try {
            boolean res = lock.tryLock(0, 3, TimeUnit.SECONDS);
            if (res) {
                String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, botId);
                if(csStringRedisTemplate.hasKey(conversationKey)){
                    HashOperations<String, String, Object> hashOperations = csStringRedisTemplate.opsForHash();
                    return hashOperations.increment(conversationKey,key,1);
                }
            }
        }catch (InterruptedException e) {
            LOGGER.error("lock error",e);
        } finally {
            if(lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
        return -1;
    }

    public void endBotConversation(String cid){
        complexConversationRepo.updateStatusByCid(cid, ConversationEntity.END_STATUS);
        csStringRedisTemplate.delete(String.format(RedisKey.BOT_CONVERSATION_KEY, cid));
        keyExpireService.removeExpireKey(String.format(RedisKey.BOT_CONVERSATION_KEY, cid));
    }

    public void updateOpFlagByCid(String cid){
        complexConversationRepo.updateOpFlagByCid(cid);
    }

    public void updateConversationStatus(String clientId,Integer botId,String cid,Integer currentStatus){
        String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, botId);
        if(Objects.equals(currentStatus,ConversationEntity.INIT_STATUS)) {
            complexConversationRepo.updateStatusByCid(cid, ConversationEntity.START_STATUS);
            RLock lock = redissonClient.getLock(String.format(RedisKey.LOCK_KEY, "conversation:" + clientId));
            try {
                boolean res = lock.tryLock(0, 3, TimeUnit.SECONDS);
                if (res) {
                    if(csStringRedisTemplate.hasKey(conversationKey)){
                        HashOperations<String, String, Object> hashOperations = csStringRedisTemplate.opsForHash();
                        hashOperations.put(conversationKey,"status",String.valueOf(ConversationEntity.START_STATUS));
                    }
                }
            }catch (InterruptedException e) {
                LOGGER.error("lock error",e);
            } finally {
                if(lock.isHeldByCurrentThread()){
                    lock.unlock();
                }
            }
        }
        keyExpireService.setExpireKey(String.format(RedisKey.BOT_CONVERSATION_KEY,cid),10,TimeUnit.MINUTES);
    }

    public ConversationContextModel.ContextInfo getConversationContextInfo(String clientId, Integer orgId){
        String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, orgId);
        HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
        Map<String, String> entries = hashOperations.entries(conversationKey);
        if(CollectionUtils.isEmpty(entries)){
            return JSON.toJavaObject(new JSONObject().fluentPutAll(entries), ConversationContextModel.ContextInfo.class);
        }
        return null;
    }
    public ConversationContextModel getConversation(String socketId, String clientId, Integer clientType, String account, String accessKey, String skillGroupId, boolean needBotConversation){
        return getConversation(socketId,clientId,clientType,account,accessKey,skillGroupId,needBotConversation,false);
    }

    public ConversationContextModel getConversation(String socketId, String clientId, Integer clientType, String account, String accessKey, String skillGroupId, boolean needBotConversation, boolean reconnect){
        RLock lock = redissonClient.getLock(String.format(RedisKey.LOCK_KEY, "conversation:" + clientId));
        try{
            boolean res = lock.tryLock(1,3, TimeUnit.SECONDS);
            if(res) {
                BotEntity bot = getBotEntity(accessKey);
                if (bot == null) return null;
                String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, bot.getId());
                HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                Map<String, String> entries = hashOperations.entries(conversationKey);
                ConversationContextModel conversationContextModel = null;
                if (CollectionUtils.isEmpty(entries)) {
                    SkillGroupEntity skillGroup = getSkillGroupEntity(bot,skillGroupId);
                    if(skillGroup == null) return  null;
                    String gcid = UUID.randomUUID().toString();
                    Date now = new Date();
                    if(needBotConversation&&!reconnect) {
                        ConversationEntity conversation = saveConversation(clientId, clientType, account, bot.getOrgId(), skillGroup.getId(), bot.getId(), gcid, now);
                        entries.put("cid", conversation.getCid());
                        entries.put("status", String.valueOf(conversation.getStatus()));
                    }
                    entries.put("gcid", gcid);
                    entries.put("gcTime", String.valueOf(now.getTime()));
                    entries.put("socketId",socketId);
                    entries.put("account", account);
                    entries.put("clientType", String.valueOf(clientType));
                    entries.put("botId", String.valueOf(bot.getId()));
                    entries.put("orgId", String.valueOf(bot.getOrgId()));
                    entries.put("accessKey", accessKey);
                    entries.put("skillGroupId", String.valueOf(skillGroupId));
                    entries.put("scene", "bot");
                    hashOperations.putAll(conversationKey, entries);
                    csStringRedisTemplate.expire(conversationKey, 1, TimeUnit.HOURS);
                    ConversationContextModel.ContextInfo contextInfo = JSON.toJavaObject(new JSONObject().fluentPutAll(entries), ConversationContextModel.ContextInfo.class);
                    if(!reconnect) {
                        createSwitchSkillGroupHistory(clientId, clientType, "bot", "user", "event", "biz:switch", contextInfo.getAccount(), contextInfo.getOrgId(), contextInfo.getBotId(), contextInfo.getSkillGroupId(), contextInfo.getGcid(), contextInfo.getCid(), skillGroup.getName());
                    }
                    conversationContextModel = new ConversationContextModel(true,contextInfo);
                }else{
                    ConversationContextModel.ContextInfo contextInfo = JSON.toJavaObject(new JSONObject().fluentPutAll(entries), ConversationContextModel.ContextInfo.class);
                    if(!Objects.equals(Objects.toString(entries.get("skillGroupId")),skillGroupId)){
                        SkillGroupEntity skillGroup = getSkillGroupEntity(bot,skillGroupId);
                        if(skillGroup == null) return  null;
                        if(contextInfo.getCid()!=null) {
                            complexConversationRepo.updateSkillGroupIdByCid(contextInfo.getCid(), Integer.parseInt(skillGroupId));
                        }
                        contextInfo.setSkillGroupId(skillGroup.getId());
                        hashOperations.put(conversationKey,"skillGroupId",String.valueOf(skillGroup.getId()));
                        createSwitchSkillGroupHistory(clientId, clientType, "bot","user","event","biz:switch",contextInfo.getAccount(), contextInfo.getOrgId(), contextInfo.getBotId(), contextInfo.getSkillGroupId(), contextInfo.getGcid(), contextInfo.getCid(), skillGroup.getName());
                    }
                    hashOperations.put(conversationKey, "socketId",socketId);
                    if(!reconnect) {
                        csStringRedisTemplate.expire(conversationKey, 1, TimeUnit.HOURS);
                    }
                    contextInfo.setSocketId(socketId);
                    conversationContextModel = new ConversationContextModel(false,contextInfo);
                }
                if(conversationContextModel.getInfo().getCid()==null&&needBotConversation&&!reconnect&&"bot".equals(conversationContextModel.getInfo().getScene())){
                    beginConversation(clientId,conversationContextModel.getInfo());
                }
                return conversationContextModel;
            }
        }catch (InterruptedException e) {
            LOGGER.error("lock error",e);
        } finally {
            if(lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
        return null;
    }

    private ConversationEntity saveConversation(String clientId, Integer clientType, String account, Integer orgId, Integer skillGroupId, Integer botId, String gcid, Date now) {
        ConversationEntity conversationEntity = new ConversationEntity();
        conversationEntity.setAccount(account);
        conversationEntity.setOrgId(orgId);
        conversationEntity.setBotId(botId);
        conversationEntity.setClientId(clientId);
        conversationEntity.setClientType(clientType);
        conversationEntity.setCid(UUID.randomUUID().toString());
        conversationEntity.setGcid(gcid);
        conversationEntity.setStartTime(now);
        conversationEntity.setSkillGroupId(Integer.valueOf(skillGroupId));
        conversationEntity.setSkillGroupIds(Arrays.asList(conversationEntity.getSkillGroupId()));
        conversationEntity.setOpFlag(0);
        conversationEntity.setStatus(ConversationEntity.INIT_STATUS);
        conversationRepo.save(conversationEntity);
        keyExpireService.setExpireKey(String.format(RedisKey.BOT_CONVERSATION_KEY, conversationEntity.getCid()), 10, TimeUnit.MINUTES);
        return conversationEntity;
    }

    private void createSwitchSkillGroupHistory(String clientId, Integer clientType, String scene, String sender, String type, String eventKey, String account, Integer orgId, Integer botId, Integer skillGroupId, String gcid, String cid, String content){
        Map<String,Object> extend = new HashMap<>();
        extend.put("skillGroupName",content);
        chatHistoryService.createChatHistory(null,clientId,clientType,scene,sender,type,eventKey,account,orgId,botId,skillGroupId,gcid,cid,content,extend,true);
    }

    public BotEntity getBotEntity(String accessKey) {
        BotEntity bot = botRepo.findByCode(accessKey);
        if (bot == null|| !Objects.equals(BotEntity.ACTIVE_STATUS,bot.getStatus())) {
            LOGGER.error("accessKey:{} not exist", accessKey);
            return null;
        }
        return bot;
    }

    private SkillGroupEntity getSkillGroupEntity(BotEntity bot,String skillGroupId){
        Optional<BotSkillGroupEntity> optional = bot.getBotSkillGroups().stream().filter(v -> Objects.equals(Objects.toString(v.getSkillGroup().getId()), skillGroupId)).findAny();
        if (!optional.isPresent()) {
            LOGGER.error("skillGroupId:{} not exist", skillGroupId);
            return null;
        }
        return optional.get().getSkillGroup();
    }

    public ConversationContextModel getWechatConversation(String clientId, Integer clientType, String appId, String accessKey, String skillGroupId){
        RLock lock = redissonClient.getLock(String.format(RedisKey.LOCK_KEY, "conversation:" + clientId));
        try{
            boolean res = lock.tryLock(1,3, TimeUnit.SECONDS);
            if(res) {
                BotEntity bot = getBotEntity(accessKey);
                if (bot == null) return null;
                String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, bot.getId());
                HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                Map<String, String> entries = hashOperations.entries(conversationKey);
                ConversationContextModel conversationContextModel = null;
                if (CollectionUtils.isEmpty(entries)) {
                    SkillGroupEntity skillGroup = null;
                    if(skillGroupId != null) {
                        skillGroup = getSkillGroupEntity(bot, skillGroupId);
                        if (skillGroup == null) return null;
                    }
                    String gcid = UUID.randomUUID().toString();
                    Date now = new Date();
                    entries.put("gcid", gcid);
                    entries.put("gcTime", String.valueOf(now.getTime()));
                    entries.put("clientType", String.valueOf(clientType));
                    entries.put("botId", String.valueOf(bot.getId()));
                    entries.put("orgId", String.valueOf(bot.getOrgId()));
                    String account = null;
                    if (StringUtils.isNotBlank(appId)) {
                        if (appPropertyConfig.isEnvTest()){
                            account = appPropertyConfig.getTestUserMobile();
                        } else {
                            account = apiService.getPublicAccountMobile(appId, clientId);
                        }
                        entries.put("account", account);
                        
                    }
                    if(skillGroupId!=null){
                        ConversationEntity conversation = saveConversation(clientId, clientType, account, bot.getOrgId(),skillGroup.getId(), bot.getId(), gcid, now);
                        entries.put("cid", conversation.getCid());
                        entries.put("status", String.valueOf(conversation.getStatus()));
                        entries.put("skillGroupId", skillGroupId);
                    }
                    entries.put("accessKey", accessKey);
                    entries.put("scene", "bot");
                    hashOperations.putAll(conversationKey, entries);
                    csStringRedisTemplate.expire(conversationKey, 1, TimeUnit.HOURS);
                    ConversationContextModel.ContextInfo contextInfo = JSON.toJavaObject(new JSONObject().fluentPutAll(entries), ConversationContextModel.ContextInfo.class);
                    if(skillGroup!=null) {
                        createSwitchSkillGroupHistory(clientId, clientType, "bot", "user", "event", "biz:switch", contextInfo.getAccount(), contextInfo.getOrgId(), contextInfo.getBotId(), contextInfo.getSkillGroupId(), contextInfo.getGcid(), contextInfo.getCid(), skillGroup.getName());
                    }
                    conversationContextModel = new ConversationContextModel(true,contextInfo);
                }else{
                    ConversationContextModel.ContextInfo contextInfo = JSON.toJavaObject(new JSONObject().fluentPutAll(entries), ConversationContextModel.ContextInfo.class);
                    if(skillGroupId!=null&&!Objects.equals(Objects.toString(contextInfo.getSkillGroupId()),skillGroupId)){
                        SkillGroupEntity skillGroup = getSkillGroupEntity(bot,skillGroupId);
                        if(skillGroup == null) return  null;
                        if(contextInfo.getCid()!=null) {
                            complexConversationRepo.updateSkillGroupIdByCid(contextInfo.getCid(), Integer.parseInt(skillGroupId));
                        }
                        contextInfo.setSkillGroupId(skillGroup.getId());
                        hashOperations.put(conversationKey,"skillGroupId",String.valueOf(skillGroup.getId()));
                        if(contextInfo.getCid()==null&&"bot".equals(contextInfo.getScene())) {
                            beginConversation(clientId, contextInfo);
                        }
                        createSwitchSkillGroupHistory(clientId, clientType, "bot","user","event","biz:switch",contextInfo.getAccount(), contextInfo.getOrgId(), contextInfo.getBotId(), contextInfo.getSkillGroupId(), contextInfo.getGcid(), contextInfo.getCid(), skillGroup.getName());
                    }
                    conversationContextModel = new ConversationContextModel(false,contextInfo);
                }
                csStringRedisTemplate.expire(conversationKey, 1, TimeUnit.HOURS);
                if(StringUtils.isNotBlank(appId)){
                    if(clientType==2) {
                        String key = String.format(RedisKey.WECHAT_CLIENTID_BOTID_APPID, clientId, bot.getId());
                        csStringRedisTemplate.opsForValue().set(key, appId);
                        csStringRedisTemplate.expireAt(key, DateUtils.addDays(DateUtils.truncate(new Date(), Calendar.DATE), 1));
                    }else if(clientType==3){
                        String key = String.format(RedisKey.WECHAT_CLIENTID_BOTID_AGENTID, clientId, bot.getId());
                        csStringRedisTemplate.opsForValue().set(key, appId);
                        csStringRedisTemplate.expireAt(key, DateUtils.addDays(DateUtils.truncate(new Date(), Calendar.DATE), 1));
                    }
                }
                return conversationContextModel;
            }
        }catch (InterruptedException e) {
            LOGGER.error("lock error",e);
        } finally {
            if(lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
        return null;
    }

    private void beginConversation(String clientId, ConversationContextModel.ContextInfo contextInfo) {
        ConversationEntity conversation = saveConversation(clientId, contextInfo.getClientType(), contextInfo.getAccount(), contextInfo.getOrgId(), contextInfo.getSkillGroupId(), contextInfo.getBotId(), contextInfo.getGcid(), new Date());
        Map<String, String> entries = new HashMap<>();
        entries.put("cid", conversation.getCid());
        entries.put("status", String.valueOf(conversation.getStatus()));
        csStringRedisTemplate.opsForHash().putAll(String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, contextInfo.getBotId()), entries);
        contextInfo.setCid(conversation.getCid());
        contextInfo.setStatus(conversation.getStatus());
    }
}
