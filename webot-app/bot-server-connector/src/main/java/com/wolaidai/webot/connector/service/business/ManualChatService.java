package com.wolaidai.webot.connector.service.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.constant.*;
import com.wolaidai.webot.connector.entity.Event;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.utils.CommonUtil;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.connector.wechat.service.WechatCpService;
import com.wolaidai.webot.connector.wechat.service.WechatMessageService;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.repo.ChatHistoryRepo;
import com.wolaidai.webot.data.mysql.entity.bot.BotEntity;
import com.wolaidai.webot.data.mysql.entity.chat.*;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.entity.config.QaCommonConfigEntity;
import com.wolaidai.webot.data.mysql.repo.*;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class ManualChatService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final RedissonClient redissonClient;

    private final StringRedisTemplate csStringRedisTemplate;

    private final RedisTemplate csRedisTemplate;

    private final ChatHistoryService chatHistoryService;

    private final SessionListRepo sessionListRepo;
    private final SatisfactionDataRepo satisfactionDataRepo;

    private final BotRepo botRepo;

    private final QueueListRepo queueListRepo;
    private final WechatMessageService wechatMessageService;
    private final WechatCpService wechatCpService;
    private final UserStateRepo userStateRepo;
    private final KeyExpireService keyExpireService;

    private final ConfigService configService;
    private final AppPropertyConfig appPropertyConfig;

    private final ChatHistoryRepo chatHistoryRepo;
    private final ServiceSummaryRepo serviceSummaryRepo;
    private final CommonConfigRepo commonConfigRepo;
    private final UserRepo userRepo;
    private final QualityInspectionService qualityInspectionService;

    public boolean satisfactionData(SessionListEntity sessionList, int type){
        SatisfactionDataEntity satisfactionData = sessionList.getSatisfactionData();
        if (satisfactionData == null) {
            satisfactionData = new SatisfactionDataEntity();
            satisfactionData.setClientId(sessionList.getClientId());
            satisfactionData.setClientTypeId(sessionList.getClientTypeId());
            satisfactionData.setOrgId(sessionList.getOrgId());
            satisfactionData.setType(type);
            satisfactionData.setServiceUser(sessionList.getLastServiceUser());
            satisfactionData.setOrigin(sessionList.getOrigin());
            satisfactionData.setSession(sessionList);
            satisfactionData.setStatus(SatisfactionDataEntity.STATUS_UNTREATED);
            satisfactionData.setCreateTime(new Date());
            satisfactionData.setUpdateTime(satisfactionData.getCreateTime());
            satisfactionDataRepo.save(satisfactionData);
            sessionList.setSatisfactionData(satisfactionData);
            sessionListRepo.updateSatisfactionLevelById(sessionList.getId(), -1);
            return false;
        }
        return true;
    }

    public JSONObject getAutoReplyConfig(int orgId, int clientType, String configType) {
        String config = configService.read(orgId, String.format(RedisKey.AUTOREPLY_CONFIG, orgId, clientType), configType, CommonConfigEntity.TYPE_AUTOREPLY_CONFIG, String.valueOf(clientType), configType);
        if (StringUtils.isBlank(config)) {
            return null;
        }
        return new JSONObject(config);
    }

    public int getRespTimeoutConfig(int orgId,String configType) {
        String config = configService.read(orgId, String.format(RedisKey.RESP_TIMEOUT_CONFIG, orgId), configType, CommonConfigEntity.TYPE_RESP_TIMEOUT_CONFIG, configType);
        if (StringUtils.isNotBlank(config)) {
            return NumberUtils.toInt(config);
        }
        return 0;
    }

    public JSONObject getSatisfactionConfig(Integer orgId) {
        String satisfactionConfig = configService.read(orgId, String.format(RedisKey.SATISFACTION_CONFIG, orgId), null, CommonConfigEntity.TYPE_SATISFACTION_CONFIG);
        if (StringUtils.isNotBlank(satisfactionConfig)) {
            return new JSONObject(satisfactionConfig);
        }
        return null;
    }

    public JSONObject getCsWorkTimeConfig(Integer orgId) {
        String config = configService.read(orgId, String.format(RedisKey.CS_WORKTIME_CONFIG, orgId), null, CommonConfigEntity.TYPE_WORKTIME_CONFIG);
        if (StringUtils.isNotBlank(config)) {
            return new JSONObject(config);
        }
        return null;
    }

    public JSONObject getBotWorkTimeConfig(Integer orgId) {
        String config = configService.read(orgId, String.format(RedisKey.BOT_WORKTIME_CONFIG, orgId), null, CommonConfigEntity.TYPE_BOT_WORKTIME_CONFIG);
        if (StringUtils.isNotBlank(config)) {
            return new JSONObject(config);
        }
        return null;
    }

    public JSONObject getBotTurnArtificialPolicy(Integer orgId) {
        String config = configService.read(orgId, String.format(RedisKey.BOT_TURNARTIFICIAL_POLICY, orgId), null, QaCommonConfigEntity.TYPE_TURN_ARTIFICIAL_POLICY_CONFIG);
        if (StringUtils.isNotBlank(config)) {
            return new JSONObject(config);
        }
        return null;
    }

    public JSONObject getBotMenuListConfig(Integer orgId, String accessKey) {
        String config = configService.read(orgId, String.format(RedisKey.BOT_MENU_LIST_CONFIG, accessKey), null, QaCommonConfigEntity.TYPE_BOT_MENU_LIST_CONFIG, accessKey);
        if (StringUtils.isBlank(config)) {
            config = configService.read(orgId, String.format(RedisKey.BOT_MENU_LIST_CONFIG, accessKey), null, QaCommonConfigEntity.TYPE_BOT_MENU_LIST_CONFIG, "-1");
        }
        if (StringUtils.isNotBlank(config)) {
            return new JSONObject(config);
        }
        return null;
    }

    public long onlineCount(int orgId) {
        return csStringRedisTemplate.opsForZSet().size(String.format(RedisKey.CS_USER_ONLINE_LIST, orgId));
    }

    public long queueCount(int orgId){
        return queueCount(orgId,0)+queueCount(orgId,1);
    }

    public long queueCount(int orgId,int customerType){
        String queueKey = String.format(RedisKey.QUEUE_NORMAL, orgId);
        if (customerType == 1) {
            queueKey = String.format(RedisKey.QUEUE_VIP, orgId);
        }
        return csStringRedisTemplate.opsForZSet().size(queueKey);
    }

    public void notifyQueueStatus(int orgId, int queueId, int customerType, boolean notifyAll) {
        String queueKey = String.format(RedisKey.QUEUE_NORMAL, orgId);
        if (customerType == 1) {
            queueKey = String.format(RedisKey.QUEUE_VIP, orgId);
        }
        Set<String> queueDataSet = csStringRedisTemplate.opsForZSet().range(queueKey, 0, -1);
        for (String queueData : queueDataSet) {
            JSONObject queueDataJson = new JSONObject(queueData);
            HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
            String clientId = queueDataJson.getString("clientId");
            int botId = queueDataJson.getInt("botId");
            int queueIdInDb = queueDataJson.getInt("queueId");
            if (notifyAll || queueId == queueIdInDb) {
                Long rank = csStringRedisTemplate.opsForZSet().rank(queueKey, queueData);
                JSONObject customerQueueMsg = getAutoReplyConfig(orgId, queueDataJson.getInt("clientType"), ManualConfigConstants.AUTO_REPLY_CUSTOMER_QUEUE_MSG);
                String content = null;
                JSONObject exceedLimitConfig = null;
                if (customerQueueMsg != null && customerQueueMsg.getBoolean("status")) {
                    content = customerQueueMsg.getString("content");
                    if (customerQueueMsg.getBoolean("exceedLimitFlag")) {
                        exceedLimitConfig = customerQueueMsg.getJSONObject("exceedLimitConfig");
                    }
                }
                if (StringUtils.isBlank(content)) {
                    content = "即将排到您，请耐心等待";
                }
                if (rank != null) {
                    boolean isWechat = wechatMessageService.isWechat(clientId, botId);
                    boolean isWorkWechat = wechatCpService.isWorkWechat(clientId, botId);
                    if (exceedLimitConfig != null&&rank>=exceedLimitConfig.getInt("exceed")) {
                        String replyContent = exceedLimitConfig.getString("content").replace("#人数#", String.valueOf(rank)).replace("#时长#", String.valueOf((rank + 1) * exceedLimitConfig.getInt("waitSecond")));
                        if(isWechat){
                            if(queueId == queueIdInDb) {
                                wechatMessageService.sendWechatTextMsg(clientId, botId, replyContent);
                            }
                        }else if(isWorkWechat){
                            if(queueId == queueIdInDb) {
                                wechatCpService.sendWechatTextMsg(clientId, botId, replyContent);
                            }
                        }else {
                            String socketId = hashOperations.get(String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, botId), "socketId");
                            if (socketId != null) {
                                csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), Collections.singletonList(new SocketPacketModel(null, socketId, SocketEvent.EVENT,
                                        new JSONObject().put("type", "queue:notify").put("content", replyContent).toString())));
                            }
                        }
                    }else{
                        String replyContent = content.replace("#人数#", String.valueOf(rank));
                        if(isWechat){
                            if(queueId == queueIdInDb) {
                                wechatMessageService.sendWechatTextMsg(clientId, botId, replyContent);
                            }
                        }else if(isWorkWechat){
                            if(queueId == queueIdInDb) {
                                wechatCpService.sendWechatTextMsg(clientId, botId, replyContent);
                            }
                        }else {
                            String socketId = hashOperations.get(String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, botId), "socketId");
                            if (socketId != null) {
                                csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), Collections.singletonList(new SocketPacketModel(null, socketId, SocketEvent.EVENT,
                                        new JSONObject().put("type", "queue:notify").put("content", replyContent).toString())));
                            }
                        }
                    }
                }
                if (!notifyAll) {
                    break;
                }
            }
        }
    }

    public void offlineSession(String sessionKey, int closeType) {
        String redisTaskKey = String.format(RedisKey.LOCK_KEY, "offlineSession");
        RLock lock = redissonClient.getLock(redisTaskKey);
        try {
            boolean res = lock.tryLock(0, 10, TimeUnit.SECONDS);
            if (!res) {
                return;
            }
            SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
            if (sessionList == null) {
                LOGGER.error("sessionKey not exists, sessionKey:{}", sessionKey);
                return;
            }
            Map<String, Object> manual = new HashMap<>();
            manual.put("sessionKey", sessionKey);
            if (Objects.equals(sessionList.getStatus(), SessionListEntity.STATUS_OFFLINE)) {
                LOGGER.error("session is closed, sessionKey:{}", sessionKey);
                csStringRedisTemplate.delete(String.format(RedisKey.GLOBAL_CONVERSATION_KEY, sessionList.getClientId(), sessionList.getBotId()));
                csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), Collections.singletonList(new SocketPacketModel(sessionKey, null, SocketEvent.EVENT, new JSONObject().put("type", "session:end").put("manual", manual).toString())));
                return;
            }
            Date customerFirstReplyTime = sessionList.getCustomerFirstReplyTime();
            if(customerFirstReplyTime!=null){
                long replyTime = System.currentTimeMillis() - customerFirstReplyTime.getTime();
                HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                String csRespDetailKey = String.format(RedisKey.CS_RESP_DETAIL, sessionList.getSessionKey());
                Long totalReplyTime = hashOperations.increment(csRespDetailKey, "totalReplyTime", replyTime);
                Long round = hashOperations.increment(csRespDetailKey, "round", 1);
                int avgRespTime = BigDecimal.valueOf(totalReplyTime).divide(BigDecimal.valueOf(round), 0, RoundingMode.HALF_UP).intValue();
                sessionList.setAvgRespTime(avgRespTime);
                int avgRespSecond = getRespTimeoutConfig(sessionList.getOrgId(), ManualConfigConstants.AVG_RESP);
                if(avgRespSecond>0){
                    if(avgRespSecond<=avgRespTime/1000) {
                        sessionList.setAvgRespTimeout(1);
                    }else{
                        sessionList.setAvgRespTimeout(0);
                    }
                }
            }
            sessionListRepo.updateById(sessionList.getId(), sessionList.getAvgRespTime(), sessionList.getAvgRespTimeout(), SessionListEntity.STATUS_OFFLINE, closeType, (System.currentTimeMillis() - sessionList.getCreateTime().getTime()) / 1000);

            csStringRedisTemplate.opsForList().rightPush(RedisKey.EVENT_CLIENT_KEY, JSON.toJSONString(new Event(UUID.randomUUID().toString(), Event.END_SESSION_KEY, sessionList.getOrgId(),
                    JSON.parseObject(new JSONObject().put("sessionKey", sessionKey).toString()), new Date())));
            HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
            Map<String, String> entries = hashOperations.entries(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, sessionList.getOrgId(), sessionList.getLastServiceUser()));
            List<SocketPacketModel> packetList = new ArrayList<>();
            if (closeType == SessionListEntity.CLOSE_TYPE_MANUAL) {
                JSONObject serviceTimeoutOfflineMsg = getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_CLOSE_SESSION_MSG);
                if (serviceTimeoutOfflineMsg != null && serviceTimeoutOfflineMsg.getBoolean("status")) {
                    String nickName = entries.get("nickName");
                    if(nickName==null){
                        UserStateEntity userStateEntity = userStateRepo.findByOrgIdAndEmail(sessionList.getOrgId(), sessionList.getLastServiceUser());
                        if(userStateEntity!=null){
                            nickName = userStateEntity.getNickName();
                        }
                    }
                    String content = serviceTimeoutOfflineMsg.getString("content").replace("#客服#", Objects.toString(nickName,"客服"));
                    if(wechatMessageService.isWechat(sessionList.getClientId(), sessionList.getBotId())){
                        wechatMessageService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),content);
                    }else if(wechatCpService.isWorkWechat(sessionList.getClientId(), sessionList.getBotId())){
                        wechatCpService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),content);
                    }
                    packetList.add(sendOfflineSessionReply(sessionList, content));
                }
            } else if (closeType == SessionListEntity.CLOSE_TYPE_CUSTOMER_TIMEOUT) {
                JSONObject closeSessionMsg = getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_CUSTOMER_OFFLINE_MSG);
                if (closeSessionMsg != null && closeSessionMsg.getBoolean("status")) {
                    String content = closeSessionMsg.getString("content");
                    if(wechatMessageService.isWechat(sessionList.getClientId(), sessionList.getBotId())){
                        wechatMessageService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),content);
                    }else if(wechatCpService.isWorkWechat(sessionList.getClientId(), sessionList.getBotId())){
                        wechatCpService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),content);
                    }
                    packetList.add(sendOfflineSessionReply(sessionList, content));
                }
            } else if (closeType == SessionListEntity.CLOSE_TYPE_CS_TIMEOUT) {
                JSONObject closeSessionMsg = getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_SERVICE_TIMEOUT_OFFLINE_MSG);
                if (closeSessionMsg != null && closeSessionMsg.getBoolean("status")) {
                    String content = closeSessionMsg.getString("content");
                    if(wechatMessageService.isWechat(sessionList.getClientId(), sessionList.getBotId())){
                        wechatMessageService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),content);
                    }else if(wechatCpService.isWorkWechat(sessionList.getClientId(), sessionList.getBotId())){
                        wechatCpService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),content);
                    }
                    packetList.add(sendOfflineSessionReply(sessionList, content));
                }
            } else if (closeType == SessionListEntity.CLOSE_TYPE_CUSTOMER) {
                String content = "用户结束了本次会话";
                if(wechatMessageService.isWechat(sessionList.getClientId(), sessionList.getBotId())){
                    wechatMessageService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),content);
                }else if(wechatCpService.isWorkWechat(sessionList.getClientId(), sessionList.getBotId())){
                    wechatCpService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),content);
                }
                packetList.add(sendOfflineSessionReply(sessionList, content));
            }
            packetList.add(new SocketPacketModel(sessionKey, null, SocketEvent.EVENT, new JSONObject().put("type", "session:end").put("manual", manual).toString()));
            sendSatisfaction(sessionList,SatisfactionDataEntity.TYPE_SYSTEM,packetList,entries.get("socketId"));
            keyExpireService.removeExpireKey(String.format(RedisKey.CS_FIRSTRESP_SESSION_KEY, sessionList.getSessionKey()),String.format(RedisKey.CS_SESSIONRESP_SESSION_KEY, sessionList.getSessionKey()));
            csStringRedisTemplate.delete(Arrays.asList(String.format(RedisKey.CS_RESP_DETAIL, sessionList.getSessionKey()),
                    String.format(RedisKey.GLOBAL_CONVERSATION_KEY, sessionList.getClientId(), sessionList.getBotId())));
            csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), packetList);
            //调用AI接口生成服务小结中的AI小结备注
            generateAISummary(sessionList);
            //异步提交质量检查
            submitQualityInspection(sessionList);
        }catch (Exception e){
            LOGGER.error("execute offlineSession fail",e);
        }finally {
            if(lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private SocketPacketModel sendOfflineSessionReply(SessionListEntity sessionList, String content) {
        Map<String, Object> manual = new HashMap<>();
        manual.put("sessionKey", sessionList.getSessionKey());
        if(sessionList.getCustomerFirstReplyTime()!=null) {
            manual.put("email",sessionList.getLastServiceUser());
            manual.put("responseTime", (int) ((System.currentTimeMillis() - sessionList.getCustomerFirstReplyTime().getTime()) / 1000));
        }
        String eventKey = "session:timeout";
        if(SessionListEntity.CLOSE_TYPE_MANUAL.equals(sessionList.getCloseType())
                || (SessionListEntity.CLOSE_TYPE_CUSTOMER.equals(sessionList.getCloseType()))) {
            eventKey = "session:close";
        }
        ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(sessionList.getClientId(), sessionList.getClientTypeId(), "cs", "system", "event", eventKey, null,
                sessionList.getOrgId(), sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(), content, null, null, manual, true);
        return new SocketPacketModel(sessionList.getSessionKey(), null, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory));
    }

    public void dequeue(String clientId,String accessKey){
        BotEntity bot = botRepo.findByCode(accessKey);
        if(bot!=null){
            QueueListEntity queueList = queueListRepo.findIdByInQueueStatusAndCreateTime(bot.getOrgId(), clientId, DateUtils.truncate(new Date(), Calendar.DATE));
            if(queueList!=null) {
                csStringRedisTemplate.opsForList().rightPush(RedisKey.EVENT_CLIENT_KEY, JSON.toJSONString(new Event(UUID.randomUUID().toString(), Event.DEQUEUE_KEY, bot.getOrgId(),
                        JSON.parseObject(new JSONObject().put("queueId", queueList.getId()).toString()), new Date())));
                csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT),
                        Collections.singletonList(new SocketPacketModel(String.format(SocketRoomConstants.GLOBAL_ORG_ROOM_NAME, bot.getOrgId()), null, SocketEvent.EVENT, new JSONObject().put("type", "queue:dequeue").put("clientId",clientId).toString())));
                HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, queueList.getClientId(), queueList.getBotId());
                if (csStringRedisTemplate.hasKey(conversationKey)) {
                    hashOperations.put(conversationKey, "scene", "bot");
                }
            }
        }
    }

    public void initUserMsgToSession(SessionListEntity sessionList,ChatHistoryEntity chatHistory){
        if (sessionList.getCustomerFirstReplyTime() == null) {
            sessionList.setCustomerFirstReplyTime(chatHistory.getDate());
        }
        sessionList.setCustomerLastReplyTime(chatHistory.getDate());
        HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
        String currentSessionKey = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, sessionList.getOrgId(), sessionList.getLastServiceUser()), "currentSessionKey");
        if (!sessionList.getSessionKey().equals(currentSessionKey)) {
            sessionList.setUnreadMsgCount(sessionList.getUnreadMsgCount() + 1);
        }
        sessionList.setLastMsg(MessageUtil.escapeHtml(chatHistory.getContent(), chatHistory.getType()));
        sessionList.setLastMsgTime(chatHistory.getDate());
        Integer lastMsgSender = sessionList.getLastMsgSender();
        sessionListRepo.updateId(sessionList.getId(), sessionList.getCustomerFirstReplyTime(), sessionList.getCustomerLastReplyTime(), sessionList.getUnreadMsgCount(), sessionList.getLastMsg(), sessionList.getLastMsgTime(), SessionListEntity.USER_SENDER);
        if (lastMsgSender == null || lastMsgSender == SessionListEntity.MANUAL_SENDER) {
            if(lastMsgSender==null) {
                int firstRespSecond = getRespTimeoutConfig(sessionList.getOrgId(), ManualConfigConstants.FIRST_RESP);
                if (firstRespSecond > 0) {
                    keyExpireService.setExpireKey(String.format(RedisKey.CS_FIRSTRESP_SESSION_KEY, sessionList.getSessionKey()), firstRespSecond, TimeUnit.SECONDS);
                }
            }
            int sessionRespSecond = getRespTimeoutConfig(sessionList.getOrgId(), ManualConfigConstants.SESSION_RESP);
            if(sessionRespSecond>0){
                keyExpireService.setExpireKey(String.format(RedisKey.CS_SESSIONRESP_SESSION_KEY, sessionList.getSessionKey()), sessionRespSecond, TimeUnit.SECONDS);
            }
            JSONObject serviceTimeoutMsg = getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_SERVICE_TIMEOUT_MSG);
            if (serviceTimeoutMsg != null && serviceTimeoutMsg.getBoolean("status")) {
                int timeoutMinutes = serviceTimeoutMsg.getInt("timeoutMinutes");
                if (timeoutMinutes > 0) {
                    keyExpireService.setExpireKey(String.format(RedisKey.CS_LAST_REPLY_SESSION_KEY, sessionList.getSessionKey()), timeoutMinutes, TimeUnit.MINUTES);
                }
            }
            JSONObject serviceTimeoutOfflineMsg = getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_SERVICE_TIMEOUT_OFFLINE_MSG);
            if (serviceTimeoutOfflineMsg != null && serviceTimeoutOfflineMsg.getBoolean("status")) {
                int timeoutMinutes = serviceTimeoutOfflineMsg.getInt("timeoutMinutes");
                if (timeoutMinutes > 0) {
                    keyExpireService.setExpireKey(String.format(RedisKey.CS_OFFLINE_SESSION_KEY, sessionList.getSessionKey()), timeoutMinutes, TimeUnit.MINUTES);
                }
            }
        }
    }

    public boolean sendSatisfaction(SessionListEntity sessionList,int type,List<SocketPacketModel> packetList,String socketId){
        boolean hasSatisfaction = satisfactionData(sessionList, type);
        if (!hasSatisfaction) {
            JSONObject satisfactionConfig = getSatisfactionConfig(sessionList.getOrgId());
            if (satisfactionConfig != null) {
                String message = "请您点击下方链接对我的服务进行评价，谢谢!\n\uD83D\uDC49" + String.format("<a href=\"%s\">点我进行评价</a>", appPropertyConfig.getSatisfactionUrl() + "?sessionKey=" + sessionList.getSessionKey());
                if (wechatMessageService.isWechat(sessionList.getClientId(), sessionList.getBotId())) {
                    wechatMessageService.sendWechatTextMsg(sessionList.getClientId(), sessionList.getBotId(), message);
                } else if (wechatCpService.isWorkWechat(sessionList.getClientId(), sessionList.getBotId())) {
                    wechatCpService.sendWechatTextMsg(sessionList.getClientId(), sessionList.getBotId(), message);
                }
                packetList.add(new SocketPacketModel(sessionList.getSessionKey(), socketId, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistoryService.createChatHistory(sessionList.getClientId(), sessionList.getClientTypeId(),
                        "cs", "system", "event", "satisfaction", null,
                        sessionList.getOrgId(), sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(),
                        "满意度已下发", null, null, new com.alibaba.fastjson.JSONObject().fluentPut("sessionKey", sessionList.getSessionKey()).fluentPut("satisfaction", JSON.parseObject(satisfactionConfig.toString())), true))));
                if(type==SatisfactionDataEntity.TYPE_CUSTOMER){
                    packetList.add(new SocketPacketModel(sessionList.getSessionKey(), null, SocketEvent.EVENT,
                            new JSONObject().put("type","satisfaction").put("content","客户请求点评")
                                    .put("manual",new JSONObject().put("sessionKey",sessionList.getSessionKey()).put("appraiseStatus",-1)).toString()));
                }
                return true;
            }
        }
        return false;
    }


    private void generateAISummary(SessionListEntity sessionList) {
        try {
            String aiRemark = getAISummary(sessionList);
            if(StringUtils.isEmpty(aiRemark)){
                return;
            }

            ServiceSummaryEntity serviceSummary = serviceSummaryRepo.findFirstBySessionId(sessionList.getId());
            if(serviceSummary == null) {
                Date currentTime = new Date();
                serviceSummary = new ServiceSummaryEntity();
                serviceSummary.setSession(sessionList);
                serviceSummary.setOrgId(sessionList.getOrgId());
                serviceSummary.setCreateTime(currentTime);
                serviceSummary.setUpdateTime(currentTime);
                serviceSummary.setBusinessId(sessionList.getBusinessId());
                serviceSummary.setClientTypeId(sessionList.getClientTypeId());
            }
            serviceSummary.setAiRemark(aiRemark);
            serviceSummaryRepo.save(serviceSummary);

            processSummarySensitive(sessionList, aiRemark);
        } catch (Exception e) {
            LOGGER.error("处理服务小结发生错误, sessionId:{}, error:{}", sessionList.getId(), e.getMessage(), e);
        }
    }

    private String getAISummary(SessionListEntity sessionList) {
        try {
            List<ChatHistoryEntity> chatHistoryList = chatHistoryRepo.findHistoryListBySessionKey(sessionList.getSessionKey());
            if(CollectionUtils.isEmpty(chatHistoryList)){
                LOGGER.info("AI小结会话历史记录为空, sessionId:{}", sessionList.getId());
                return null;
            }
            chatHistoryList = chatHistoryList.stream()
                    .filter(v -> v.getRecall() == null)
                    .filter(v -> v.getSender().equals("user") || v.getSender().equals("manual"))
                    .collect(Collectors.toList());
            long userMsgCount = chatHistoryList.stream().filter(v -> v.getSender().equals("user")).count();
            if(userMsgCount==0){
                LOGGER.info("AI小结用户消息为空, sessionId:{}", sessionList.getId());
                return null;
            }
            StringBuilder sb = new StringBuilder();
            for(ChatHistoryEntity chatHistory:chatHistoryList){
                String content = chatHistory.getContent();
                content = CommonUtil.removeHtmlTagsAndNewlines(content);
                if(chatHistory.getSender().equals("manual")){
                    content = "客服：" + content + "\n";
                } else if (chatHistory.getSender().equals("user")) {
                    content = "用户：" + content + "\n";
                }
                sb.append(content);
            }
            JSONObject info = new JSONObject();
            info.put("dialogue", sb.toString());
            String response = HttpClientUtil.post(appPropertyConfig.getAiSummaryUrl(), info.toString());
            LOGGER.info("AI小结接口返回数据, sessionId:{}, req:{}, response:{}", sessionList.getId(), info, response);
            if(StringUtils.isBlank(response)){
                LOGGER.error("AI小结接口返回为空, sessionId:{}", sessionList.getId());
                return null;
            }
            JSONObject aiSummary = new JSONObject(response);
            if(aiSummary.has("code") && aiSummary.getInt("code") == 0 && aiSummary.has("data")) {
                JSONObject data = aiSummary.getJSONObject("data");
                String summary = data.getString("summary");
                return summary;
            }
            return null;
        } catch (Exception e) {
            LOGGER.error("生成AI小结备注发生错误, sessionId:{}, error:{}", sessionList.getId(), e.getMessage(), e);
        }
        return null;
    }



    public void processSummarySensitive(SessionListEntity sessionList, String content) {
        if(StringUtils.isNotBlank(content)) {
            try {
                Integer orgId = sessionList.getOrgId();
                CommonConfigEntity configEntity = commonConfigRepo.findOneByOrgIdAndType(orgId, "summarySensitive");
                if(configEntity != null && StringUtils.isNotBlank(configEntity.getContent())) {
                    List<String> sensitiveList = JSON.parseArray(configEntity.getContent(), String.class);
                    List<String> hitList = new ArrayList<>();
                    for(String sensitive : sensitiveList) {
                        if(content.contains(sensitive)) {
                            hitList.add(sensitive);
                        }
                    }
                    if(hitList.size() > 0) {
                        Map<String, String> warnMap = new HashMap<>();
                        com.alibaba.fastjson.JSONObject detail = sessionList.getCustomerDetail();
                        JSONArray customers = null != detail ? detail.getJSONArray("customers") : null;
                        if(null != customers && customers.size() > 0) {
                            com.alibaba.fastjson.JSONObject customer = customers.getJSONObject(0);
                            warnMap.put("uuid", customer.getString("uuid"));
                        }
                        String serviceName = "";
                        String extraField = userRepo.findExtraField(orgId, appPropertyConfig.getCsProductId(), sessionList.getLastServiceUser());
                        if (StringUtils.isNotBlank(extraField)) {
                            com.alibaba.fastjson.JSONObject extra = JSON.parseObject(extraField);
                            serviceName = extra.getString("nickName");
                        }
                        warnMap.put("customerName", sessionList.getCustomerName());
                        warnMap.put("serviceName", serviceName);
                        warnMap.put("hitInfo", String.join(",", hitList));
                        warnMap.put("sessionId", sessionList.getId().toString());
                        sendWarnInfo(warnMap);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("检测服务小结, 发送告警信息发生错误, sessionId:{}", sessionList.getId(),e);
            }
        }
    }

    private void sendWarnInfo(Map<String, String> warnMap) {
        try {
            com.alibaba.fastjson.JSONObject data = new com.alibaba.fastjson.JSONObject();
            data.put("msgtype", "markdown");
            StringBuilder sb = new StringBuilder();
            sb.append("<font color=\"warning\">").append("【在线客服敏感小结通知】").append("</font>\n");
            sb.append("用户姓名：").append(warnMap.get("customerName")).append("\n");
            if(warnMap.containsKey("uuid")) {
                sb.append("用户uuid：").append(warnMap.get("uuid")).append("\n");
            }
            sb.append("客服姓名：").append(warnMap.get("serviceName")).append("\n");
            sb.append("AI小结命中敏感词：").append("<font color=\"warning\">").append(warnMap.get("hitInfo")).append("</font>\n");
            data.put("markdown", new com.alibaba.fastjson.JSONObject().fluentPut("content", sb.toString()));
            HttpClientUtil.post(appPropertyConfig.getSummaryWebhookUrl(), data.toJSONString(), 3000);
            LOGGER.info("检测服务小结，sessionId:{}, 发送消息：{}", warnMap.get("sessionId"), sb);
        } catch (Exception e) {
            LOGGER.error("检测服务小结, 发送告警信息发生错误",e);
        }
    }

    /**
     * 提交质量检查
     */
    private void submitQualityInspection(SessionListEntity sessionList) {
        try {
            qualityInspectionService.submitQualityInspectionAsync(sessionList);
        } catch (Exception e) {
            LOGGER.error("提交质量检查发生错误, sessionId:{}, error:{}", sessionList.getId(), e.getMessage(), e);
        }
    }


}
