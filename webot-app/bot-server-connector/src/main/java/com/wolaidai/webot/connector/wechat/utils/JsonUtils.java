package com.wolaidai.webot.connector.wechat.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
public class JsonUtils {
    public static String toJson(Object obj) {
        Gson gson = new GsonBuilder()
            .setPrettyPrinting()
            .disableHtmlEscaping()
            .create();
        return gson.toJson(obj);
    }
}
