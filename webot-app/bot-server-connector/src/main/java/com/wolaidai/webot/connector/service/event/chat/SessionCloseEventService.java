package com.wolaidai.webot.connector.service.event.chat;

import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.business.ConversationService;
import com.wolaidai.webot.connector.service.business.ManualChatService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.data.mysql.entity.bot.BotEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class SessionCloseEventService extends BaseEventService {

    private final ManualChatService manualChatService;

    private final ConversationService conversationService;

    private final StringRedisTemplate csStringRedisTemplate;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        JSONObject data = (JSONObject)model.getData();
        BotEntity botEntity = conversationService.getBotEntity(data.getString("accessKey"));
        if (botEntity != null) {
            HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
            String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, data.getString("clientId"), botEntity.getId());
            String sessionKey = hashOperations.get(conversationKey, "sessionKey");
            if (StringUtils.isNotBlank(sessionKey)) {
                manualChatService.offlineSession(sessionKey, SessionListEntity.CLOSE_TYPE_CUSTOMER);
            }
        }
        return null;
    }
}
