package com.wolaidai.webot.connector.wechat.handler.wx;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.constant.RedisConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.constant.SocketNamespace;
import com.wolaidai.webot.connector.model.ConversationContextModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.utils.CommonUtil;
import com.wolaidai.webot.connector.wechat.constant.MsgMenuPrefix;
import com.wolaidai.webot.connector.wechat.utils.MsgUtils;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mysql.entity.bot.NoticeEntity;
import com.wolaidai.webot.data.mysql.entity.bot.SkillGroupEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.enums.WxMpApiUrl;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import ws.schild.jave.EncoderException;
import ws.schild.jave.MultimediaObject;

import java.net.URL;
import java.util.*;

import static me.chanjar.weixin.common.api.WxConsts.XmlMsgType;

@Component
public class MsgMpHandler extends AbstractMpHandler {

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService wxMpService,
                                    WxSessionManager sessionManager) {
        int clientType = 2;
        String appId = wxMpService.getWxMpConfigStorage().getAppId();
        String accessKey = (String) context.get("accessKey");
        if(StringUtils.isBlank(accessKey)){
            logger.error("accessKey not exists,appId:{}",appId);
            return null;
        }
        String clientId = wxMessage.getFromUser();
        String origin = wxMessage.getToUser();
        String replyContent = wxMessage.getContent();
        Map<String,Object> media = null;
        Map<String,Object> manual = null;
        boolean needSave = true;
        List<SkillGroupEntity> allSkillGroups = getAllSkillGroups(accessKey);
        SkillGroupEntity skillGroup = null;
        try {
            if (Objects.equals(replyContent, "【收到不支持的消息类型，暂无法显示】")) {
                wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().toUser(clientId).content("不支持该消息类型").build());
                return null;
            }
            String skillGroupId = getSwitchSkillGroup(clientId, accessKey);
            ConversationContextModel wechatConversation = conversationService.getWechatConversation(clientId, clientType, appId, accessKey, skillGroupId);
            if (wechatConversation == null) {
                logger.error("bot not found,accessKey:{}", accessKey);
                return null;
            }
            ConversationContextModel.ContextInfo contextInfo = wechatConversation.getInfo();
            ChatHistoryEntity lastChatHistory = null;
            if(XmlMsgType.IMAGE.equals(wxMessage.getMsgType())){
                String fileId = uploadMedia(wxMessage.getPicUrl(),"2","1");
                replyContent = fileId;
            }if(XmlMsgType.VIDEO.equals(wxMessage.getMsgType())){
                String url = WxMpApiUrl.Material.MEDIA_GET_URL.getUrl(wxMpService.getWxMpConfigStorage())+"?access_token="+wxMpService.getAccessToken()+"&media_id="+wxMessage.getMediaId();
                String fileId = uploadMedia(url,"2","2");
                replyContent = fileId;
            }else if(XmlMsgType.VOICE.equals(wxMessage.getMsgType())){
                String url = WxMpApiUrl.Material.MEDIA_GET_URL.getUrl(wxMpService.getWxMpConfigStorage())+"?access_token="+wxMpService.getAccessToken()+"&media_id="+wxMessage.getMediaId();
                String fileId = uploadMedia(url,"2","3");
                replyContent = fileId;
                long duration = 0;
                try {
                    MultimediaObject multimediaObject = new MultimediaObject(new URL(url));
                    duration = multimediaObject.getInfo().getDuration();
                } catch (EncoderException e) {
                    logger.error("音频文件读取异常",e);
                }
                media = new HashMap<>();
                if(duration>0){
                    media.put("duration",duration/1000);
                }
                String recognition = wxMessage.getRecognition();
                if(recognition!=null){
                    media.put("recognition",recognition);
                }
            }else if(XmlMsgType.TEXT.equals(wxMessage.getMsgType())) {
//                replyContent = replyContent.replace("(", "（").replace(")", "）").replace("^", "");
                if(wxMessage.getBizMsgMenuId()!=null){
                    if(wxMessage.getBizMsgMenuId().startsWith(MsgMenuPrefix.CS_PREFIX)){
                        String csTime = StringUtils.substringAfter(wxMessage.getBizMsgMenuId(), MsgMenuPrefix.CS_PREFIX);
                        if(StringUtils.isNotBlank(csTime)&& !DateUtils.isSameDay(new Date(),DateUtils.parseDate(csTime,"yyyyMMdd"))){
                            org.json.JSONObject botTurnartificialPolicyJsonObject = manualChatService.getBotTurnArtificialPolicy(contextInfo.getOrgId());
                            if (null != botTurnartificialPolicyJsonObject) {
                                String linkFailureReply = botTurnartificialPolicyJsonObject.optString("linkFailureReply");
                                if(StringUtils.isNotBlank(linkFailureReply)){
                                    wechatMessageService.sendWechatTextMsg(clientId, contextInfo.getBotId(),linkFailureReply);
                                    return null;
                                }
                            }
                        }
                        toCs(wxMpService, clientId, origin, contextInfo);
                        return null;
                    }else if(wxMessage.getBizMsgMenuId().startsWith(MsgMenuPrefix.SATISFACTION_PREFIX)){
                        int level = NumberUtils.toInt(wxMessage.getBizMsgMenuId().replaceFirst(MsgMenuPrefix.SATISFACTION_PREFIX,""), -1);
                        sendSatisfaction(clientType, clientId, contextInfo.getBotId(), level);
                        return null;
                    }else if(wxMessage.getBizMsgMenuId().startsWith(MsgMenuPrefix.SKILLGROUPID_PREFIX)){
                        int id = NumberUtils.toInt(wxMessage.getBizMsgMenuId().replaceFirst(MsgMenuPrefix.SKILLGROUPID_PREFIX,""), -1);
                        if(id>0) {
                            skillGroup = findMatchedSkillGroup(accessKey, replyContent, id);
                            if (skillGroup == null) {
                                if (allSkillGroups.size() == 1) {
                                    skillGroup = allSkillGroups.get(0);
                                } else {
                                    sendSwitchSkillGroupMsg(wxMpService, clientId, accessKey);
                                    addPreSwitchSkillGroup(clientId, accessKey);
                                    return null;
                                }
                            }
                        }
                    }
                }else {
                    if(allSkillGroups.size()>1) {
                        String preSkillGroupId = getPreSwitchSkillGroup(clientId, accessKey);
                        if (preSkillGroupId!=null) {
                            if(skillGroup==null){
                                int index = NumberUtils.toInt(replyContent,-1);
                                if(index==-1){
                                    index = NumberUtils.toInt(StringUtils.substringBefore(replyContent,"."),-1);
                                }
                                if(index>0) {
                                    skillGroup = getSkillGroupByIndex(accessKey,index-1);
                                }else if("是".equals(replyContent)){
                                    skillGroup = getSkillGroupById(accessKey,NumberUtils.toInt(preSkillGroupId,-1));
                                }else{
                                    skillGroup = getSkillGroupByName(accessKey,replyContent);
                                }
                            }
                        }
                        if(skillGroup==null){
                            SkillGroupEntity tmpSkillGroup = getSkillGroupContainsName(accessKey, replyContent);
                            if (tmpSkillGroup != null && !String.valueOf(tmpSkillGroup.getId()).equals(skillGroupId)) {
                                sendSwitchSkillGroupMsgByName(wxMpService, clientId, tmpSkillGroup);
                                addPreSwitchSkillGroup(clientId, accessKey, tmpSkillGroup.getId());
                                return null;
                            } else if (replyContent.matches(".*(((选择|修改|更改|更换|切换|改|换)(业务|产品))|((业务|产品)(选择|修改|更改|更换|切换))).*")) {
                                sendSwitchSkillGroupMsg(wxMpService, clientId, accessKey);
                                addPreSwitchSkillGroup(clientId, accessKey);
                                return null;
                            }
                        }
                    }else if(allSkillGroups.size()==1&&skillGroupId==null){
                        skillGroup = allSkillGroups.get(0);
                    }
                }
                if(hasPreSatisfaction(clientId, contextInfo.getBotId())){
                    int level = NumberUtils.toInt(replyContent,-1);
                    if(level==-1){
                        level = NumberUtils.toInt(StringUtils.substringBefore(replyContent,"."),-1);
                    }
                    if (sendSatisfaction(clientType, clientId, contextInfo.getBotId(), level)) {
                        return null;
                    }
                }
                if(skillGroupId==null&&skillGroup==null){
                    lastChatHistory = chatHistoryService.createChatHistory(Objects.toString(wxMessage.getMsgId()), clientId, clientType, origin, "bot", "user", wxMessage.getMsgType(), null, null,
                            contextInfo.getOrgId(), contextInfo.getBotId(), contextInfo.getSkillGroupId(), contextInfo.getGcid(), contextInfo.getCid(), replyContent, null, null,true);
                    org.json.JSONObject reply = apiService.getOpReply(lastChatHistory.getOrgId(), lastChatHistory.getId(), lastChatHistory.getBotId(),  clientId, clientType, lastChatHistory.getType(), lastChatHistory.getContent());
                    if(sendBotReply(wxMpService, reply,lastChatHistory,contextInfo, wechatConversation.isNew())){
                        return null;
                    }
                }
                if(skillGroup!=null) {
                    needSave = false;
                    skillGroupId = String.valueOf(skillGroup.getId());
                    setSwitchSkillGroup(clientId, accessKey, skillGroupId);
                    wechatConversation = conversationService.getWechatConversation(clientId, clientType, appId, accessKey, skillGroupId);
                    if (wechatConversation == null) {
                        logger.error("create wechat conversation error,clientId:{},clientType:{},accessKey:{},skillGroupId:{}", clientId,clientType,accessKey,skillGroupId);
                        return null;
                    }
                    NoticeEntity notice = noticeRepo.findBySkillGroupId(skillGroup.getId());
                    if(notice!=null){
                        wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().toUser(clientId).content(MsgUtils.handleText(notice.getContent())).build());
                    }
                    if(allSkillGroups.size()>1) {
                        wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().toUser(clientId).content(String.format("你选择了“%s”，如需咨询其他产品业务请输入“切换业务”", skillGroup.getName())).build());
                    }
                    lastChatHistory = chatHistoryService.getLastChatHistory(clientId,origin, contextInfo.getBotId());
                }
            }
            if(StringUtils.isBlank(replyContent)){
                logger.error("消息类型不支持,内容:{}",wxMessage);
                return null;
            }
            contextInfo = wechatConversation.getInfo();
            if(contextInfo.getSessionKey()!=null){
                manual = new HashMap<>();
                manual.put("sessionKey",contextInfo.getSessionKey());
            }
            if(needSave){
                if(lastChatHistory==null) {
                    lastChatHistory = chatHistoryService.createChatHistory(Objects.toString(wxMessage.getMsgId()), clientId, clientType, origin, contextInfo.getScene(), "user", wxMessage.getMsgType(), null, null,
                            contextInfo.getOrgId(), contextInfo.getBotId(), NumberUtils.createInteger(skillGroupId), contextInfo.getGcid(), contextInfo.getCid(), replyContent, media, manual,true);

                }
            }else{
                if(lastChatHistory!=null) {
                    lastChatHistory.setSkillGroupId(NumberUtils.createInteger(skillGroupId));
                    lastChatHistory.setCid(wechatConversation.getInfo().getCid());
                    lastChatHistory.setGcid(wechatConversation.getInfo().getGcid());
                    chatHistoryService.saveChatHistory(lastChatHistory);
                }
            }
            if(skillGroupId==null){
                sendSwitchSkillGroupMsg(wxMpService, clientId, accessKey);
                addPreSwitchSkillGroup(clientId, accessKey);
                return null;
            }else{
                if(lastChatHistory==null){
                    return null;
                }
                if(Objects.equals(contextInfo.getScene(), "bot")) {
                    conversationService.updateConversationStatus(clientId, contextInfo.getBotId(), contextInfo.getCid(), contextInfo.getStatus());
                    String recognition = lastChatHistory.getMedia() != null ? Objects.toString(lastChatHistory.getMedia().get("recognition"), null) : null;
                    org.json.JSONObject reply = apiService.getReply(lastChatHistory.getOrgId(), lastChatHistory.getId(), lastChatHistory.getBotId(), lastChatHistory.getSkillGroupId(), null,  clientId, clientType, lastChatHistory.getType(), lastChatHistory.getContent(), recognition, lastChatHistory.getCid(), false);
                    sendBotReply(wxMpService, reply,lastChatHistory,contextInfo, wechatConversation.isNew());
                    
                }else if(Objects.equals(contextInfo.getScene(), "cs")){
                    boolean isExistManual = StringUtils.equalsAny(lastChatHistory.getContent(),"取消人工","结束人工","退出人工");
                    String sessionKey = contextInfo.getSessionKey();
                    if(StringUtils.isNotBlank(sessionKey)){
                        SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
                        if(sessionList!=null) {
                            manualChatService.initUserMsgToSession(sessionList, lastChatHistory);
                            //处理用户敏感信息
                            CommonUtil.processUserSensitiveInfo(lastChatHistory);
                            csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), Arrays.asList(new SocketPacketModel(contextInfo.getSessionKey(), null, SocketEvent.MESSAGE, JSON.toJSONString(lastChatHistory))));
                            if(isExistManual){
                                manualChatService.offlineSession(sessionKey,SessionListEntity.CLOSE_TYPE_CUSTOMER);
                            }
                        }
                    }else{
                        if(isExistManual){
                            manualChatService.dequeue(clientId,contextInfo.getAccessKey());
                            wechatMessageService.sendWechatTextMsg(clientId, contextInfo.getBotId(), "您已退出人工");
                        }
                    }
                }
            }
        }catch (Exception e){
            logger.error("send msg error",e);
        }
        return null;
//        //TODO 组装回复消息
//        String content = "收到信息内容：" + JsonUtils.toJson(wxMessage);
//        return new TextBuilder().build(content, wxMessage, wxMpService);
    }
}
