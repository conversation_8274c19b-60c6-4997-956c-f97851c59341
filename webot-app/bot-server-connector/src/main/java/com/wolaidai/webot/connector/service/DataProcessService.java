package com.wolaidai.webot.connector.service;

import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.List;

public abstract class DataProcessService extends BaseService implements ApplicationContextAware {
    protected ApplicationContext applicationContext;
    protected abstract List<SocketPacketModel> processData(MessageModel messageModel);

    public List<SocketPacketModel> process(MessageModel messageModel){
        LOGGER.info("process msg:{}",messageModel);
        try{
            return processData(messageModel);
        }catch (Exception e){
            LOGGER.error("Data process error",e);
        }
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

}
