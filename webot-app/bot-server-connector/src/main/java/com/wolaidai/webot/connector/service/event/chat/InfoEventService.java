package com.wolaidai.webot.connector.service.event.chat;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.constant.ManualConfigConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.model.inner.WorkbenchMessageModel;
import com.wolaidai.webot.connector.service.business.ChatRecordService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.data.mysql.entity.chat.*;
import com.wolaidai.webot.data.mysql.repo.ChatMembersSnapshotRepo;
import com.wolaidai.webot.data.mysql.repo.ChatSessionRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class InfoEventService extends BaseEventService {
    private final StringRedisTemplate csStringRedisTemplate;
    private final ChatSessionRepo chatSessionRepo;
    private final ChatRecordService chatRecordService;
    private final ChatMembersSnapshotRepo chatMembersSnapshotRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject)model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        String socketId = model.getSocketId();
        String source = data.optString("source");
        if(Objects.equals(source, ManualConfigConstants.WORKBENCH_TYPE)){
            WorkbenchMessageModel workbenchMessageModel = JSON.parseObject(data.toString(), WorkbenchMessageModel.class);
            sendWorkbenchMessage(socketId, models, workbenchMessageModel, callback);
        }
        return models;
    }

    private void sendWorkbenchMessage(String socketId, List<SocketPacketModel> models, WorkbenchMessageModel messageModel, SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback) {
        com.alibaba.fastjson.JSONObject manual = messageModel.getManual();
        Integer sessionKey = manual.getInteger("sessionKey");
        if (sessionKey != null) {
            ChatSessionEntity sessionEntity = chatSessionRepo.findById(sessionKey).orElse(null);
            if (sessionEntity != null) {
                HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                ChatRoomEntity room = sessionEntity.getRoom();
                Integer orgId = sessionEntity.getOrgId();
                String sender = messageModel.getSender();
                Date dateNow = new Date();
                String toEmail = null;
                if (room != null) { //群聊
                    //群成员
                    Set<UserStateEntity> roomMembers =  room.getRoomUsers().stream().map(ChatRoomUsersEntity::getUser).collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(roomMembers)) {
                        //判断在群里
                        if (!roomMembers.stream().map(UserStateEntity::getEmail).collect(Collectors.toSet()).contains(sender)) {
                            if (callback != null) {
                                callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", "你已被移出了群聊").put("timestamp", System.currentTimeMillis()));
                            }
                            return;
                        }
                        //依然在群聊中的成员
                        Set<ChatMembersEntity> inGroupChatMembers = sessionEntity.getMembers().stream().filter(c -> roomMembers.contains(c.getUser())).collect(Collectors.toSet());
                        inGroupChatMembers.forEach(v -> {
                            UserStateEntity user = v.getUser();
                            //处理群成员未读消息数
                            String currentSessionKey = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, user.getEmail()), "currentInnerSessionKey");
                            //非当前发送人&(不在线|在线聊天会话不等于当前会话)
                            if (!sender.equals(user.getEmail()) && (StringUtils.isBlank(currentSessionKey)
                                    || StringUtils.isNotBlank(currentSessionKey) && !Integer.valueOf(currentSessionKey).equals(sessionKey))) { //非发送人消息数+1
                                v.setUnreadMsgCount(v.getUnreadMsgCount() + 1);
                            }
                            //激活收信人状态，以便查询会话列表
                            if (ChatMembersEntity.STATUS_INACTIVE.equals(v.getStatus())) {
                                v.setStatus(ChatMembersEntity.STATUS_ACTIVE);
                                v.setUpdateTime(dateNow);
                            }
                        });
                    }
                } else {
                    //收信人
                    ChatMembersEntity receiveMember = sessionEntity.getMembers().stream().filter(cm -> !cm.getUser().getEmail().equals(sender)).findAny().orElse(null);
                    if (receiveMember != null) {
                        toEmail = receiveMember.getUser().getEmail();
                        String currentSessionKey = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, toEmail), "currentInnerSessionKey");
                        //不在线|在线聊天会话不等于当前会话
                        if (StringUtils.isBlank(currentSessionKey)
                                || StringUtils.isNotBlank(currentSessionKey) && !Integer.valueOf(currentSessionKey).equals(sessionKey)) {
                            receiveMember.setUnreadMsgCount(receiveMember.getUnreadMsgCount() + 1);
                        }
                        //激活收信人状态，以便查询会话列表
                        if (ChatMembersEntity.STATUS_INACTIVE.equals(receiveMember.getStatus())) {
                            receiveMember.setStatus(ChatMembersEntity.STATUS_ACTIVE);
                            receiveMember.setUpdateTime(dateNow);
                        }
                    }
                }
                sessionEntity.setLastMsg(MessageUtil.escapeHtml(messageModel.getContent(), messageModel.getType()));
                sessionEntity.setLastMsgSender(sender);
                sessionEntity.setLastMsgTime(dateNow);
                sessionEntity.setUpdateTime(new Date());
                chatSessionRepo.save(sessionEntity);
                //群聊
                if (room != null) {
                    models.add(new SocketPacketModel("session_" + sessionEntity.getId(), socketId, SocketEvent.INFO,
                            MessageUtil.toJsonString(chatRecordService.createChatRecord(messageModel.getMsgId(), sender, null, messageModel.getContent(),
                                    messageModel.getType(), sessionEntity, chatMembersSnapshotRepo.findFirstBySessionIdOrderByIdDesc(sessionKey), messageModel.getMedia(), manual))));
                } else if (StringUtils.isNotBlank(toEmail)) { //单聊
                   ChatRecordEntity chatRecord = chatRecordService.createChatRecord(messageModel.getMsgId(),
                            sender, toEmail, messageModel.getContent(), messageModel.getType(), sessionEntity, null, messageModel.getMedia(), manual);
                    String toSocketId = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, toEmail), "socketId");
                    if (toSocketId != null) {
                        models.add(new SocketPacketModel(null, toSocketId, SocketEvent.INFO, MessageUtil.toJsonString(chatRecord)));
                    }
                }
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret", 0).put("msgId", messageModel.getMsgId()).put("timestamp", System.currentTimeMillis()));
                }
            }
        }
    }
}
