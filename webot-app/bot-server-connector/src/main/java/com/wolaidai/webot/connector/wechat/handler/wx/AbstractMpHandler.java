package com.wolaidai.webot.connector.wechat.handler.wx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.constant.ManualConfigConstants;
import com.wolaidai.webot.connector.constant.RedisConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.constant.SocketNamespace;
import com.wolaidai.webot.connector.entity.Event;
import com.wolaidai.webot.connector.model.ConversationContextModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.business.*;
import com.wolaidai.webot.connector.service.event.chat.InitEventService;
import com.wolaidai.webot.connector.service.worktime.BotWorkTimeService;
import com.wolaidai.webot.connector.service.worktime.CsWorkTimeService;
import com.wolaidai.webot.connector.wechat.constant.MsgMenuPrefix;
import com.wolaidai.webot.connector.wechat.service.WechatMessageService;
import com.wolaidai.webot.connector.wechat.utils.MsgUtils;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.repo.ComplexChatHistoryRepo;
import com.wolaidai.webot.data.mysql.entity.bot.MaterialEntity;
import com.wolaidai.webot.data.mysql.entity.bot.SkillGroupEntity;
import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SatisfactionDataEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.repo.*;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpMessageHandler;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.builder.kefu.WxMsgMenuBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public abstract class AbstractMpHandler implements WxMpMessageHandler {
    protected Logger logger = LoggerFactory.getLogger(getClass());
    private Cache cache = CacheBuilder.newBuilder().expireAfterWrite(3, TimeUnit.MINUTES).build();
    @Autowired
    protected AppPropertyConfig appPropertyConfig;
    @Autowired
    protected SessionListRepo sessionListRepo;
    @Autowired
    protected ManualChatService manualChatService;
    @Autowired
    protected ChatHistoryService chatHistoryService;
    @Autowired
    protected ConversationService conversationService;
    @Autowired
    protected BotRepo botRepo;
    @Autowired
    protected SkillGroupRepo skillGroupRepo;
    @Resource
    protected StringRedisTemplate botStringRedisTemplate;
    @Resource
    protected StringRedisTemplate csStringRedisTemplate;
    @Autowired
    protected NoticeRepo noticeRepo;
    @Autowired
    protected MaterialRepo materialRepo;
    @Autowired
    protected ApiService apiService;
    @Resource
    protected RedisTemplate csRedisTemplate;
    @Autowired
    protected CsWorkTimeService csWorkTimeService;
    @Autowired
    protected BotWorkTimeService botWorkTimeService;
    @Autowired
    protected QueueListRepo queueListRepo;
    @Autowired
    protected SatisfactionDataRepo satisfactionDataRepo;
    @Autowired
    protected ComplexChatHistoryRepo complexChatHistoryRepo;
    @Autowired
    protected WechatMessageService wechatMessageService;
    @Autowired
    protected BlackListRepo blackListRepo;
    @Autowired
    protected TemplateService templateService;
    @Autowired
    private InitEventService initEventService;

    protected List<SkillGroupEntity> getAllSkillGroups(String accessKey){
        String key = "skillGroup_"+accessKey;
        List<SkillGroupEntity> skillGroups = (List<SkillGroupEntity>) cache.getIfPresent(key);
        if(skillGroups==null) {
            skillGroups = skillGroupRepo.findByAccessKey(accessKey);
            if(!CollectionUtils.isEmpty(skillGroups)){
                cache.put(key,skillGroups);
            }
        }
        return skillGroups;
    }

    protected SkillGroupEntity getSkillGroupByName(String accessKey, String name){
        List<SkillGroupEntity> allSkillGroups = getAllSkillGroups(accessKey);
        if(!CollectionUtils.isEmpty(allSkillGroups)){
            return allSkillGroups.stream().filter(v-> StringUtils.equalsAnyIgnoreCase(name,v.getName(),v.getAliasName())).findAny().orElse(null);
        }
        return null;
    }

    protected SkillGroupEntity getSkillGroupContainsName(String accessKey, String name){
        List<SkillGroupEntity> allSkillGroups = getAllSkillGroups(accessKey);
        if(!CollectionUtils.isEmpty(allSkillGroups)){
            return allSkillGroups.stream().filter(v-> StringUtils.containsAnyIgnoreCase(name,v.getName(),v.getAliasName())).findAny().orElse(null);
        }
        return null;
    }

    protected SkillGroupEntity getSkillGroupByIndex(String accessKey, int index){
        List<SkillGroupEntity> allSkillGroups = getAllSkillGroups(accessKey);
        if(!CollectionUtils.isEmpty(allSkillGroups)&&index<allSkillGroups.size()){
            return allSkillGroups.get(index);
        }
        return null;
    }

    protected SkillGroupEntity getSkillGroupById(String accessKey, int id){
        List<SkillGroupEntity> allSkillGroups = getAllSkillGroups(accessKey);
        if(!CollectionUtils.isEmpty(allSkillGroups)){
            return allSkillGroups.stream().filter(v->Objects.equals(v.getId(),id)).findAny().orElse(null);
        }
        return null;
    }

    protected boolean hasPreSatisfaction(String clientId,Integer botId){
        String key = String.format(RedisKey.WECHAT_PRESATISFACTION, clientId, botId);
        boolean result = csStringRedisTemplate.hasKey(key);
        if(result) {
            csStringRedisTemplate.delete(key);
        }
        return result;
    }

    protected void addPreSwitchSkillGroup(String clientId,String accessKey){
        csStringRedisTemplate.opsForValue().set(String.format(RedisKey.WECHAT_PRESWITCH_SKILLGROUP,clientId,accessKey),"0",1,TimeUnit.DAYS);
    }

    protected void addPreSwitchSkillGroup(String clientId,String accessKey,Integer skillGroupId){
        csStringRedisTemplate.opsForValue().set(String.format(RedisKey.WECHAT_PRESWITCH_SKILLGROUP,clientId,accessKey),skillGroupId.toString(),1,TimeUnit.DAYS);
    }

    protected String getPreSwitchSkillGroup(String clientId,String accessKey){
        String key = String.format(RedisKey.WECHAT_PRESWITCH_SKILLGROUP, clientId, accessKey);
        String result = csStringRedisTemplate.opsForValue().get(key);
        if(result!=null) {
            csStringRedisTemplate.delete(key);
        }
        return result;
    }

    protected void setSwitchSkillGroup(String clientId,String accessKey,String skillGroupId){
        csStringRedisTemplate.opsForValue().set(String.format(RedisKey.WECHAT_SWITCH_SKILLGROUP,clientId,accessKey),skillGroupId,1,TimeUnit.DAYS);
    }

    protected String getSwitchSkillGroup(String clientId,String accessKey){
        String skillGroupKey = String.format(RedisKey.WECHAT_SWITCH_SKILLGROUP, clientId, accessKey);
        String skillGroupId = csStringRedisTemplate.opsForValue().get(skillGroupKey);
        if(skillGroupId==null){
            List<SkillGroupEntity> allSkillGroups = getAllSkillGroups(accessKey);
            if(allSkillGroups.size()==1){
                skillGroupId = String.valueOf(allSkillGroups.get(0).getId());
                setSwitchSkillGroup(clientId,accessKey,skillGroupId);
            }
        }
        //技能自动延期一天，避免让用户重复选择
        if(skillGroupId!=null){
            csStringRedisTemplate.expire(skillGroupKey, 1,TimeUnit.DAYS);
        }
        return skillGroupId;
    }

    protected String uploadMedia(String url, String channel, String type){
        try {
            String response = HttpClientUtil.postFormData(appPropertyConfig.getFileServerUrl() + "/upload", Arrays.asList(new BasicNameValuePair("url", url), new BasicNameValuePair("channel", channel), new BasicNameValuePair("type", type)));
            if(StringUtils.isNotBlank(response)){
                JSONObject jsonObject = JSON.parseObject(response);
                JSONObject data = jsonObject.getJSONObject("data");
                if(data!=null) {
                    return data.getString("fileId");
                }
            }
        } catch (Exception e) {
            logger.error("upload image error",e);
        }
        return null;
    }

    protected SkillGroupEntity findMatchedSkillGroup(String accessKey, String replyContent, int skillGroupId) {
        SkillGroupEntity skillGroup = getSkillGroupById(accessKey,skillGroupId);
        if(skillGroup==null) {
            skillGroup = getSkillGroupByName(accessKey, replyContent);
        }
        if(skillGroup==null){
            skillGroup = getSkillGroupContainsName(accessKey, replyContent);
        }
        return skillGroup;
    }

    protected boolean toCs(WxMpService wxMpService,String clientId,String origin,ConversationContextModel.ContextInfo contextInfo) throws WxErrorException {
        Integer skillGroupId = contextInfo.getSkillGroupId();
        if (skillGroupId!=null) {
            Integer blacklistId = blackListRepo.findByOrgIdAndPhoneAndClientId(contextInfo.getOrgId(), contextInfo.getAccount(), clientId);
            if(blacklistId!=null){
                org.json.JSONObject blackListConfig = manualChatService.getAutoReplyConfig(contextInfo.getOrgId(), contextInfo.getClientType(), ManualConfigConstants.AUTO_REPLY_BLACKLIST_MSG);
                if (blackListConfig != null && blackListConfig.getBoolean("status")){
                    wechatMessageService.sendWechatTextMsg(clientId,contextInfo.getBotId(),blackListConfig.getString("content"));
                    return false;
                }
            }

            if (contextInfo.getCid() != null) {
                conversationService.updateOpFlagByCid(contextInfo.getCid());
            }

            //该clientId存在在线会话
            String onlineSessionClientId = sessionListRepo.findClientIdByOnlineStatusAndCreateTime(contextInfo.getOrgId(), clientId, DateUtils.truncate(new Date(), Calendar.DATE));
            if (onlineSessionClientId != null) {
                logger.error("已在会话中，clientId:{}, orgId:{}", clientId, contextInfo.getOrgId());
                wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().toUser(clientId).content("已在会话中").build());
                return false;
            }
            //该clientId是否已在排队中
            QueueListEntity queueList = queueListRepo.findIdByInQueueStatusAndCreateTime(contextInfo.getOrgId(), clientId, DateUtils.truncate(new Date(), Calendar.DATE));
            if (queueList != null) {
                logger.error("已在排队中，clientId:{}, orgId:{}", clientId, contextInfo.getOrgId());
                wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().toUser(clientId).content("已在排队中").build());
                return false;
            }

            boolean isWorkTime = csWorkTimeService.isWorkTime(contextInfo.getOrgId());
            boolean hasOnline = manualChatService.onlineCount(contextInfo.getOrgId()) > 0;
            if (!isWorkTime || !hasOnline) {
                org.json.JSONObject serviceOfflineMsgJson = manualChatService.getAutoReplyConfig(contextInfo.getOrgId(), contextInfo.getClientType(), ManualConfigConstants.AUTO_REPLY_SERVICE_OFFLINE_MSG);
                if (serviceOfflineMsgJson != null) {
                    if (serviceOfflineMsgJson.getBoolean("status")) {
                        String content = serviceOfflineMsgJson.getString("content");
                        wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().toUser(clientId).content(!hasOnline ? "客服不在线" : "非工作时间").build());
                        wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().toUser(clientId).content(MsgUtils.handleText(content)).build());
                        return false;
                    }
                }
            } else {
                boolean queueExceed = false;
                org.json.JSONObject queueExceedMsgJson = manualChatService.getAutoReplyConfig(contextInfo.getOrgId(), contextInfo.getClientType(), ManualConfigConstants.AUTO_REPLY_QUEUE_EXCEED_MSG);
                if (queueExceedMsgJson != null && queueExceedMsgJson.getBoolean("status")) {
                    long queueCount = manualChatService.queueCount(contextInfo.getOrgId());
                    if (queueCount >= queueExceedMsgJson.getInt("exceed")) {
                        queueExceed = true;
                        wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().toUser(clientId).content(MsgUtils.handleText(queueExceedMsgJson.getString("content"))).build());
                    }
                }
                if (!queueExceed) {
                    SkillGroupEntity skillGroup = getSkillGroupById(contextInfo.getAccessKey(),skillGroupId);
                    if(skillGroup!=null) {
                        csStringRedisTemplate.opsForList().rightPush(RedisKey.EVENT_CLIENT_KEY, JSON.toJSONString(new Event(UUID.randomUUID().toString(), Event.INQUEUE_KEY, contextInfo.getOrgId(),
                                new JSONObject().fluentPut("origin",origin).fluentPut("clientId", clientId).fluentPut("clientType", contextInfo.getClientType()).fluentPut("botId", contextInfo.getBotId()).fluentPut("gcid", contextInfo.getGcid()).fluentPut("gcTime", contextInfo.getGcTime().getTime())
                                        .fluentPut("businessId", String.valueOf(skillGroupId)).fluentPut("businessName", skillGroup.getName()).fluentPut("account", contextInfo.getAccount()), new Date())));
                    }
                }
            }
        }
        return false;
    }

    protected void sendSwitchSkillGroupMsgByName(WxMpService wxMpService, String clientId, SkillGroupEntity skillGroup) throws WxErrorException {
        WxMsgMenuBuilder wxMsgMenuBuilder = WxMpKefuMessage.MSGMENU().headContent(String.format("您是否想切换到“%s”产品",Objects.toString(skillGroup.getAliasName(),skillGroup.getName()))).toUser(clientId);
        wxMsgMenuBuilder.addMenus(new WxMpKefuMessage.MsgMenu(MsgMenuPrefix.SKILLGROUPID_PREFIX + skillGroup.getId(), "是"));
        wxMsgMenuBuilder.addMenus(new WxMpKefuMessage.MsgMenu(MsgMenuPrefix.SKILLGROUPID_PREFIX +0, "否"));
        wechatMessageService.sendWechatMenuMsg(wxMpService, wxMsgMenuBuilder);
    }

    protected void sendSwitchSkillGroupMsg(WxMpService wxMpService, String clientId, String accessKey) throws WxErrorException {
        WxMsgMenuBuilder wxMsgMenuBuilder = WxMpKefuMessage.MSGMENU().headContent("请输入对应数字序号选择需要咨询的业务：").toUser(clientId);
        List<SkillGroupEntity> allSkillGroups = getAllSkillGroups(accessKey);
        for (int i = 0; i < allSkillGroups.size(); i++) {
            SkillGroupEntity skillGroup = allSkillGroups.get(i);
            String skillGroupName = Objects.toString(skillGroup.getAliasName(), skillGroup.getName());
            wxMsgMenuBuilder.addMenus(new WxMpKefuMessage.MsgMenu(MsgMenuPrefix.SKILLGROUPID_PREFIX +skillGroup.getId(), (i + 1) + "." + skillGroupName));
        }
        wechatMessageService.sendWechatMenuMsg(wxMpService, wxMsgMenuBuilder);
    }

    protected boolean sendBotReply(WxMpService wxMpService, org.json.JSONObject reply, ChatHistoryEntity chatHistory, ConversationContextModel.ContextInfo contextInfo, boolean isNew) throws Exception {
        JSONArray replyArr = reply.optJSONArray("reply");
        boolean hasOp = false;
        boolean response = false;
        if (replyArr != null) {
            for (int i = 0; i < replyArr.length(); i++) {
                org.json.JSONObject replyObj = replyArr.getJSONObject(i);
                String type = replyObj.optString("type");
                String replyContent = replyObj.optString("content");
                Map<String, Object> mediaMap = null;
                Map<String, Object> menuMap = null;
                boolean hasExtra = (i == replyArr.length() - 1 && reply.has("extra"));
                org.json.JSONObject extra = hasExtra?reply.optJSONObject("extra"):null;
                String materialId = replyObj.optString("material_id");
                if (StringUtils.isNotBlank(materialId)) {
                    MaterialEntity materialEntity = materialRepo.findByIdOrFileId(Integer.parseInt(materialId), materialId);
                    if (materialEntity.getType() == 1) {
                        type = "image";
                    } else if (materialEntity.getType() == 2) {
                        type = "voice";
                        mediaMap = new org.json.JSONObject().put("duration", Math.round(materialEntity.getDuration() / 1000)).toMap();
                    }
                    replyContent = materialEntity.getFileId();
                }else if(Objects.equals("menu",type)){
                    org.json.JSONObject menu = replyObj.getJSONObject("content");
                    replyContent = menu.getJSONArray("list").toList().stream().map(v -> "<p>" + ((Map) v).get("content") + "</p>").collect(Collectors.joining());
                    String headContent = menu.optString("head_content");
                    if (StringUtils.isNotBlank(headContent)) {
                        replyContent = headContent + replyContent;
                    }
                    String tailContent = menu.optString("tail_content");
                    if (StringUtils.isNotBlank(tailContent)) {
                        replyContent = replyContent + tailContent;
                    }
                    menu.put("head_content", replaceContent(headContent, contextInfo.getAccount()));
                    menuMap = menu.toMap();
                }
                if (hasExtra) {
                    if(!hasOp){
                        hasOp = extra.optBoolean("hasOp");
                    }
                    hasOp = hasOp&&botWorkTimeService.isWorkTime(chatHistory.getOrgId());
                    extra.put("hasOp", hasOp);
                    extra.put("hasFeedback", false);
                    extra.put("hitType", 0);
                    if (extra.getInt("matchSensitiveWord") == 1) {
                        extra.put("hitType", 1);
                        long hitSensitive = conversationService.incrementKey(chatHistory.getClientId(), chatHistory.getBotId(), "hitSensitive");
                        if (hitSensitive == 1) {
                            if (botWorkTimeService.isWorkTime(chatHistory.getOrgId())) {
                                extra.put("hasOp", true);
                            } else {
                                extra.put("hasFeedback", true);
                            }
                        }
                    } else if (extra.getInt("matchEmotion") == 1) {
                        extra.put("hitType", 2);
                        long hitEmotion = conversationService.incrementKey(chatHistory.getClientId(), chatHistory.getBotId(), "hitEmotion");
                        if (hitEmotion == 2) {
                            if (botWorkTimeService.isWorkTime(chatHistory.getOrgId())) {
                                extra.put("hasOp", true);
                            }
                        } else if (hitEmotion == 3) {
                            extra.put("hasFeedback", true);
                        }
                    }
                }

                if (replyContent.contains("热点问题：")) {
                    org.json.JSONObject extraMenu = templateService.extractMenu(replyContent);
                    replyContent = MsgUtils.getH5MenuStr(extraMenu);
                    if (menuMap != null) {
                        menuMap.putAll(extraMenu.toMap());
                    } else {
                        menuMap = extraMenu.toMap();
                    }
                    type = "menu";

                }
                replyContent = replaceContent(replyContent, contextInfo.getAccount());
                ChatHistoryEntity botChatHistory = chatHistoryService.createChatHistory(null, chatHistory.getClientId(), chatHistory.getClientType(), chatHistory.getOrigin(),"bot", "bot", type, null, null,
                        chatHistory.getOrgId(), chatHistory.getBotId(), chatHistory.getSkillGroupId(), chatHistory.getGcid(), chatHistory.getCid(), replyContent, null, extra == null ? null : extra.toMap(), mediaMap, menuMap, null, true);
                if(extra!=null){
                    if(extra.optBoolean("hasOp")){
                        if(!extra.optBoolean("blockOp")){
                            toCs(wxMpService, chatHistory.getClientId(), chatHistory.getOrigin(), contextInfo);
                            return true;
                        }else {
                            botChatHistory.setContent(botChatHistory.getContent()+"\n\uD83D\uDC49"+MsgUtils.msgMenuToText(Collections.singletonList(new WxMpKefuMessage.MsgMenu(MsgMenuPrefix.CS_PREFIX + DateFormatUtils.format(new Date(),"yyyyMMdd"),"人工客服"))));
                        }
                    }else if(extra.optBoolean("hasFeedback")){
                        //TODO
                    }
                    if(extra.optInt("solve")!=-1){
                        response = true;
                    }
                }
                wechatMessageService.sendWechatMsg(wxMpService, botChatHistory.getClientId(), botChatHistory);
                
                if (isNew){
                    org.json.JSONObject menu = initEventService.queryUserStateAndRtHotQuestions(contextInfo.getAccount(), contextInfo.getSkillGroupId());
                    if (Objects.nonNull(menu)) {
                        String content = MsgUtils.getH5MenuStr(menu);
                        ChatHistoryEntity botChatHistory2 = chatHistoryService.createChatHistory(null, chatHistory.getClientId(), chatHistory.getClientType(), chatHistory.getOrigin(), "bot", "bot", "menu", null, null,
                                chatHistory.getOrgId(), chatHistory.getBotId(), chatHistory.getSkillGroupId(), chatHistory.getGcid(), chatHistory.getCid(), content, null, extra == null ? null : extra.toMap(), mediaMap, menu.toMap(), null, true);
                        wechatMessageService.sendWechatMsg(wxMpService, botChatHistory2.getClientId(), botChatHistory2);
                    }
                }
            }
        }
        return response;
    }


    private String replaceContent(String content, String account) {
        return templateService.replacePlaceholders(content, account);
    }

    protected boolean sendSatisfaction(int clientType, String clientId, Integer botId, int level) {
        if(level<1||level>5){
            return false;
        }
        SessionListEntity sessionList = sessionListRepo.findFirstByClientIdAndClientTypeIdAndBotIdOrderByIdDesc(clientId, clientType, botId);
        if(sessionList!=null){
            SatisfactionDataEntity satisfactionData = sessionList.getSatisfactionData();
            if(satisfactionData==null){
                manualChatService.satisfactionData(sessionList, SatisfactionDataEntity.TYPE_CUSTOMER);
            }
            if(satisfactionData.getStatus()==SatisfactionDataEntity.STATUS_TREATED){
                wechatMessageService.sendWechatTextMsg(clientId, botId,"您已对当前服务进行过评价");
            }else if(DateUtils.addMinutes(satisfactionData.getCreateTime(),30).before(new Date())){
                wechatMessageService.sendWechatTextMsg(clientId, botId,"已超过评价时间");
            }else{
                satisfactionData.setStatus(SatisfactionDataEntity.STATUS_TREATED);
                satisfactionData.setUpdateTime(new Date());
                satisfactionData.setLevel(6-level);
                satisfactionDataRepo.save(satisfactionData);
                sessionListRepo.updateSatisfactionLevelById(sessionList.getId(), satisfactionData.getLevel());
                complexChatHistoryRepo.updateSatisfactionBySessionKey(sessionList.getSessionKey(), satisfactionData.getLevel(), satisfactionData.getLabels(), satisfactionData.getContent());
                wechatMessageService.sendWechatTextMsg(clientId, botId,"感谢您的评价");
                csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), Arrays.asList(new SocketPacketModel(sessionList.getSessionKey(), null, SocketEvent.EVENT,
                        new org.json.JSONObject().put("type","satisfaction:commit").put("content","客户已评价")
                                .put("manual",new org.json.JSONObject().put("sessionKey",sessionList.getSessionKey()).put("appraiseLevel",satisfactionData.getLevel()).put("appraiseStatus",satisfactionData.getStatus())).toString())));
            }
            return true;
        }
        return false;
    }
}
