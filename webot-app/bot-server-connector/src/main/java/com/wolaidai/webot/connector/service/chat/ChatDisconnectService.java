package com.wolaidai.webot.connector.service.chat;

import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.DataProcessService;
import com.wolaidai.webot.connector.service.business.KeyExpireService;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@AllArgsConstructor
public class ChatDisconnectService extends DataProcessService {

    private final StringRedisTemplate csStringRedisTemplate;
    private final KeyExpireService keyExpireService;

    @Override
    protected List<SocketPacketModel> processData(MessageModel messageModel) {
        String socketKey = String.format(RedisKey.CS_SOCKET_KEY, messageModel.getSocketId());
        HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
        Map<String, String> socketData = hashOperations.entries(socketKey);
        if(!CollectionUtils.isEmpty(socketData)){
            String orgId = socketData.get("orgId");
            String email = socketData.get("email");
            if(orgId!=null&&email!=null){
                keyExpireService.setExpireKey(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, email),3, TimeUnit.MINUTES);
            }
            csStringRedisTemplate.delete(socketKey);
        }else{
            socketKey = String.format(RedisKey.H5_SOCKET_KEY, messageModel.getSocketId());
            socketData = hashOperations.entries(socketKey);
            if(!CollectionUtils.isEmpty(socketData)){
                String clientId = socketData.get("clientId");
                String botId = socketData.get("botId");
                if(clientId!=null&&botId!=null) {
                    String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, botId);
                    hashOperations.delete(String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, botId),"socketId");
                    String sessionKey = hashOperations.get(conversationKey, "sessionKey");
                    if(sessionKey!=null){
                        keyExpireService.setExpireKey(String.format(RedisKey.CS_CLIENT_STATUS_TIMEOUT,sessionKey),5,TimeUnit.SECONDS);
                    }
                }
                csStringRedisTemplate.delete(socketKey);

            }
        }
        return null;
    }
}
