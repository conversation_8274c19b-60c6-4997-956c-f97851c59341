package com.wolaidai.webot.connector.service.chat;

import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.DataProcessService;
import com.wolaidai.webot.connector.service.event.chat.*;
import com.wolaidai.webot.connector.service.event.chat.inner.*;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class ChatEventService extends DataProcessService {
    @Override
    protected List<SocketPacketModel> processData(MessageModel messageModel) {
        JSONObject data = (JSONObject) messageModel.getData();
        String type = data.getString("type");
        if (Objects.equals(type, SocketEvent.INIT_TYPE)) {
            return applicationContext.getBean(InitEventService.class).process(messageModel);
        } else if(Objects.equals(type, SocketEvent.INQUEUE_TYPE)) {
            return applicationContext.getBean(InqueueEventService.class).process(messageModel);
        } else if(Objects.equals(type, SocketEvent.SESSION_SWITCH_TYPE)) {
            return applicationContext.getBean(SessionSwitchEventService.class).process(messageModel);
        } else if(Objects.equals(type, SocketEvent.SATISFACTION_COMMIT_TYPE)){
            return applicationContext.getBean(SatisfactionCommitEventService.class).process(messageModel);
        } else if(Objects.equals(type, SocketEvent.SATISFACTION_TYPE)) {
            return applicationContext.getBean(SatisfactionEventService.class).process(messageModel);
        } else if(Objects.equals(type, SocketEvent.RECALL_TYPE)) {
            return applicationContext.getBean(RecallEventService.class).process(messageModel);
        } else if(Objects.equals(type, SocketEvent.SESSION_TRANS_TYPE)) {
            return applicationContext.getBean(SessionTransEventService.class).process(messageModel);
        } else if(Objects.equals(type, SocketEvent.SESSION_TRANS_REMAINING_TYPE)){
            return applicationContext.getBean(TransRemainingEventService.class).process(messageModel);
        } else if(Objects.equals(type, SocketEvent.DEQUEUE_TYPE)) {
            return applicationContext.getBean(DequeueEventService.class).process(messageModel);
        } else if (Objects.equals(type, SocketEvent.SESSION_CLOSE_TYPE)) {
            return applicationContext.getBean(SessionCloseEventService.class).process(messageModel);
        } else if (Objects.equals(type, SocketEvent.TYPE_FACE_DETECTION)) {
            return applicationContext.getBean(FaceDetectionService.class).process(messageModel);
        } else if (Objects.equals(type, SocketEvent.INNER_SESSION_INIT_TYPE)) {
            return applicationContext.getBean(SessionInitEventService.class).process(messageModel);
        } else if (Objects.equals(type, SocketEvent.INNER_SESSION_JOIN_TYPE)) {
            return applicationContext.getBean(SessionJoinEventService.class).process(messageModel);
        } else if (Objects.equals(type, SocketEvent.INNER_SESSION_KICK_TYPE)) {
            return applicationContext.getBean(SessionKickEventService.class).process(messageModel);
        } else if (Objects.equals(type, SocketEvent.INNER_SESSION_EXIT_TYPE)) {
            return applicationContext.getBean(SessionExitEventService.class).process(messageModel);
        } else if (Objects.equals(type, SocketEvent.INNER_SESSION_UPD_TYPE)) {
            return applicationContext.getBean(SessionUpdEventService.class).process(messageModel);
        } else if (Objects.equals(type, SocketEvent.INNER_SESSION_CUT_TYPE)) {
            return applicationContext.getBean(SessionCutEventService.class).process(messageModel);
        } else if (Objects.equals(type, SocketEvent.LIKE_COMMIT_TYPE)) {
            return applicationContext.getBean(LikeCommitEventService.class).process(messageModel);
        } else if (Objects.equals(type, SocketEvent.VERIFY_TYPE)) {
            return applicationContext.getBean(VerifyEventService.class).process(messageModel);
        }
        return null;
    }
}
