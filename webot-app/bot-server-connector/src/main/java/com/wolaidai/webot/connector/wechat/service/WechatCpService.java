package com.wolaidai.webot.connector.wechat.service;

import com.google.common.collect.Maps;
import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.wechat.handler.wxe.LogCpHandler;
import com.wolaidai.webot.connector.wechat.handler.wxe.MsgCpHandler;
import com.wolaidai.webot.connector.wechat.utils.MsgUtils;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.entity.WechatWorkEntity;
import com.wolaidai.webot.data.mongodb.repo.WechatWorkRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import me.chanjar.weixin.cp.config.impl.WxCpRedissonConfigImpl;
import me.chanjar.weixin.cp.message.WxCpMessageRouter;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.builder.kefu.WxMsgMenuBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@AllArgsConstructor
public class WechatCpService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    private final ApplicationContext applicationContext;
    private final AppPropertyConfig appPropertyConfig;
    private final WechatWorkRepo wechatWorkRepo;
    private final RedissonClient redissonClient;
    private final StringRedisTemplate csStringRedisTemplate;
    private static Map<Integer, WxCpMessageRouter> routers = Maps.newConcurrentMap();
    private static Map<Integer, WxCpService> cpServices = Maps.newConcurrentMap();

    public synchronized WxCpService getWxCpService(Integer agentId){
        if(agentId==null){
            return null;
        }
        if(!cpServices.containsKey(agentId)){
            WxCpDefaultConfigImpl wxCpConfigStorage = new WxCpRedissonConfigImpl(redissonClient);
            WechatWorkEntity wechatWork = wechatWorkRepo.findByAgentId(agentId.toString());
            if(wechatWork!=null){
                wxCpConfigStorage.setCorpId(wechatWork.getCorpId());
                wxCpConfigStorage.setAgentId(Integer.valueOf(wechatWork.getAgentId()));
                wxCpConfigStorage.setCorpSecret(wechatWork.getSecret());
                wxCpConfigStorage.setToken(wechatWork.getToken());
                wxCpConfigStorage.setAesKey(wechatWork.getEncodingAESKey());
                WxCpService wxCpService = new WxCpServiceImpl();
                wxCpService.setWxCpConfigStorage(wxCpConfigStorage);
                cpServices.put(wxCpConfigStorage.getAgentId(), wxCpService);
                routers.put(wxCpConfigStorage.getAgentId(), this.newRouter(wxCpService));
            }
        }
        return cpServices.get(agentId);
    }

    public WxCpMessageRouter newRouter(WxCpService wxCpService) {
        if(wxCpService==null){
            return null;
        }
        final WxCpMessageRouter newRouter = new WxCpMessageRouter(wxCpService);
        // 记录所有事件的日志 （异步执行）
        newRouter.rule().handler(applicationContext.getBean(LogCpHandler.class)).next();
        // 默认
        newRouter.rule().async(false).matcher(message -> StringUtils.equalsAny(message.getMsgType(), WxConsts.XmlMsgType.TEXT, WxConsts.XmlMsgType.IMAGE, WxConsts.XmlMsgType.VIDEO, WxConsts.XmlMsgType.VOICE)).handler(applicationContext.getBean(MsgCpHandler.class)).end();
        return newRouter;
    }

    public WxCpMessageRouter getRouter(Integer agentId){
        return routers.get(agentId);
    }

    public void sendWechatTextMsg(WxCpService wxCpService, String clientId,String content){
        if(wxCpService!=null){
            try {
                String text = MsgUtils.handleText(content);
                if(StringUtils.isNotBlank(text)) {
                    wxCpService.getMessageService().send(WxCpMessage.TEXT().content(text).toUser(clientId).build());
                }
            } catch (Exception e) {
                LOGGER.error("发送企业微信文本消息失败,agentId:{}",wxCpService.getWxCpConfigStorage().getAgentId(),e);
            }
        }else{
            LOGGER.error("找不到指定wxCpService");
        }
    }
    public void sendWechatTextMsg(String clientId,Integer botId,String content){
        String agentId = csStringRedisTemplate.opsForValue().get(String.format(RedisKey.WECHAT_CLIENTID_BOTID_AGENTID, clientId, botId));
        if(agentId==null){
            return;
        }
        WxCpService wxCpService = getWxCpService(Integer.valueOf(agentId));
        sendWechatTextMsg(wxCpService,clientId,content);
    }

    public  void sendWechatMsg(WxCpService wxCpService, String clientId, ChatHistoryEntity chatHistory) throws Exception {
        if(Objects.equals(chatHistory.getType(),"text")){
            String text = MsgUtils.handleText(chatHistory.getContent());
            if(StringUtils.isNotBlank(text)) {
                wxCpService.getMessageService().send(WxCpMessage.TEXT().content(text).toUser(clientId).build());
            }
        }else if(Objects.equals(chatHistory.getType(),"image")){
            WxMediaUploadResult wxMediaUploadResult = wxCpService.getMediaService().upload(WxConsts.MediaFileType.IMAGE, "jpg", new ByteArrayInputStream(HttpClientUtil.getFile(getMediaUrl(chatHistory.getContent()))));
            wxCpService.getMessageService().send(WxCpMessage.IMAGE().mediaId(wxMediaUploadResult.getMediaId()).toUser(clientId).build());
        }else if(Objects.equals(chatHistory.getType(),"voice")){
            WxMediaUploadResult wxMediaUploadResult = wxCpService.getMediaService().upload(WxConsts.MediaFileType.VOICE, "mp3", new ByteArrayInputStream(HttpClientUtil.getFile(getMediaUrl(chatHistory.getContent()))));
            wxCpService.getMessageService().send(WxCpMessage.VOICE().mediaId(wxMediaUploadResult.getMediaId()).toUser(clientId).build());
        }else if(Objects.equals(chatHistory.getType(),"video")){
            WxMediaUploadResult wxMediaUploadResult = wxCpService.getMediaService().upload(WxConsts.MediaFileType.VIDEO, "mp4", new ByteArrayInputStream(HttpClientUtil.getFile(getMediaUrl(chatHistory.getContent()))));
            wxCpService.getMessageService().send(WxCpMessage.VIDEO().mediaId(wxMediaUploadResult.getMediaId()).toUser(clientId).build());
        }else if(Objects.equals(chatHistory.getType(),"menu")){
            Map<String, Object> menu = chatHistory.getMenu();
            WxMsgMenuBuilder wxMsgMenuBuilder = WxMpKefuMessage.MSGMENU().headContent(MsgUtils.handleText(Objects.toString(menu.get("head_content")))).tailContent(MsgUtils.handleText(Objects.toString(menu.get("tail_content")))).toUser(clientId);
            List<Object> list = (List<Object>) menu.get("list");
            for (int j = 0; j < list.size(); j++) {
                Map<String,Object> menuItem = (Map<String, Object>) list.get(j);
                wxMsgMenuBuilder.addMenus(new WxMpKefuMessage.MsgMenu(Objects.toString(menuItem.get("id")), (String) menuItem.get("content")));
            }
            sendWechatMenuMsg(wxCpService,wxMsgMenuBuilder);
        }
    }

    public void sendWechatMsg(String clientId,Integer botId,ChatHistoryEntity chatHistory){
        String agentId = csStringRedisTemplate.opsForValue().get(String.format(RedisKey.WECHAT_CLIENTID_BOTID_AGENTID, clientId, botId));
        if(agentId!=null) {
            WxCpService wxCpService = getWxCpService(Integer.valueOf(agentId));
            if (wxCpService != null) {
                try {
                    sendWechatMsg(wxCpService, clientId, chatHistory);
                } catch (Exception e) {
                    LOGGER.error("发送微信消息失败,agentId:{}", wxCpService.getWxCpConfigStorage().getAgentId(), e);
                }
            } else {
                LOGGER.error("找不到指定agentId:{}", agentId);
            }
        }else{
            LOGGER.error("找不到指定agentId");
        }
    }

    public void sendWechatMenuMsg(String clientId,Integer botId, WxMsgMenuBuilder wxMsgMenuBuilder){
        String agentId = csStringRedisTemplate.opsForValue().get(String.format(RedisKey.WECHAT_CLIENTID_BOTID_AGENTID, clientId, botId));
        if(agentId!=null) {
            WxCpService wxCpService = getWxCpService(Integer.valueOf(agentId));
            if (wxCpService != null) {
                try {
                    sendWechatMenuMsg(wxCpService, wxMsgMenuBuilder);
                } catch (Exception e) {
                    LOGGER.error("发送企业微信菜单消息失败,agentId:{}", agentId, e);
                }
            } else {
                LOGGER.error("找不到指定agentId:{}", agentId);
            }
        }else{
            LOGGER.error("找不到指定agentId");
        }
    }

    public void sendWechatMenuMsg(WxCpService wxCpService, WxMsgMenuBuilder wxMsgMenuBuilder) throws WxErrorException {
        WxMpKefuMessage kefuMessage = wxMsgMenuBuilder.build();
        String text = MsgUtils.handleText(kefuMessage.getHeadContent())+"\n";
        for (WxMpKefuMessage.MsgMenu msgMenu : kefuMessage.getMsgMenus()) {
            text +=msgMenu.getContent()+"\n";
        }
        text+=MsgUtils.handleText(kefuMessage.getTailContent());
        wxCpService.getMessageService().send(WxCpMessage.TEXT().content(text.trim()).toUser(kefuMessage.getToUser()).build());
    }

    private String getMediaUrl(String mediaId){
        int materialType = 1;
        if(NumberUtils.isDigits(mediaId)){
            materialType = 2;
        }
        return appPropertyConfig.getFileServerUrl()+"/"+materialType+"/"+mediaId;
    }

    public boolean isWorkWechat(String clientId,Integer botId){
        return csStringRedisTemplate.hasKey(String.format(RedisKey.WECHAT_CLIENTID_BOTID_AGENTID, clientId, botId));
    }

}
