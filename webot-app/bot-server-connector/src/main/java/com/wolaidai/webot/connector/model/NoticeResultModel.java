package com.wolaidai.webot.connector.model;

import com.wolaidai.webot.data.mysql.entity.bot.NoticeEntity;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class NoticeResultModel extends BaseModel {
    private String content;
    private boolean enable;
    private Date startTime;
    private Date endTime;

    public NoticeResultModel(NoticeEntity n) {
        this.content = n.getContent();
        this.startTime = n.getStartTime();
        this.endTime = n.getEndTime();
        this.enable = Objects.equals(n.getStatus(), 1);
    }
}
