package com.wolaidai.webot.connector.service.event.chat.inner;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.constant.ManualConfigConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.constant.SocketNamespace;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.model.SocketRoomModel;
import com.wolaidai.webot.connector.model.inner.SessionExitModel;
import com.wolaidai.webot.connector.service.SocketService;
import com.wolaidai.webot.connector.service.business.ChatRecordService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.data.mysql.entity.chat.*;
import com.wolaidai.webot.data.mysql.repo.ChatMembersSnapshotRepo;
import com.wolaidai.webot.data.mysql.repo.ChatRoomRepo;
import com.wolaidai.webot.data.mysql.repo.ChatSessionRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class SessionExitEventService extends BaseEventService {

    private final StringRedisTemplate csStringRedisTemplate;
    private final ChatSessionRepo chatSessionRepo;
    private final ChatRoomRepo chatRoomRepo;
    private final SocketService socketService;
    private final ChatRecordService chatRecordService;
    private final ChatMembersSnapshotRepo chatMembersSnapshotRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject)model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        SessionExitModel sessionModel = JSON.parseObject(data.toString(), SessionExitModel.class);
        if (ManualConfigConstants.WORKBENCH_TYPE.equals(sessionModel.getSource())) {
            Integer sessionId = sessionModel.getSessionKey();
            String from = sessionModel.getEmail();
            if (sessionId == null || StringUtils.isBlank(from)) {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret",-1)
                            .put("msg", "param lost").put("timestamp",System.currentTimeMillis()));
                }
                return null;
            }
            ChatSessionEntity sessionEntity = chatSessionRepo.findById(sessionId).orElse(null);
            if (sessionEntity == null) {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret",-1)
                            .put("msg", "群聊会话已删除").put("timestamp",System.currentTimeMillis()));
                }
                return null;
            }
            Integer orgId = sessionEntity.getOrgId();
            ChatRoomEntity chatRoomEntity = sessionEntity.getRoom();
            if (chatRoomEntity == null) {
                if (callback != null ) {
                    callback.sendAcknowledgement(new JSONObject().put("ret",-1)
                            .put("msg", "该群聊已不存在").put("timestamp",System.currentTimeMillis()));
                }
                return null;
            }
            Date dateNow = new Date();
            HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
            String eventMsg = String.format("%s %s退出了群聊", ObjectUtils.defaultIfNull(hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, from), "nickName"), ""),
                    ObjectUtils.defaultIfNull(hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, from), "name"), ""));
            //保存聊天室信息
            chatRoomEntity.getRoomUsers().removeIf(c -> from.equals(c.getUser().getEmail()));
            //群主退出,顺位继承
            if (Objects.equals(from, chatRoomEntity.getCreator())) {
                chatRoomEntity.setCreator(chatRoomEntity.getRoomUsers()
                        .iterator().next().getUser().getEmail());
            }
            //保存群聊成员变动信息
            chatRoomEntity.setUpdateTime(dateNow);
            chatRoomRepo.save(chatRoomEntity);
            ChatRecordEntity chatRecord = chatRecordService.createChatRecord(null, from, null, eventMsg, ChatRecordEntity.EVENT_TYPE,
                    sessionEntity, chatMembersSnapshotRepo.findFirstBySessionIdOrderByIdDesc(sessionId), null, new com.alibaba.fastjson.JSONObject().fluentPut("sessionKey", sessionId));
            //保会话成员快照信息
            ChatMembersSnapshotEntity snapshotEntity =
                    new ChatMembersSnapshotEntity(sessionId, chatRoomEntity.getRoomUsers().stream().map(ChatRoomUsersEntity::getUser)
                            .map(UserStateEntity::getEmail).collect(Collectors.toList()));
            chatMembersSnapshotRepo.save(snapshotEntity);
            String socketId = model.getSocketId();
            String msg = MessageUtil.toJsonString(chatRecord);
            models.add(new SocketPacketModel(null, socketId, SocketEvent.INFO, msg));
            models.add(new SocketPacketModel("session_" + sessionModel.getSessionKey(), socketId, SocketEvent.INFO, msg));
            //退出聊天室
            socketService.joinOrLeaveGlobalRoom(SocketNamespace.CHAT, SocketRoomModel.REMOVE_TYPE, Collections.singletonList("session_" + sessionId), socketId);
            //保存会话信息
            sessionEntity.setLastMsg(eventMsg);
            sessionEntity.setLastMsgTime(dateNow);
            sessionEntity.setLastMsgSender(from);
            sessionEntity.setUpdateTime(dateNow);
            chatSessionRepo.save(sessionEntity);
        }
        return models;
    }
}
