package com.wolaidai.webot.connector.controller;

import com.wolaidai.webot.connector.constant.WebStatusConstants;
import com.wolaidai.webot.connector.model.ResponseModel;
import com.wolaidai.webot.connector.service.business.QualityInspectionService;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

@AllArgsConstructor
@Api(tags = "质量检查接口")
@RestController
@RequestMapping("/quality-inspection")
public class QualityInspectionController extends BaseController {

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());
    private final QualityInspectionService qualityInspectionService;
    private final SessionListRepo sessionListRepo;

    @PostMapping("/callback")
    @ApiOperation(value = "质量检查完成回调")
    public ResponseModel qualityInspectionCallback(@RequestBody CallbackRequest request) {
        try {
            LOGGER.info("收到质量检查回调, taskId:{}, status:{}, sessionKey:{}", 
                request.getTaskId(), request.getStatus(), request.getSessionKey());

            // 参数验证
            if (StringUtils.isBlank(request.getTaskId())) {
                LOGGER.error("质量检查回调参数错误: taskId为空");
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "taskId不能为空");
            }

            if (StringUtils.isBlank(request.getSessionKey())) {
                LOGGER.error("质量检查回调参数错误: sessionKey为空, taskId:{}", request.getTaskId());
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "sessionKey不能为空");
            }

            if (request.getStatus() == null) {
                LOGGER.error("质量检查回调参数错误: status为空, taskId:{}", request.getTaskId());
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "status不能为空");
            }

            // 验证会话是否存在
            SessionListEntity sessionList = sessionListRepo.findBySessionKey(request.getSessionKey());
            if (sessionList == null) {
                LOGGER.error("质量检查回调会话不存在, sessionKey:{}, taskId:{}", 
                    request.getSessionKey(), request.getTaskId());
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话不存在");
            }

            // 验证taskId是否匹配
            if (!request.getTaskId().equals(sessionList.getQualityInspectionTaskId())) {
                LOGGER.error("质量检查回调taskId不匹配, sessionKey:{}, callbackTaskId:{}, dbTaskId:{}", 
                    request.getSessionKey(), request.getTaskId(), sessionList.getQualityInspectionTaskId());
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "taskId不匹配");
            }

            // 检查当前状态，避免重复处理
            if (sessionList.getQualityInspectionStatus() != null && sessionList.getQualityInspectionStatus() == 2) {
                LOGGER.info("质量检查已完成，跳过重复处理, sessionKey:{}, taskId:{}", 
                    request.getSessionKey(), request.getTaskId());
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "处理成功");
            }

            // 根据回调状态处理
            if (request.getStatus() == 1) { // 1表示检查完成
                // 异步查询检查结果并更新数据库
                qualityInspectionService.queryQualityInspectionResult(request.getTaskId(), request.getSessionKey());
                LOGGER.info("质量检查完成回调处理成功, sessionKey:{}, taskId:{}", 
                    request.getSessionKey(), request.getTaskId());
            } else if (request.getStatus() == 2) { // 2表示检查失败
                // 更新状态为检查失败
                qualityInspectionService.updateQualityInspectionStatus(sessionList.getId(), 3, request.getTaskId());
                LOGGER.info("质量检查失败回调处理成功, sessionKey:{}, taskId:{}", 
                    request.getSessionKey(), request.getTaskId());
            } else {
                LOGGER.warn("未知的质量检查回调状态, sessionKey:{}, taskId:{}, status:{}", 
                    request.getSessionKey(), request.getTaskId(), request.getStatus());
            }

            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "处理成功");

        } catch (Exception e) {
            LOGGER.error("处理质量检查回调发生错误, taskId:{}, sessionKey:{}, error:{}", 
                request.getTaskId(), request.getSessionKey(), e.getMessage(), e);
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "处理失败");
        }
    }

    /**
     * 回调请求参数
     */
    public static class CallbackRequest {
        private String taskId;      // 质量检查任务ID
        private String sessionKey; // 会话标识
        private Integer status;     // 检查状态：1-完成，2-失败

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public String getSessionKey() {
            return sessionKey;
        }

        public void setSessionKey(String sessionKey) {
            this.sessionKey = sessionKey;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }
}
