package com.wolaidai.webot.connector.utils;

import com.wolaidai.webot.connector.constant.CryptKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;

public class CryptUtils {
    private final static Logger LOGGER = LoggerFactory.getLogger(CryptUtils.class);
    private static SecretKeySpec key;
    static {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");// 创建AES的Key生产者
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            // 设置 密钥key的字节数组 作为安全随机数生成器的种子
            random.setSeed(CryptKey.DEFAULT_KEY.getBytes());
            kgen.init(128, random);// 利用用户密码作为随机数初始化出
            SecretKey secretKey = kgen.generateKey();// 根据用户密码，生成一个密钥
            byte[] enCodeFormat = secretKey.getEncoded();// 返回基本编码格式的密钥
            key = new SecretKeySpec(enCodeFormat, "AES");// 转换为AES专用密钥
        }catch (Exception e){
            LOGGER.error("generate secret key error",e);
        }
    }
    public static byte[] encryptAes(byte[] data) {
        try {
            Cipher cipher = Cipher.getInstance("AES");// 创建密码器
            cipher.init(Cipher.ENCRYPT_MODE, key);// 初始化为加密模式的密码器
            return cipher.doFinal(data);// 加密
        }catch (Exception e){
            LOGGER.error("encrypt aes error",e);
            return null;
        }
    }

    public static byte[] decryptAes(byte[] data) {
        try {
            Cipher cipher = Cipher.getInstance("AES");// 创建密码器
            cipher.init(Cipher.DECRYPT_MODE, key);// 初始化为解密模式的密码器
            return cipher.doFinal(data);
        }catch (Exception e){
            LOGGER.error("decrypt aes error",e);
            return null;
        }
    }
}
