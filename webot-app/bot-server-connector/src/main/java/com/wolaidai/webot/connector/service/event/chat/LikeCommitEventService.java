package com.wolaidai.webot.connector.service.event.chat;

import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.repo.ChatHistoryRepo;
import com.wolaidai.webot.data.mongodb.repo.ComplexChatHistoryRepo;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;

import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@AllArgsConstructor
public class LikeCommitEventService extends BaseEventService {

    private final ComplexChatHistoryRepo complexChatHistoryRepo;
    private final ChatHistoryRepo chatHistoryRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject) model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        String msgId = data.getString("msgId");
        Integer like = data.getInt("like");
        if(!Arrays.asList(0,1).contains(like)){
            if(callback!=null) {
                callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", "参数错误").put("timestamp", System.currentTimeMillis()));
            }
            return models;
        }

        ChatHistoryEntity history = chatHistoryRepo.findByMsgId(msgId);
        if(history == null || history.getExtend()==null || history.getExtend().get("docQa")==null || (Integer)history.getExtend().get("docQa")!=1 
            || history.getExtend().get("like")!=null) {
            if(callback!=null) {
                callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", "无法评价该消息").put("timestamp", System.currentTimeMillis()));
            }
            return models;
        } 
        complexChatHistoryRepo.updateLikeById(history.getId(), like);
        if(callback!=null){
            callback.sendAcknowledgement(new JSONObject().put("ret", 0).put("msg", "操作成功").put("timestamp", System.currentTimeMillis()));
        }
        return models;
    }
}
