package com.wolaidai.webot.connector.model;

import com.wolaidai.webot.data.mysql.entity.bot.QaEntity;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class QuestionRowModel extends BaseModel {
    private String question;
    private List<QASimilarRowModel> qa_similars;

    public QuestionRowModel(QaEntity q) {
        this.question = q.getQuestion();
        this.qa_similars = q.getSimilarQuestions().stream().filter(i -> !i.getQuestion().contains("我来贷")).map(i -> new QASimilarRowModel(i)).collect(Collectors.toList());
    }

}
