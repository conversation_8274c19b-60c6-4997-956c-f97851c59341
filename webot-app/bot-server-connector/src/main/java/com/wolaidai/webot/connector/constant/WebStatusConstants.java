package com.wolaidai.webot.connector.constant;


public interface WebStatusConstants {
    int RESPONSE_CODE_SUCCESS = 0;
    int RESPONSE_CODE_FAILURE = 1;
    int RESPONSE_CODE_RESOURCE_NOT_EXIST = 2;
    int RESPONSE_CODE_ANONYMOUS = 10;
    int RESPONSE_CODE_ACCESS_DENIED = 20;
    int RESPONSE_CODE_FACE_TOKEN_EXPIRED = 30;
    int RESPONSE_CODE_FACE_DETECTION_FAILURE_TIMES_LIMIT = 31;
    int RESPONSE_CODE_FACE_DETECTION_UNNECESSARY = 32;
    int RESPONSE_CODE_FACE_DETECTION_MISSING = 33;
}
