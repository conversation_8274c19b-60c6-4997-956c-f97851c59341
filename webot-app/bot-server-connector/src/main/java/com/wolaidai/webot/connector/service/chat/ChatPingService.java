package com.wolaidai.webot.connector.service.chat;

import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.DataProcessService;
import io.socket.socketio.server.SocketIoSocket;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChatPingService extends DataProcessService {
    @Override
    protected List<SocketPacketModel> processData(MessageModel messageModel) {
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = messageModel.getCallback();
        if(callback!=null){
            callback.sendAcknowledgement("pong");
        }
        return null;
    }
}
