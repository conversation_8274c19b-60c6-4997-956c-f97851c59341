package com.wolaidai.webot.connector.constant;


/**
 * 用户状态枚举
 * <AUTHOR> 
 */
public enum UserStateEnum {
	NORMAL("1","注册用户"),
	OVERDUE("2","逾期用户"),
	TRANSFER_OR_FASU("3","债转用户"),
	VIP("4","会员业务"),
	NOT_REGISTER("5","非注册用户"),
	;
	public String code;

	public String msg;

	UserStateEnum(String code, String msg) {
		this.code = code;
		this.msg = msg;
	}
	
	public static UserStateEnum valueOfCode(String code) {
		for (UserStateEnum userStateEnum : UserStateEnum.values()) {
			if (userStateEnum.getCode().equals(code)) {
				return userStateEnum;
			}
		}
		return null;
	}

	public String getCode() {
		return code;
	}

	public String getMsg() {
		return msg;
	}
}
