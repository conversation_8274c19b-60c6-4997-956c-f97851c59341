package com.wolaidai.webot.connector.service.business;

import com.wolaidai.webot.connector.service.BaseService;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service
public class KeyExpireService extends BaseService {
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private StringRedisTemplate csStringRedisTemplate;
    private ExpireProcessService expireProcessService;

    @Autowired
    public void setExpireProcessService(@Lazy ExpireProcessService expireProcessService) {
        this.expireProcessService = expireProcessService;
    }

    public void setExpireKey(String value, long timestamp){
        ZSetOperations<String, String> zSetOperations = csStringRedisTemplate.opsForZSet();
        zSetOperations.add(RedisKey.EXPIRE_KEY_ZSET, value,timestamp);
    }

    public void setExpireKey(String value, long timeout, TimeUnit unit){
        ZSetOperations<String, String> zSetOperations = csStringRedisTemplate.opsForZSet();
        zSetOperations.add(RedisKey.EXPIRE_KEY_ZSET, value, System.currentTimeMillis()+unit.toMillis(timeout));
    }

    public void removeExpireKey(String... values){
        ZSetOperations<String, String> zSetOperations = csStringRedisTemplate.opsForZSet();
        zSetOperations.remove(RedisKey.EXPIRE_KEY_ZSET, values);
    }

    public long getExpireTime(String value){
        ZSetOperations<String, String> zSetOperations = csStringRedisTemplate.opsForZSet();
        Double score = zSetOperations.score(RedisKey.EXPIRE_KEY_ZSET, value);
        if(score!=null&&score>0){
            return score.longValue()-System.currentTimeMillis();
        }
        return 0;
    }

    public boolean hasExpireKey(String value){
        ZSetOperations<String, String> zSetOperations = csStringRedisTemplate.opsForZSet();
        return zSetOperations.score(RedisKey.EXPIRE_KEY_ZSET, value)!=null;
    }

    @Async
    public void checkExpireKey(){
        ZSetOperations<String, String> zSetOperations = csStringRedisTemplate.opsForZSet();
        while (true){
            RLock lock = redissonClient.getLock(String.format(RedisKey.LOCK_KEY, "checkExpireKey"));
            try {
                boolean res = lock.tryLock(0, 3, TimeUnit.SECONDS);
                if (res) {
                    Set<ZSetOperations.TypedTuple<String>> set = zSetOperations.rangeByScoreWithScores(RedisKey.EXPIRE_KEY_ZSET, 0, System.currentTimeMillis());
                    for (ZSetOperations.TypedTuple<String> v : set) {
                        long now = System.currentTimeMillis();
                        if (now >= v.getScore()) {
                            LOGGER.info("checkExpireKey,value:{},delay:{}ms", v.getValue(), (now - v.getScore()));
                            zSetOperations.remove(RedisKey.EXPIRE_KEY_ZSET, v.getValue());
                            expireProcessService.processKeyExpireEvent(v.getValue());
                        }
                    }
                }
            }catch (Exception e){
                LOGGER.error("checkExpireKey error",e);
            }finally {
                if(lock.isHeldByCurrentThread()){
                    lock.unlock();
                }
            }
            try {
                TimeUnit.MILLISECONDS.sleep(500);
            } catch (InterruptedException e) {
            }
        }
    }


}
