package com.wolaidai.webot.connector.service.event.chat;

import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@AllArgsConstructor
public class SessionSwitchEventService extends BaseEventService {

    private final StringRedisTemplate csStringRedisTemplate;

    private final SessionListRepo sessionListRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        JSONObject data = (JSONObject)model.getData();
        JSONObject manual = data.getJSONObject("manual");
        String sessionKey = manual.getString("sessionKey");
        String email = manual.getString("email");
        SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
        if(sessionList!=null) {
            csStringRedisTemplate.opsForHash().put(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, sessionList.getOrgId(), email), "currentSessionKey", sessionKey);
            sessionListRepo.updateUnreadMsgCountBySessionKey(sessionKey,0,new Date());
        }
        return null;
    }
}
