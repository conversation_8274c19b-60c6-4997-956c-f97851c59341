package com.wolaidai.webot.connector.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializeFilter;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;

public class MessageUtil {
    private static SerializeFilter[] filters;

    static {
        SimplePropertyPreFilter simplePropertyPreFilter = new SimplePropertyPreFilter();
        simplePropertyPreFilter.getExcludes().addAll(Lists.newArrayList("id", "session", "snapshot"));
        filters = new SerializeFilter[]{simplePropertyPreFilter};
    }

    public static String toJsonString(Object o) {
        return JSON.toJSONString(o, filters);
    }

    public static String escapeHtml(String content, String messageType) {
        content = Jsoup.clean(content, Whitelist.none());
        if("text".equals(messageType)){
            return StringUtils.substring(content,0,100);
        } else if ("image".equals(messageType)) {
            return "[图片]";
        } else if ("voice".equals(messageType)) {
            return "[语音]";
        } else if ("video".equals(messageType)) {
            return "[视频]";
        } else if ("file".equals(messageType)) {
            return "[文件]";
        } else if ("recall".equals(messageType)) {
            return "你撤回了一条消息";
        }
        return StringUtils.substring(content,0,100);
    }
}
