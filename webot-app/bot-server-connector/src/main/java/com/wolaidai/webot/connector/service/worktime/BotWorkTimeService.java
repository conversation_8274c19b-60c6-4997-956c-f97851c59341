package com.wolaidai.webot.connector.service.worktime;

import java.util.Date;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.connector.service.BaseService;
import com.wolaidai.webot.connector.service.business.ManualChatService;

import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class BotWorkTimeService extends BaseService {

    private final ManualChatService manualChatService;

    public boolean isWorkTime(int orgId){
        Date now = new Date();
        org.json.JSONObject config = manualChatService.getBotWorkTimeConfig(orgId);
        if (null != config) {
            JSONObject configJson = JSON.parseObject(config.toString());
            JSONArray specificArray = configJson.getJSONArray("specific");
            if(!CollectionUtils.isEmpty(specificArray)){
                for (int i = 0; i < specificArray.size(); i++) {
                    JSONObject specificJson = specificArray.getJSONObject(i);
                    Date startTime = specificJson.getDate("startTime");
                    Date endTime = specificJson.getDate("endTime");
                    if(startTime!=null&&endTime!=null&&now.getTime()>=startTime.getTime()&&now.getTime()<endTime.getTime()){
                        return true;
                    }
                }
            }
            JSONArray holidaysArray = configJson.getJSONArray("holidays");
            if(!CollectionUtils.isEmpty(holidaysArray)){
                for (int i = 0; i < holidaysArray.size(); i++) {
                    JSONObject holidayJson = holidaysArray.getJSONObject(i);
                    Date startTime = holidayJson.getDate("startTime");
                    Date endTime = holidayJson.getDate("endTime");
                    if(startTime!=null&&endTime!=null&&now.getTime()>=startTime.getTime()&&now.getTime()<endTime.getTime()){
                        return false;
                    }
                }
            }
            JSONObject workdaysJson = configJson.getJSONObject("workdays");
            JSONArray days = workdaysJson.getJSONArray("days");
            for (int i = 0; i < days.size(); i++) {
                if(days.getIntValue(i)==now.getDay()){
                    String startTime = workdaysJson.getString("startTime");
                    String endTime = workdaysJson.getString("endTime");
                    if(startTime!=null&&endTime!=null){
                        String time = DateFormatUtils.format(now, "HH:mm");
                        if(time.compareTo(startTime)>=0&&time.compareTo(endTime)<0){
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
}
