package com.wolaidai.webot.connector.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.connector.callback.SocketCallback;
import com.wolaidai.webot.connector.constant.RedisConstants;
import com.wolaidai.webot.connector.constant.SocketNamespace;
import com.wolaidai.webot.connector.handler.SocketServerWrapper;
import com.wolaidai.webot.connector.listener.ChatConnectSocketListener;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.SocketService;
import com.wolaidai.webot.connector.service.business.InitConfigService;
import com.wolaidai.webot.connector.service.business.KeyExpireService;
import com.wolaidai.webot.connector.service.message.MessageReceiverService;
import com.wolaidai.webot.connector.utils.SocketContextUtils;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.engineio.server.Emitter;
import io.socket.socketio.server.SocketIoNamespace;
import io.socket.socketio.server.SocketIoServer;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.RedisSerializer;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@AllArgsConstructor
@Configuration
public class EngineIoConfig implements ApplicationRunner{

    private static final Logger LOGGER = LoggerFactory.getLogger(EngineIoConfig.class);

    private final AppPropertyConfig appPropertyConfig;

    private final RedisTemplate csRedisTemplate;

    private final StringRedisTemplate csStringRedisTemplate;

    private final SocketService socketService;

    private final MessageReceiverService messageReceiverService;

    private final ApplicationContext applicationContext;
    private final KeyExpireService keyExpireService;

    private final InitConfigService initConfigService;

    @Bean
    public SocketServerWrapper socketServerWrapper() {
        return new SocketServerWrapper("0.0.0.0", appPropertyConfig.getSocketPort(), appPropertyConfig.getSocketContextPath(), appPropertyConfig.getSocketRequestPath(), null);
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        SocketServerWrapper serverWrapper = socketServerWrapper();
        try {
            serverWrapper.startServer();
            LOGGER.info("Socket server started.");
        } catch (Exception e) {
            LOGGER.info("Socket server start error.", e);
        }
        if(appPropertyConfig.getCheckMsgFlag()==1) {
            messageReceiverService.getMsg();
        }
        SocketIoServer server = serverWrapper.getSocketIoServer();
        ChatConnectSocketListener chatConnectSocketListener = applicationContext.getBean(ChatConnectSocketListener.class);
        addNamespace(server, SocketNamespace.CHAT, chatConnectSocketListener);
        keyExpireService.checkExpireKey();
        initConfigService.initConfig();
    }

    private void addNamespace(SocketIoServer server, String namespace, Emitter.Listener listener) {
        SocketIoNamespace ns = server.namespace(namespace);
        ns.on("connection", listener);
    }

    @Bean
    public RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addMessageListener((message, pattern) -> {
            String channel = new String(message.getChannel());
            if(channel.startsWith(StringUtils.substringBeforeLast(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, ":"))){
                String namespace = StringUtils.substringAfterLast(channel, ":");
                RedisSerializer<List<SocketPacketModel>> valueSerializer = csRedisTemplate.getValueSerializer();
                List<SocketPacketModel> socketPacketModel = valueSerializer.deserialize(message.getBody());
                LOGGER.info("receive msg from channel:{},data:{}",channel,JSON.toJSONString(socketPacketModel));
                for (SocketPacketModel model : socketPacketModel) {
                    socketService.sendMessage(namespace,model);
                }
            }else if(channel.startsWith(StringUtils.substringBeforeLast(RedisConstants.SOCKET_RESPONSE_CHANNEL_PREFIX, ":"))){
                RedisSerializer<SocketPacketModel> valueSerializer = csRedisTemplate.getValueSerializer();
                SocketPacketModel socketPacketModel = valueSerializer.deserialize(message.getBody());
                LOGGER.info("receive msg from channel:{},data:{}",channel,JSON.toJSONString(socketPacketModel));
                SocketCallback callback = SocketContextUtils.getCallback(socketPacketModel.getCallbackId());
                if(callback!=null){
                    callback.call(socketPacketModel.getData());
                }
            }else if(channel.startsWith(StringUtils.substringBeforeLast(RedisConstants.SOCKET_ROOMS_CHANNEL_PREFIX, ":"))){
                JSONObject jsonData = JSON.parseObject(new String(message.getBody()));
                LOGGER.info("receive msg from channel:{},data:{}",channel,jsonData.toJSONString());
                Integer type = jsonData.getInteger("type");
                String namespace = jsonData.getString("namespace");
                JSONArray rooms = jsonData.getJSONArray("rooms");
                JSONArray socketIds = jsonData.getJSONArray("socketIds");
                socketService.joinOrLeaveLocalRoom(namespace, type, rooms.toJavaList(String.class), socketIds.toArray(new String[]{}));
                String requestId = jsonData.getString("requestId");
                if(StringUtils.isNotBlank(requestId)) {
                    String key = String.format(RedisKey.ROOM_REQUEST_ID_KEY,requestId);
                    csStringRedisTemplate.opsForList().leftPush(key, String.valueOf(System.currentTimeMillis()));
                    csStringRedisTemplate.expire(key,5, TimeUnit.SECONDS);
                }
            }
        }, PatternTopic.of("socket:channel:*"));
        return container;
    }

}
