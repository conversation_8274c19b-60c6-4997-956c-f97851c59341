package com.wolaidai.webot.connector.model;

import com.wolaidai.webot.connector.constant.WebStatusConstants;
import lombok.Data;

@Data
public class ResponseModel extends BaseModel{
    private int ret = WebStatusConstants.RESPONSE_CODE_SUCCESS;
    private String msg = "";
    private Object data;

    public ResponseModel() {
    }

    public ResponseModel(Object data) {
        this.data = data;
    }

    public ResponseModel(int ret, String msg) {
        this.ret = ret;
        this.msg = msg;
    }

    public ResponseModel(int ret, String msg, Object data) {
        this.ret = ret;
        this.msg = msg;
        this.data = data;
    }
}
