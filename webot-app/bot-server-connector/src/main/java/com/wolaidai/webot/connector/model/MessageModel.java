package com.wolaidai.webot.connector.model;

import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class MessageModel extends BaseModel {
    private String event;
    private long eventTime;
    private String namespace;
    private String socketId;
    private String ip;
    private String ua;
    private Object data;
    private SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback;
}
