package com.wolaidai.webot.connector.wechat.utils;

import com.wolaidai.webot.common.util.JsoupUtil;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.jsoup.parser.Parser;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MsgUtils {
    public static String handleText(String str){
        if(StringUtils.isBlank(str)){
            return "";
        }
        String text = str.replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&nbsp;", " ")
                .replace("\\n", "\n")
                .replace("</p>", "")
                .replaceAll("<p>", "\n")
                .replaceAll("<br(/)?>", "\n")
                .replaceAll("\\s*target=\"_blank\"\\s*", "")
                .trim();
        return Parser.unescapeEntities(JsoupUtil.cleanHtml(text),true);
    }

    public static String msgMenuToText(List<WxMpKefuMessage.MsgMenu> msgMenus){
        StringBuilder sb = new StringBuilder();
        for (WxMpKefuMessage.MsgMenu msgMenu : msgMenus) {
            sb.append(String.format("<a href=\"weixin://bizmsgmenu?msgmenucontent=%s&msgmenuid=%s\">%s</a>\n",msgMenu.getContent(),msgMenu.getId(),msgMenu.getContent()));
        }
        return sb.toString();
    }


    public static String getH5MenuStr(JSONObject menu) {
        String replyContent = "";
        replyContent = menu.getJSONArray("list").toList().stream().map(v -> "<p>" + ((Map) v).get("content") + "</p>").collect(Collectors.joining());
        String headContent = menu.optString("head_content");
        if (StringUtils.isNotBlank(headContent)) {
            replyContent = headContent + replyContent;
        }
        String tailContent = menu.optString("tail_content");

        if (StringUtils.isNotBlank(tailContent)) {
            if (tailContent.contains("也许您是要问：")) {
                replyContent = replyContent + "<p>" + tailContent + "</p>";
            } else {
                replyContent = replyContent + tailContent;
            }
        }

        return replyContent;
    }
}
