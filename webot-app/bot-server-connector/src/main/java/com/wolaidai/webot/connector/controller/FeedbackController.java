package com.wolaidai.webot.connector.controller;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.common.util.OssFileClient;
import com.wolaidai.webot.connector.constant.WebStatusConstants;
import com.wolaidai.webot.connector.model.FeedbackCategorySimpleRowModel;
import com.wolaidai.webot.connector.model.FeedbackCreateModel;
import com.wolaidai.webot.connector.model.ResponseModel;
import com.wolaidai.webot.data.mongodb.entity.ConversationEntity;
import com.wolaidai.webot.data.mongodb.entity.FeedbackEntity;
import com.wolaidai.webot.data.mongodb.repo.ConversationRepo;
import com.wolaidai.webot.data.mongodb.repo.FeedbackRepo;
import com.wolaidai.webot.data.mysql.entity.bot.BotEntity;
import com.wolaidai.webot.data.mysql.entity.bot.SkillGroupEntity;
import com.wolaidai.webot.data.mysql.entity.feedback.FeedbackCategoryEntity;
import com.wolaidai.webot.data.mysql.repo.BotRepo;
import com.wolaidai.webot.data.mysql.repo.FeedbackCategoryRepo;
import com.wolaidai.webot.data.mysql.repo.SkillGroupRepo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@AllArgsConstructor
@Api(tags = "反馈接口")
@RestController
@RequestMapping("/feedback")
public class FeedbackController extends BaseController {

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());
    private final FeedbackRepo feedbackRepo;
    private final FeedbackCategoryRepo feedbackCategoryRepo;
    private final ConversationRepo conversationRepo;
    private final BotRepo botRepo;
    private final SkillGroupRepo skillGroupRepo;

    @GetMapping("/category/{accessKey}")
    @ApiOperation(value = "查询反馈分类")
    public ResponseModel getFeedbackCategory(@PathVariable String accessKey) {
        List<FeedbackCategoryEntity> list = feedbackCategoryRepo.findByAccessKey(accessKey);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", list.stream().map(i -> new FeedbackCategorySimpleRowModel(i)).collect(Collectors.toList()));
    }
    
    @PostMapping
    @ApiOperation(value = "提交反馈")
    public ResponseModel saveFeedback(MultipartFile[] attachments, FeedbackCreateModel model) throws IOException {
        if (StringUtils.isBlank(model.getDescription())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "无效的问题描述");
        }
        if (StringUtils.isBlank(model.getMobile())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "无效的手机号");
        }
        ConversationEntity c = null;
        if (StringUtils.isBlank(model.getCid()) || null == (c = conversationRepo.findByCid(model.getCid()))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话不存在");
        }
        ArrayList<String> _attachments = new ArrayList<>();
        if (null != attachments) {
            String platformName = "webot", materialType = "image", channelStr = "h5", suffix = ".jpg";
            String fileDir = platformName + "/" + materialType + "/user/" + channelStr + "/" + DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now()) + "/";
            for (MultipartFile f : attachments) {
                InputStream inputStream = f.getInputStream();
                if (inputStream == null) {
                    return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "文件读取异常");
                }
                String file = fileDir + UUID.randomUUID() + suffix;
                OssFileClient.putObject(appPropertyConfig.getOssBucketName(), file, inputStream);
                inputStream.close();
                _attachments.add(Base64.getEncoder().encodeToString(file.getBytes()));
            }
        }
        FeedbackEntity feedback = new FeedbackEntity();
        feedback.setCid(c.getCid());
        feedback.setClientId(c.getClientId());
        feedback.setClientType(c.getClientType());
        feedback.setBotId(c.getBotId());
        feedback.setOrgId(c.getOrgId());
        feedback.setDescription(model.getDescription());
        feedback.setMobile(model.getMobile());
        feedback.setEmail(model.getEmail());
        feedback.setAttachments(_attachments);
        feedback.setHitType(model.getHitType());
        feedback.setCategoryId(model.getCategoryId());
        feedback.setDate(new Date());
        feedbackRepo.saveFeedBack(feedback);
        if (appPropertyConfig.isNotifyFeedback()) {
            notifyFeedback(feedback, c);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    private void notifyFeedback(FeedbackEntity f, ConversationEntity c) {
        try {
            BotEntity bot = botRepo.findById(f.getBotId()).orElse(null);
            SkillGroupEntity sg = skillGroupRepo.findById(c.getSkillGroupId()).orElse(null);
            FeedbackCategoryEntity fc = feedbackCategoryRepo.findByIdAndBotId(f.getCategoryId(), f.getBotId());
            String biz = sg.getName();
            if (null != fc) {
                biz += "-" + fc.getContent();
            }
            String desc = f.getDescription();
            if (null != desc && desc.length() > 20) {
                desc = desc.substring(0, 20) + "……";
            }
            JSONObject data = new JSONObject();
            data.put("msgtype", "markdown");
            StringBuilder sb = new StringBuilder();
            sb.append("手机号：").append(f.getMobile()).append("\n");
            sb.append("机器人：<font color=\"info\">").append(bot.getName()).append("</font>\n");
            sb.append("业　务：<font color=\"info\">").append(biz).append("</font>\n");
            sb.append("时　间：<font color=\"warning\">").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(f.getDate())).append("</font>\n");
            sb.append("附　件：<font color=\"comment\">").append(f.getAttachments().size()).append("</font>\n");
            sb.append("问题描述：<font color=\"comment\">").append(desc).append("</font>\n");
            sb.append(">[详情](").append(appPropertyConfig.getFeedbackDetailUrl()).append("?id=").append(f.getId()).append(")");
            data.put("markdown", new JSONObject().fluentPut("content", sb.toString()));
            HttpClientUtil.post(appPropertyConfig.getWebhookUrl(), data.toJSONString(), 3000);
        } catch (Exception e) {
            LOGGER.warn("notifyFeedback error:{}", e);
        }
    }
}
