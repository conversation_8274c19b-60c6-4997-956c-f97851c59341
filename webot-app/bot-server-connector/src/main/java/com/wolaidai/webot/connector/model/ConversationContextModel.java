package com.wolaidai.webot.connector.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

@Data
@AllArgsConstructor
public class ConversationContextModel extends BaseModel{
    private boolean isNew;
    private ContextInfo info;

    @Data
    public static class ContextInfo{
        private String socketId;
        private String isWechat;
        private String gcid;
        private Date gcTime;
        private String cid;
        private String sessionKey;
        private String account;
        private Integer clientType;
        private Integer botId;
        private Integer orgId;
        private Integer skillGroupId;
        private String accessKey;
        private String scene;
        private Integer status;
    }
}
