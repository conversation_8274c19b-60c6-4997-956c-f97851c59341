package com.wolaidai.webot.connector.service.schedule.impl;


import com.wolaidai.webot.connector.constant.ManualConfigConstants;
import com.wolaidai.webot.connector.service.business.ManualChatService;
import com.wolaidai.webot.connector.service.schedule.ScheduleService;
import com.wolaidai.webot.data.mongodb.repo.*;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@AllArgsConstructor
public class ScheduleServiceImpl implements ScheduleService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final RedissonClient redissonClient;

    private final SessionListRepo sessionListRepo;

    private final ManualChatService manualChatService;

    private final ComplexConversationRepo complexConversationRepo;

    private final ComplexChatHistoryRepo complexChatHistoryRepo;

    @Override
    @Scheduled(cron = "0 0/10 * * * ?")
    public void checkCsSession() {
        String redisTaskKey = String.format(RedisKey.LOCK_KEY,"checkCsSession");
        RLock lock = redissonClient.getLock(redisTaskKey);
        try {
            boolean res = lock.tryLock(0, 10, TimeUnit.SECONDS);
            if (!res) {
                return;
            }
            LOGGER.info("start checkCsSession...");
            long start = System.currentTimeMillis();
            List<SessionListEntity> sessionList = sessionListRepo.findByStatusInAndCreateTimeAfter(Arrays.asList(SessionListEntity.STATUS_INIT, SessionListEntity.STATUS_ONLINE), DateUtils.truncate(new Date(), Calendar.DATE));
            if (sessionList.isEmpty()) {
                return;
            }
            Map<String,JSONObject> serviceTimeoutOfflineMsgMap = new HashMap<>();
            Map<String,JSONObject> customerTimeoutOfflineMsgMap = new HashMap<>();
            for (SessionListEntity session : sessionList) {
                Integer lastMsgSender = session.getLastMsgSender();
                if(Objects.equals(lastMsgSender,SessionListEntity.USER_SENDER)){
                    offlineSession(serviceTimeoutOfflineMsgMap, session, ManualConfigConstants.AUTO_REPLY_SERVICE_TIMEOUT_OFFLINE_MSG);
                }else{
                    offlineSession(customerTimeoutOfflineMsgMap, session, ManualConfigConstants.AUTO_REPLY_CUSTOMER_OFFLINE_MSG);
                }
            }
            LOGGER.info("end checkCsSession,cost {} ms",System.currentTimeMillis()-start);
        }catch (Exception e){
            LOGGER.error("execute checkCsSession schedule fail",e);
        }finally {
            if(lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    private void offlineSession(Map<String, JSONObject> map, SessionListEntity session, String msgType) {
        JSONObject timeoutOfflineMsg = getTimeoutOfflineMsg(session.getOrgId(), session.getClientTypeId(), msgType, map);
        if(timeoutOfflineMsg!=null){
            Date lastMsgTime = session.getLastMsgTime();
            int timeoutMinutes = timeoutOfflineMsg.getInt("timeoutMinutes");
            if(lastMsgTime!=null&&timeoutMinutes>0){
                if(System.currentTimeMillis()-lastMsgTime.getTime()>=timeoutMinutes*60*1000){
                    if(ManualConfigConstants.AUTO_REPLY_SERVICE_TIMEOUT_OFFLINE_MSG.equals(msgType)) {
                        manualChatService.offlineSession(session.getSessionKey(), SessionListEntity.CLOSE_TYPE_CS_TIMEOUT);
                    }else if(ManualConfigConstants.AUTO_REPLY_CUSTOMER_OFFLINE_MSG.equals(msgType)){
                        manualChatService.offlineSession(session.getSessionKey(), SessionListEntity.CLOSE_TYPE_CUSTOMER_TIMEOUT);
                    }
                }
            }
        }
    }

    private JSONObject getTimeoutOfflineMsg(int orgId,int clientType,String msgType,Map<String,JSONObject> map){
        String mapKey = orgId+"_"+clientType;
        JSONObject data = map.get(mapKey);
        if(data!=null){
            return data;
        }
        data = manualChatService.getAutoReplyConfig(orgId, clientType, msgType);
        if(data!=null && data.getBoolean("status")){
            map.put(mapKey,data);
            return data;
        }
        return null;
    }


}
