package com.wolaidai.webot.connector.service.event.chat;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.constant.ManualConfigConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.model.*;
import com.wolaidai.webot.connector.service.business.*;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.connector.service.worktime.BotWorkTimeService;
import com.wolaidai.webot.connector.utils.CommonUtil;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.connector.utils.VoiceUtils;
import com.wolaidai.webot.connector.wechat.service.WechatCpService;
import com.wolaidai.webot.connector.wechat.service.WechatMessageService;
import com.wolaidai.webot.connector.wechat.utils.MsgUtils;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.entity.TestChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.repo.ChatHistoryRepo;
import com.wolaidai.webot.data.mysql.entity.bot.BotEntity;
import com.wolaidai.webot.data.mysql.entity.bot.MaterialEntity;
import com.wolaidai.webot.data.mysql.entity.bot.SkillEntity;
import com.wolaidai.webot.data.mysql.entity.bot.SkillSkillGroupEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.docqa.DocQaFileEntity;
import com.wolaidai.webot.data.mysql.entity.docqa.DocQaReleaseEntity;
import com.wolaidai.webot.data.mysql.repo.DocQaFileRepo;
import com.wolaidai.webot.data.mysql.repo.DocQaReleaseRepo;
import com.wolaidai.webot.data.mysql.repo.MaterialRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.mysql.repo.SkillGroupRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import ws.schild.jave.EncoderException;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class MessageEventService extends BaseEventService {

    private static final int MAX_CONCURRENT_CALLS = 3;
    private static final Semaphore semaphore = new Semaphore(MAX_CONCURRENT_CALLS);

    private final AppPropertyConfig appPropertyConfig;
    private final ChatHistoryService chatHistoryService;
    private final ManualChatService manualChatService;
    private final ConversationService conversationService;
    private final BotWorkTimeService botWorkTimeService;
    private final MaterialRepo materialRepo;
    private final ApiService apiService;
    private final SessionListRepo sessionListRepo;
    private final StringRedisTemplate csStringRedisTemplate;
    private final WechatMessageService wechatMessageService;
    private final WechatCpService wechatCpService;
    private final KeyExpireService keyExpireService;
    private final ChatHistoryRepo chatHistoryRepo;
    private final DocQaFileRepo docQaFileRepo;
    private final SkillGroupRepo skillGroupRepo;
    private final DocQaReleaseRepo docQaReleaseRepo;
    private final TemplateService templateService;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject)model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        String socketId = model.getSocketId();
        String source = data.optString("source");
        if(Objects.equals(source,"H5")) {
            BotMessageModel botMessageModel = JSON.parseObject(data.toString(), BotMessageModel.class);
            sendH5Message(socketId, models, botMessageModel, callback);
        }else if(Objects.equals(source,"cs")){
            CsMessageModel csMessageModel = JSON.parseObject(data.toString(), CsMessageModel.class);
            sendCsMessage(socketId, models, csMessageModel, callback);
        }else if(Objects.equals(source,"test")){
            BotTestMessageModel botTestMessageModel = JSON.parseObject(data.toString(), BotTestMessageModel.class);
            sendTestMessage(socketId, models, botTestMessageModel, callback);
        }
        return models;
    }

    private void sendH5Message(String socketId, List<SocketPacketModel> models, BotMessageModel botMessageModel, SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback) {
        String clientId = botMessageModel.getClientId();
        String accessKey = botMessageModel.getAccessKey();
        String skillGroupId = botMessageModel.getSkillGroupId();
        String content = botMessageModel.getContent();
        if (StringUtils.isNotBlank(clientId) && StringUtils.isNotBlank(accessKey) && StringUtils.isNotBlank(skillGroupId) && StringUtils.isNotBlank(content)) {
            ConversationContextModel botConversation = conversationService.getConversation(socketId, clientId, botMessageModel.getClientType(), botMessageModel.getAccount(), accessKey, skillGroupId, true);
            if (botConversation == null) {
                return;
            }
            String recognition = null;
            if ("voice".equals(botMessageModel.getType())) {
                byte[] fileData = apiService.getFile(botMessageModel.getMedia().getString("mediaId"));
                if (fileData != null && fileData.length > 0) {
                    File source = null;
                    File target = null;
                    InputStream fis = null;
                    try {
                        source = File.createTempFile("voice_", ".mp3");
                        FileUtils.writeByteArrayToFile(source, fileData);
                        target = File.createTempFile("voice_", ".wav");
                        VoiceUtils.voiceFileConversion(source, target, "wav");
                        fis = FileUtils.openInputStream(target);
                        recognition = apiService.speechRecognition(fis, null, "8000", true, false, false);
                        botMessageModel.getMedia().put("recognition",recognition);
                    } catch (EncoderException e) {
                        LOGGER.error("转换文件失败", e);
                    } catch (IOException e) {
                        LOGGER.error("文件读写失败", e);
                    } finally {
                        FileUtils.deleteQuietly(source);
                        FileUtils.deleteQuietly(target);
                        IOUtils.closeQuietly(fis);
                    }
                }
            }
            ConversationContextModel.ContextInfo contextInfo = botConversation.getInfo();
            if (Objects.equals(contextInfo.getScene(), "bot")) {
                BotEntity bot = conversationService.getBotEntity(accessKey);
                Map<String, Object> extend = null;
                if(bot != null && bot.getType()==2) {
                    if(botMessageModel.getLabelList() != null && botMessageModel.getLabelList().size() > 0) {
                        extend = new HashMap<>();
                        extend.put("docLabels", botMessageModel.getLabelList());
                    } else {
                        //构建返回消息，提示用户需先选择问答范围，该数据不入库
                        ChatHistoryEntity chatHistory = new ChatHistoryEntity();
                        chatHistory.setBotId(contextInfo.getBotId());
                        chatHistory.setType(botMessageModel.getType());
                        chatHistory.setSender("bot");
                        chatHistory.setContent("请先选择标签");
                        models.add(new SocketPacketModel(null, socketId, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory)));
                        callback.sendAcknowledgement(new JSONObject().put("ret",0).put("msgId", botMessageModel.getMsgId()).put("timestamp", System.currentTimeMillis()));
                        return;
                    }
                }
                conversationService.updateConversationStatus(clientId, contextInfo.getBotId(), contextInfo.getCid(), contextInfo.getStatus());
                ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(botMessageModel.getMsgId(), clientId, contextInfo.getClientType(), "bot", "user", botMessageModel.getType(), null, contextInfo.getAccount(),
                        contextInfo.getOrgId(), contextInfo.getBotId(), contextInfo.getSkillGroupId(), contextInfo.getGcid(), contextInfo.getCid(), content, extend, null, botMessageModel.getMedia(), null, null, true);
                
                if(bot != null && bot.getType()==2) {
                    //文档问答特殊处理
                    sendDocQaReply(socketId, models, contextInfo.getAccount(), contextInfo.getOrgId(), contextInfo.getBotId(), contextInfo.getSkillGroupId(), clientId, 
                        contextInfo.getClientType(), contextInfo.getGcid(), contextInfo.getCid());
                } else {
                    sendBotReply(socketId, models, contextInfo.getAccount(),botMessageModel.getAccount(), contextInfo.getOrgId(), contextInfo.getBotId(),
                        contextInfo.getSkillGroupId(), null, clientId, contextInfo.getClientType(), botMessageModel.getType(),botMessageModel.getContent(),
                        contextInfo.getGcid(), contextInfo.getCid(), recognition, chatHistory.getId(), false);
                }
                if(callback!=null){
                    callback.sendAcknowledgement(new JSONObject().put("ret",0).put("msgId", botMessageModel.getMsgId()).put("timestamp", chatHistory.getDate().getTime()));
                }
            }else if(Objects.equals(contextInfo.getScene(), "cs")){
                String sessionKey = contextInfo.getSessionKey();
                if(StringUtils.isNotBlank(sessionKey)){
                    Map<String,Object> manual = new HashMap<>();
                    manual.put("sessionKey",contextInfo.getSessionKey());
                    SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
                    if(sessionList!=null) {
                        ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(botMessageModel.getMsgId(), sessionList.getClientId(), sessionList.getClientTypeId(), "cs", "user", botMessageModel.getType(), null, contextInfo.getAccount(),
                                sessionList.getOrgId(), sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(), null, content, null, null, botMessageModel.getMedia(), null, manual, true);
                        manualChatService.initUserMsgToSession(sessionList,chatHistory);
                        //处理用户敏感信息
                        CommonUtil.processUserSensitiveInfo(chatHistory);
                        models.add(new SocketPacketModel(contextInfo.getSessionKey(), socketId, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory)));
                        if(callback!=null){
                            callback.sendAcknowledgement(new JSONObject().put("ret",0).put("msgId",botMessageModel.getMsgId()).put("timestamp",chatHistory.getDate().getTime()));
                        }
                    }
                }
            }
        }
    }

    private void sendBotReply(String socketId, List<SocketPacketModel> models, String account,String newAccount, Integer orgId, Integer botId,
                              Integer skillGroupId, Integer skillId, String clientId, Integer clientType, String msgType, String msgContent,
                              String gcId, String cid, String recognition, String chatId, boolean isTest) {
//        LOGGER.info("sendBotReply:{}", msgContent);
        JSONObject reply = apiService.getReply(orgId, chatId, botId, skillGroupId, skillId,  clientId, clientType, msgType, msgContent, recognition, cid, isTest);
        JSONArray replyArr = reply.optJSONArray("reply");
        boolean hasOp = false;
        if (replyArr != null) {
            for (int i = 0; i < replyArr.length(); i++) {
                JSONObject replyObj = replyArr.getJSONObject(i);
                String type = replyObj.optString("type");
                String replyContent = replyObj.optString("content");
//                if (!hasOp && "text".equals(type) && replyContent.contains("人工")) {
//                    hasOp = true;
//                }
                Map<String, Object> mediaMap = null;
                Map<String, Object> menuMap = null;
                boolean hasExtra = (i == replyArr.length() - 1 && reply.has("extra"));
                JSONObject extra = hasExtra?reply.optJSONObject("extra"):null;
                String content = replyContent;
                String materialId = replyObj.optString("material_id");
                if (StringUtils.isNotBlank(materialId)) {
                    MaterialEntity materialEntity = materialRepo.findByIdOrFileId(Integer.parseInt(materialId), materialId);
                    if (materialEntity.getType() == 1) {
                        type = "image";
                    } else if (materialEntity.getType() == 2) {
                        type = "voice";
                        mediaMap = new JSONObject().put("duration", Math.round(materialEntity.getDuration() / 1000)).toMap();
                    }
                    content = materialEntity.getFileId();
                } else {
                    if (Objects.equals(type, "menu")) {
                        JSONObject menu = replyObj.getJSONObject("content");
                        replyContent = menu.getJSONArray("list").toList().stream().map(v -> "<p>" + ((Map) v).get("content") + "</p>").collect(Collectors.joining());
                        String headContent = menu.optString("head_content");
                        if (StringUtils.isNotBlank(headContent)) {
                            replyContent = headContent + replyContent;
                        }
                        String tailContent = menu.optString("tail_content");
                        if (StringUtils.isNotBlank(tailContent)) {
                            replyContent = replyContent + tailContent;
                        }
                        content = replyContent;
                        menu.put("head_content", replaceContent(headContent, newAccount));
                        menuMap = menu.toMap();
                    }
                    if (hasExtra&&!isTest) {
                        if(!hasOp){
                            hasOp = extra.optBoolean("hasOp");
                        }
                        hasOp = hasOp&&botWorkTimeService.isWorkTime(orgId);
                        extra.put("hasOp", hasOp);
                        extra.put("hasFeedback", false);
                        extra.put("hitType", 0);
                        if (extra.getInt("matchSensitiveWord") == 1) {
                            extra.put("hitType", 1);
                            long hitSensitive = conversationService.incrementKey(clientId, botId, "hitSensitive");
                            if (hitSensitive == 1) {
                                if (botWorkTimeService.isWorkTime(orgId)) {
                                    extra.put("hasOp", true);
                                } else {
                                    extra.put("hasFeedback", true);
                                }
                            }
                        } else if (extra.getInt("matchEmotion") == 1) {
                            extra.put("hitType", 2);
                            long hitEmotion = conversationService.incrementKey(clientId, botId, "hitEmotion");
                            if (hitEmotion == 2) {
                                if (botWorkTimeService.isWorkTime(orgId)) {
                                    extra.put("hasOp", true);
                                }
                            } else if (hitEmotion == 3) {
                                extra.put("hasFeedback", true);
                            }
                        }
                    }
                }
                if(!isTest) {
                    if (content.contains("热点问题：")) {
                        JSONObject extraMenu = convertToMenu(content);
	                    content = MsgUtils.getH5MenuStr(extraMenu);
                        if (menuMap != null) {
                            menuMap.putAll(extraMenu.toMap());
                        } else {
                            menuMap = extraMenu.toMap();
                        }
                        type = "menu";

                    }
                    content = replaceContent(content, newAccount);
                    ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(null, clientId, clientType, "bot", "bot", type, null, account,
                            orgId, botId, skillGroupId, gcId, cid, content, null, extra == null ? null : extra.toMap(), mediaMap, menuMap, null, true);
                    models.add(new SocketPacketModel(null, socketId, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory)));
                }else{
                    TestChatHistoryEntity testChatHistoryEntity = chatHistoryService.createTestChatHistory(null, clientId, clientType, "bot",  type, account, botId, skillGroupId,skillId, content, extra == null ? null : extra.toMap(), mediaMap, menuMap, true);
                    com.alibaba.fastjson.JSONObject data = new com.alibaba.fastjson.JSONObject();
                    data.putAll(JSON.parseObject(MessageUtil.toJsonString(testChatHistoryEntity)));
                    JSONObject debugInfo = reply.getJSONObject("debugInfo");
                    if(debugInfo!=null) {
                        data.put("debugInfo", JSON.parseObject(debugInfo.toString()));
                    }
                    models.add(new SocketPacketModel(null, socketId, SocketEvent.MESSAGE, data.toString()));
                }
            }
        }
    }
    

    private JSONObject convertToMenu(String content) {
        return templateService.extractMenu(content);
        
    }

    private String replaceContent(String content, String account) {
//        LOGGER.info("replaceContent content:{}, account:{}", content, account);
        return templateService.replacePlaceholders(content, account);
    }

    private void sendCsMessage(String socketId, List<SocketPacketModel> models, CsMessageModel csMessageModel, SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback) {
        com.alibaba.fastjson.JSONObject manual = csMessageModel.getManual();
        HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
        String sessionKey = manual.getString("sessionKey");
        if(StringUtils.isNotBlank(sessionKey)){
            SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
            if(sessionList!=null){
                String csRespDetailKey = String.format(RedisKey.CS_RESP_DETAIL, sessionList.getSessionKey());
                keyExpireService.removeExpireKey(String.format(RedisKey.CS_FIRSTRESP_SESSION_KEY, sessionList.getSessionKey()),String.format(RedisKey.CS_SESSIONRESP_SESSION_KEY, sessionList.getSessionKey()));
                hashOperations.delete(csRespDetailKey,"firstReplyTimeout","sessionReplyTimeout");
                Integer lastMsgSender = sessionList.getLastMsgSender();
                Date customerFirstReplyTime = sessionList.getCustomerFirstReplyTime();
                if(sessionList.getStatus()==SessionListEntity.STATUS_ONLINE){
                    if(customerFirstReplyTime!=null){
                        manual.put("responseTime",(int)((System.currentTimeMillis()-sessionList.getCustomerFirstReplyTime().getTime())/1000));
                        sessionList.setCustomerFirstReplyTime(null);
                        long replyTime = System.currentTimeMillis() - customerFirstReplyTime.getTime();
                        Long totalReplyTime = hashOperations.increment(csRespDetailKey, "totalReplyTime", replyTime);
                        Long round = hashOperations.increment(csRespDetailKey, "round", 1);
                        int avgRespTime = BigDecimal.valueOf(totalReplyTime).divide(BigDecimal.valueOf(round), 0, RoundingMode.HALF_UP).intValue();
                        sessionList.setAvgRespTime(avgRespTime);
                        int avgRespSecond = manualChatService.getRespTimeoutConfig(sessionList.getOrgId(), ManualConfigConstants.AVG_RESP);
                        if(avgRespSecond>0){
                            if(avgRespSecond<=avgRespTime/1000) {
                                sessionList.setAvgRespTimeout(1);
                            }else{
                                sessionList.setAvgRespTimeout(0);
                            }
                        }
                        if(round==1){
                            sessionList.setServiceFirstRespTime((int) replyTime);
                        }
                    }
                }else if(sessionList.getStatus()==SessionListEntity.STATUS_OFFLINE){
                    //是否离线消息并且有转其他人工客服
                    if("cs".equals(hashOperations.get(String.format(RedisKey.GLOBAL_CONVERSATION_KEY, sessionList.getClientId(), sessionList.getBotId()),"scene"))){
                        callback.sendAcknowledgement(new JSONObject().put("ret",-1).put("msg","其他客服正在接待该用户，暂时不能发送离线消息").put("msgId", csMessageModel.getMsgId()).put("timestamp", System.currentTimeMillis()));
                        return;
                    }
                }
                ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(csMessageModel.getMsgId(),sessionList.getClientId(), sessionList.getClientTypeId(), "cs", "manual", csMessageModel.getType(), null, null,
                        sessionList.getOrgId(), sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(), null, csMessageModel.getContent(), null, null, csMessageModel.getMedia(), null, manual, true);
                String msg = MessageUtil.escapeHtml(chatHistory.getContent(),chatHistory.getType());
                sessionListRepo.updateById(sessionList.getId(), sessionList.getCustomerFirstReplyTime(), sessionList.getAvgRespTime(), sessionList.getAvgRespTimeout(), sessionList.getServiceFirstRespTime(), 0, msg, chatHistory.getDate(), SessionListEntity.MANUAL_SENDER);
                if(lastMsgSender!=null&&lastMsgSender==SessionListEntity.USER_SENDER) {
                    JSONObject customerTimeoutMsg = manualChatService.getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_CUSTOMER_TIMEOUT_MSG);
                    if (customerTimeoutMsg != null && customerTimeoutMsg.getBoolean("status")) {
                        int timeoutMinutes = customerTimeoutMsg.getInt("timeoutMinutes");
                        if (timeoutMinutes > 0) {
                            keyExpireService.setExpireKey(String.format(RedisKey.CS_LAST_REPLY_SESSION_KEY, sessionList.getSessionKey()), timeoutMinutes, TimeUnit.MINUTES);
                        }
                    }
                    JSONObject customerOfflineMsg = manualChatService.getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_CUSTOMER_OFFLINE_MSG);
                    if (customerOfflineMsg != null && customerOfflineMsg.getBoolean("status")) {
                        int timeoutMinutes = customerOfflineMsg.getInt("timeoutMinutes");
                        if (timeoutMinutes > 0) {
                            keyExpireService.setExpireKey(String.format(RedisKey.CS_OFFLINE_SESSION_KEY, sessionList.getSessionKey()), timeoutMinutes, TimeUnit.MINUTES);
                        }
                    }
                }
                if(wechatMessageService.isWechat(sessionList.getClientId(),sessionList.getBotId())){
                    wechatMessageService.sendWechatMsg(sessionList.getClientId(),sessionList.getBotId(),chatHistory);
                }else if(wechatCpService.isWorkWechat(sessionList.getClientId(),sessionList.getBotId())){
                    wechatCpService.sendWechatMsg(sessionList.getClientId(),sessionList.getBotId(),chatHistory);
                }else {
                    models.add(new SocketPacketModel(sessionKey, socketId, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory)));
                }
                if(callback!=null){
                    callback.sendAcknowledgement(new JSONObject().put("ret",0).put("msgId", csMessageModel.getMsgId()).put("timestamp", chatHistory.getDate().getTime()));
                }
            }
        }
    }



    private void sendTestMessage(String socketId, List<SocketPacketModel> models, BotTestMessageModel botTestMessageModel, SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback){
        TestChatHistoryEntity chatHistory = chatHistoryService.createTestChatHistory(botTestMessageModel.getMsgId(), botTestMessageModel.getClientId(), botTestMessageModel.getClientType(), botTestMessageModel.getSender(),  botTestMessageModel.getType(), botTestMessageModel.getAccount(), botTestMessageModel.getBotId(), botTestMessageModel.getSkillGroupId(), botTestMessageModel.getSkillId(), botTestMessageModel.getContent(), null, botTestMessageModel.getMedia(),null, true);
        sendBotReply(socketId, models, botTestMessageModel.getAccount(), botTestMessageModel.getAccount() ,null, botTestMessageModel.getBotId(),
                botTestMessageModel.getSkillGroupId(), botTestMessageModel.getSkillId(), botTestMessageModel.getClientId(), botTestMessageModel.getClientType(), botTestMessageModel.getType(),botTestMessageModel.getContent(),
                null, null, null, null, true);
        if(callback!=null){
            callback.sendAcknowledgement(new JSONObject().put("ret",0).put("msgId", botTestMessageModel.getMsgId()).put("timestamp", chatHistory.getDate().getTime()));
        }
    }

    private void sendDocQaReply(String socketId, List<SocketPacketModel> models, String account, Integer orgId, Integer botId,
                              Integer skillGroupId, String clientId, Integer clientType, String gcId, String cid) {
        //从histories根据cid获取历史记录，取最近的6条消息组装问答上下文
        List<ChatHistoryEntity> chatHistList = chatHistoryRepo.findHistoryListByCid(cid);
        List<Map<String, Object>> chatData = new ArrayList<>();
        List<String> docLabelList = null;
        for(int i=0; i<7 && i<chatHistList.size(); i++) {
            ChatHistoryEntity historyEntity = chatHistList.get(i);
            ChatHistoryEntity queryEntity = null;
            ChatHistoryEntity answerEntity = null;
            if(historyEntity.getSender().equals("user")) {
                queryEntity = historyEntity;
                answerEntity = null;
                //最后一条用户发送的消息
                if(i==0) {
                    if(historyEntity.getExtend() != null && historyEntity.getExtend().get("docLabels") != null) {
                        docLabelList = (List<String>) historyEntity.getExtend().get("docLabels");
                    }
                } else if(chatHistList.get(i-1).getSender().equals("bot")) {
                    answerEntity = chatHistList.get(i-1);
                }
                Map<String, Object> chatMap = new HashMap<>();
                chatMap.put("id", queryEntity.getMsgId());
                chatMap.put("query", queryEntity.getContent());
                chatMap.put("answer", answerEntity == null ? null : answerEntity.getContent());
                chatMap.put("time", queryEntity.getDate());
                chatData.add(chatMap);
            }
        }
        Collections.reverse(chatData);
        
        Set<Integer> docIdSet = new HashSet<>();
        List<SkillSkillGroupEntity> skillList = new ArrayList(skillGroupRepo.findById(skillGroupId).get().getSkillSkillGroups());
        Integer skillId = skillList.get(0).getSkill().getId();
        DocQaReleaseEntity releaseEntity = docQaReleaseRepo.findFirstByOrgIdAndSkillIdAndStatusOrderByCreateTimeDesc(orgId, skillId, DocQaReleaseEntity.SUCCESS_STATUS);
        if(releaseEntity != null && releaseEntity.getAllDoc() != null && releaseEntity.getAllDoc().size() > 0) {
            List<Integer> docIdList = releaseEntity.getAllDoc().toJavaList(Integer.class);
            if(docLabelList != null && docLabelList.size()>0) {
                for(String label : docLabelList) {
                    List<DocQaFileEntity> fileList = docQaFileRepo.findByDocIdAndLabel(docIdList, label);
                    if(fileList != null && fileList.size() > 0) {
                        docIdSet.addAll(fileList.stream().map(DocQaFileEntity::getId).collect(Collectors.toList()));
                    }
                }
            }
        }
       
        com.alibaba.fastjson.JSONObject queryData = new com.alibaba.fastjson.JSONObject();
        queryData.put("cid", cid);
        queryData.put("data", chatData);
        String mode = "summary";
        Boolean queryMap = false;
        if(docIdSet.size() > 0) {
            queryData.put("file_ids", docIdSet);
            String docQaSearchDocId = appPropertyConfig.getDocQaSearchDocId();
            if(StringUtils.isNotBlank(docQaSearchDocId)) {
                Set<Integer> searchDocIdSet = new HashSet<>();
                List<String> docIdList = Arrays.asList(docQaSearchDocId.split(","));
                docIdList.forEach(v -> searchDocIdSet.add(Integer.parseInt(v)));
                searchDocIdSet.retainAll(docIdSet);
                if(!searchDocIdSet.isEmpty()) {
                    mode = "search";
                    queryMap = true;
                }
            }
        }
        com.alibaba.fastjson.JSONObject searchConfig = new com.alibaba.fastjson.JSONObject();
        searchConfig.put("mode", mode);
        searchConfig.put("query_map", queryMap);
        queryData.put("configs", searchConfig);

        String answer = "系统繁忙，请稍后再试";
        com.alibaba.fastjson.JSONObject resData = null;
        Map<String, Object> extend = null;

        try {
            semaphore.acquire(); // 获取信号量许可
            LOGGER.info("调用文档问答, cid:" + cid);
            resData = apiService.queryDocQa(queryData);
            semaphore.release(); // 释放信号量许可
        } catch (InterruptedException e) {
            LOGGER.error("处理调用文档问答请求错误, cid:{}", cid, e);
        } 
        extend = new HashMap<>();
        extend.put("docLabels", docLabelList);
        extend.put("docIds", docIdSet);
        if(resData != null) {
            if(StringUtils.isNotEmpty(resData.getString("answer"))) {
                answer = resData.getString("answer");
                extend.put("docQa", 1);
            }
            if(resData.get("reference")!=null) {
                extend.put("reference", resData.get("reference"));
            }
        }
        
        //保存回答的记录
        ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(null, clientId, clientType, "bot", "bot", "text", null, account,
                                orgId, botId, skillGroupId, gcId, cid, answer, extend, null, null, null, null, true);
        models.add(new SocketPacketModel(null, socketId, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory)));
    }
}
