package com.wolaidai.webot.connector.service.business;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.data.mysql.entity.bot.BotEntity;
import com.wolaidai.webot.data.mysql.entity.config.CombineCommonConfigEntity;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.entity.config.QaCommonConfigEntity;
import com.wolaidai.webot.data.mysql.repo.BotCommonConfigRepo;
import com.wolaidai.webot.data.mysql.repo.BotRepo;
import com.wolaidai.webot.data.mysql.repo.CommonConfigRepo;

import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class InitConfigService {
    private final AppPropertyConfig appPropertyConfig;
    private final RedisTemplate<String, String> csStringRedisTemplate;
    private final RedisTemplate<String, String> botStringRedisTemplate;
    private final CommonConfigRepo commonConfigRepo;
    private final BotCommonConfigRepo botCommonConfigRepo;
    private final BotRepo botRepo;

    public void initConfig() {
        if (appPropertyConfig.isCheckRedisConfig()) {
            int[] orgs = { -1, 1, 13 };
            for (int orgId : orgs) {
                initConfig(true, orgId, String.format("cs:config:autoreply:orgId:%s:clientType:%s", orgId, 1), "autoreply", "1");
                initConfig(true, orgId, String.format("cs:config:autoreply:orgId:%s:clientType:%s", orgId, 2), "autoreply", "2");
                initConfig(true, orgId, String.format("cs:config:autoreply:orgId:%s:clientType:%s", orgId, 3), "autoreply", "3");
                initConfig(true, orgId, String.format("cs:config:respTimeout:orgId:%s", orgId), "respTimeout");
                initConfig(true, orgId, String.format("cs:config:workbench:orgId:%s:status", orgId), "workbench_status");
                initConfig(true, orgId, String.format("cs:config:workbench:orgId:%s", orgId), "workbench");
                initConfig(true, orgId, String.format("cs:user:reception:max:orgId:%s", orgId), "maxReception");

                initConfig(false, orgId, String.format("cs:config:worktime:orgId:%s", orgId), "worktime");
                initConfig(false, orgId, String.format("cs:config:serviceuser:orgId:%s", orgId), "serviceuser");
                initConfig(false, orgId, String.format("cs:config:satisfaction:orgId:%s", orgId), "satisfaction");
                initConfig(false, orgId, String.format("cs:user:reception:default:orgId:%s", orgId), "defaultReception");

                initConfig(false, orgId, String.format("server:worktime:%s", orgId), "botworktime");
                initConfig(false, orgId, String.format("server:defaultreply:org:%s", orgId), "defaultreply");
                initConfig(false, orgId, String.format("server:turnArtificialPolicy:org:%s", orgId), "turnArtificialPolicy");
                initConfig(false, orgId, String.format("server:wechatQuestionRec:org:%s", orgId), "wechatQuestionRec");
                initConfig(false, orgId, String.format("org:%s:low_score", orgId), "lowScore");
                initConfig(false, orgId, String.format("org:%s:high_score", orgId), "highScore");
                initConfig(false, orgId, String.format("org:%s:chitchat_score", orgId), "chitchatScore");
            }
            initConfig(false, -1, String.format("server:bot:menu:code:%s", -1), "botmenu", "-1");
            for (BotEntity bot : botRepo.findAll()) {
                initConfig(false, bot.getOrgId(), String.format("server:bot:menu:code:%s", bot.getCode()), "botmenu", bot.getCode());
            }
        }
    }

    private void initConfig(boolean hash, Integer orgId, String key, String... type) {
        boolean botType = CommonConfigEntity.isBotType(type[0]);
        Long expired = botType ? botStringRedisTemplate.getExpire(key) : csStringRedisTemplate.getExpire(key);
        if (!Objects.equals(expired, -1L)) {
            return;
        }
        if (!hash) {
            String value = botType ? botStringRedisTemplate.opsForValue().get(key) : csStringRedisTemplate.opsForValue().get(key);
            if (null != value) {
                if (botType) {
                    if (type.length > 2 && null != botCommonConfigRepo.checkByOrgIdAndTypeAndSubType1AndSubType2(orgId, type[0], type[1], type[2])) {
                        return;
                    } else if (type.length > 1 && null != botCommonConfigRepo.checkByOrgIdAndTypeAndSubType1(orgId, type[0], type[1])) {
                        return;
                    } else if (type.length == 1 && null != botCommonConfigRepo.checkByOrgIdAndType(orgId, type[0])) {
                        return;
                    }
                } else {
                    if (type.length > 2 && null != commonConfigRepo.checkByOrgIdAndTypeAndSubType1AndSubType2(orgId, type[0], type[1], type[2])) {
                        return;
                    } else if (type.length > 1 && null != commonConfigRepo.checkByOrgIdAndTypeAndSubType1(orgId, type[0], type[1])) {
                        return;
                    } else if (type.length == 1 && null != commonConfigRepo.checkByOrgIdAndType(orgId, type[0])) {
                        return;
                    }
                }
                CombineCommonConfigEntity conf = botType ? new QaCommonConfigEntity() : new CommonConfigEntity();
                conf.setContent(value);
                conf.setOrgId(orgId);
                conf.setType(type[0]);
                if (type.length > 1) {
                    conf.setSubType1(type[1]);
                }
                if (type.length > 2) {
                    conf.setSubType2(type[2]);
                }
                conf.setCreateTime(new Date());
                conf.setUpdateTime(conf.getCreateTime());
                if (botType) {
                    botCommonConfigRepo.save((QaCommonConfigEntity) conf);
                } else {
                    commonConfigRepo.save((CommonConfigEntity) conf);
                }
            }
            return;
        }
        HashOperations<String, String, String> ho = null;
        if (botType) {
            ho = botStringRedisTemplate.opsForHash();
        } else {
            ho = csStringRedisTemplate.opsForHash();
        }
        Map<String, String> map = ho.entries(key);
        if (null == map || map.size() == 0) {
            return;
        }
        for (Map.Entry<String, String> entry : map.entrySet()) {
            CombineCommonConfigEntity conf = botType ? new QaCommonConfigEntity() : new CommonConfigEntity();
            conf.setOrgId(orgId);
            conf.setContent(entry.getValue());
            conf.setType(type[0]);
            if (type.length > 1) {
                conf.setSubType1(type[1]);
                conf.setSubType2(entry.getKey());
                if (botType) {
                    if (null != botCommonConfigRepo.checkByOrgIdAndTypeAndSubType1AndSubType2(orgId, type[0], type[1], entry.getKey())) {
                        continue;
                    }
                } else if (null != commonConfigRepo.checkByOrgIdAndTypeAndSubType1AndSubType2(orgId, type[0], type[1], entry.getKey())) {
                    continue;
                }
            } else {
                conf.setSubType1(entry.getKey());
                if (botType) {
                    if (null != botCommonConfigRepo.checkByOrgIdAndTypeAndSubType1(orgId, type[0], entry.getKey())) {
                        continue;
                    }
                } else if (null != commonConfigRepo.checkByOrgIdAndTypeAndSubType1(orgId, type[0], entry.getKey())) {
                    continue;
                }
            }
            conf.setCreateTime(new Date());
            conf.setUpdateTime(conf.getCreateTime());
            if (botType) {
                botCommonConfigRepo.save((QaCommonConfigEntity) conf);
            } else {
                commonConfigRepo.save((CommonConfigEntity) conf);
            }
        }
    }
}
