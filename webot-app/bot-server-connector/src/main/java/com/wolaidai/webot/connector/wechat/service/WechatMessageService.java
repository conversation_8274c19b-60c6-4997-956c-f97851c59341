package com.wolaidai.webot.connector.wechat.service;

import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.wechat.utils.MsgUtils;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.builder.kefu.WxMsgMenuBuilder;
import me.chanjar.weixin.open.api.WxOpenService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@AllArgsConstructor
public class WechatMessageService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    private final AppPropertyConfig appPropertyConfig;
    private final StringRedisTemplate csStringRedisTemplate;
    private final ApplicationContext applicationContext;

    public  void sendWechatMsg(WxMpService wxMpService, String clientId, ChatHistoryEntity chatHistory) throws Exception {
        if(Objects.equals(chatHistory.getType(),"text")){
            String text = MsgUtils.handleText(chatHistory.getContent());
            if(StringUtils.isNotBlank(text)) {
                wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().toUser(clientId).content(text).build());
            }
        }else if(Objects.equals(chatHistory.getType(),"image")){
            WxMediaUploadResult wxMediaUploadResult = wxMpService.getMaterialService().mediaUpload(WxConsts.MediaFileType.IMAGE, "jpg", new ByteArrayInputStream(HttpClientUtil.getFile(getMediaUrl(chatHistory.getContent()))));
            wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.IMAGE().toUser(clientId).mediaId(wxMediaUploadResult.getMediaId()).build());
        }else if(Objects.equals(chatHistory.getType(),"voice")){
            WxMediaUploadResult wxMediaUploadResult = wxMpService.getMaterialService().mediaUpload(WxConsts.MediaFileType.VOICE, "mp3", new ByteArrayInputStream(HttpClientUtil.getFile(getMediaUrl(chatHistory.getContent()))));
            wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.VOICE().toUser(clientId).mediaId(wxMediaUploadResult.getMediaId()).build());
        }else if(Objects.equals(chatHistory.getType(),"video")){
            WxMediaUploadResult wxMediaUploadResult = wxMpService.getMaterialService().mediaUpload(WxConsts.MediaFileType.VIDEO, "mp4", new ByteArrayInputStream(HttpClientUtil.getFile(getMediaUrl(chatHistory.getContent()))));
            wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.VIDEO().toUser(clientId).mediaId(wxMediaUploadResult.getMediaId()).build());
        }else if(Objects.equals(chatHistory.getType(),"menu")){
            Map<String, Object> menu = chatHistory.getMenu();
            if(menu!=null) {
                WxMsgMenuBuilder wxMsgMenuBuilder = WxMpKefuMessage.MSGMENU().headContent(MsgUtils.handleText(Objects.toString(menu.get("head_content")))).tailContent(MsgUtils.handleText(Objects.toString(menu.get("tail_content")))).toUser(clientId);
                List<Object> list = (List<Object>) menu.get("list");
                for (int j = 0; j < list.size(); j++) {
                    Map<String,Object> menuItem = (Map<String, Object>) list.get(j);
                    wxMsgMenuBuilder.addMenus(new WxMpKefuMessage.MsgMenu(Objects.toString(menuItem.get("id")), (String) menuItem.get("content")));
                }
                sendWechatMenuMsg(wxMpService,wxMsgMenuBuilder);
            }
        }
    }

    private String getMediaUrl(String mediaId){
        int materialType = 1;
        if(NumberUtils.isDigits(mediaId)){
            materialType = 2;
        }
        return appPropertyConfig.getFileServerUrl()+"/"+materialType+"/"+mediaId;
    }

    public void sendWechatMenuMsg(WxMpService wxMpService, WxMsgMenuBuilder wxMsgMenuBuilder) throws WxErrorException {
        WxMpKefuMessage kefuMessage = wxMsgMenuBuilder.build();
        if (appPropertyConfig.isEnvTest()) {
            String headContent = Objects.toString(kefuMessage.getHeadContent(),"");
            String menuContent = MsgUtils.msgMenuToText(kefuMessage.getMsgMenus());
            String tailContent = Objects.toString(kefuMessage.getTailContent(),"");
            String content = StringUtils.joinWith("\n",headContent,menuContent,tailContent).trim();
            wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().toUser(kefuMessage.getToUser()).content(content).build());
        } else {
            wxMpService.getKefuService().sendKefuMessage(kefuMessage);
        }
    }

    public WxMpService getWxMpService(String appId){
        if(appId==null){
            return null;
        }
        WxMpService actualWxMpService = null;
        try {
            WxOpenService wxOpenService = applicationContext.getBean(WxOpenService.class);
            if (wxOpenService != null) {
                actualWxMpService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appId);
            } else {
                WxMpService wxMpService = applicationContext.getBean(WxMpService.class);
                if (wxMpService != null) {
                    actualWxMpService = wxMpService.switchoverTo(appId);
                }
            }
        }catch (Exception e){
            LOGGER.error("获取WxMpService错误",e);
        }
        return actualWxMpService;
    }

    public void sendWechatMsg(String clientId,Integer botId,ChatHistoryEntity chatHistory){
        String appId = csStringRedisTemplate.opsForValue().get(String.format(RedisKey.WECHAT_CLIENTID_BOTID_APPID, clientId, botId));
        WxMpService wxMpService = getWxMpService(appId);
        if(wxMpService!=null){
            try {
                sendWechatMsg(wxMpService, clientId, chatHistory);
            } catch (Exception e) {
                LOGGER.error("发送微信消息失败,appId:{}",wxMpService.getWxMpConfigStorage().getAppId(),e);
            }
        }else{
            LOGGER.error("找不到指定appId:{}",appId);
        }
    }

    public void sendWechatTextMsg(String clientId,Integer botId,String content){
        String appId = csStringRedisTemplate.opsForValue().get(String.format(RedisKey.WECHAT_CLIENTID_BOTID_APPID, clientId, botId));
        WxMpService wxMpService = getWxMpService(appId);
        if(wxMpService!=null){
            try {
                String text = MsgUtils.handleText(content);
                if(StringUtils.isNotBlank(text)) {
                    wxMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().toUser(clientId).content(text).build());
                }
            } catch (Exception e) {
                LOGGER.error("发送微信文本消息失败,appId:{}",appId,e);
            }
        }else{
            LOGGER.error("找不到指定appId:{}",appId);
        }
    }

    public void sendWechatMenuMsg(String clientId,Integer botId, WxMsgMenuBuilder wxMsgMenuBuilder){
        String appId = csStringRedisTemplate.opsForValue().get(String.format(RedisKey.WECHAT_CLIENTID_BOTID_APPID, clientId, botId));
        WxMpService wxMpService = getWxMpService(appId);
        if(wxMpService!=null){
            try {
                sendWechatMenuMsg(wxMpService,wxMsgMenuBuilder);
            } catch (Exception e) {
                LOGGER.error("发送微信菜单消息失败,appId:{}",appId,e);
            }
        }else{
            LOGGER.error("找不到指定appId:{}",appId);
        }
    }

    public boolean isWechat(String clientId,Integer botId){
        return csStringRedisTemplate.hasKey(String.format(RedisKey.WECHAT_CLIENTID_BOTID_APPID, clientId, botId));
    }
}
