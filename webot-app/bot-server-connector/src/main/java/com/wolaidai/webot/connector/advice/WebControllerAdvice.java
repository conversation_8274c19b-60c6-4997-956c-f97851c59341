package com.wolaidai.webot.connector.advice;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.common.util.IpUtil;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.constant.WebStatusConstants;
import com.wolaidai.webot.connector.controller.BaseController;
import com.wolaidai.webot.connector.model.BaseModel;
import com.wolaidai.webot.connector.model.ResponseModel;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.concurrent.TimeUnit;

@Aspect
@Configuration
public class WebControllerAdvice {
    private static final Logger LOGGER = LoggerFactory.getLogger(WebControllerAdvice.class);

    @Around("this(baseController)")
    public Object doReturnMsg(ProceedingJoinPoint pjp, BaseController baseController) throws Throwable {
        long start = System.currentTimeMillis();
        String requestBody = null;
        Object[] args = pjp.getArgs();
        if (args != null) {
            for (Object arg : args) {
                if (arg instanceof BaseModel) {
                    requestBody = JSON.toJSONString(arg);
                }
            }
        }
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();
        StringBuilder commonRequestMessage = createCommonRequestMessage(request);
        LOGGER.info("Before request:[{}]", createRequestMessage(commonRequestMessage.toString(), requestBody));
        Object ret = pjp.proceed();
        String responseBody = null;
        if (ret != null) {
            responseBody = JSON.toJSONString(ret);
        }
        long cost = System.currentTimeMillis() - start;
        LOGGER.info("After request:[{}],cost:{}ms", createResponseMessage(commonRequestMessage.toString(), responseBody), cost);
        return ret;
    }

    private String createRequestMessage(String commonRequestMessage, String requestBody) {
        StringBuilder msg = new StringBuilder();
        msg.append(commonRequestMessage);
        if (requestBody != null) {
            msg.append(";requestBody=").append(requestBody);
        }
        return msg.toString();
    }

    private String createResponseMessage(String commonRequestMessage, String responseBody) {
        StringBuilder msg = new StringBuilder();
        msg.append(commonRequestMessage);
        if (responseBody != null) {
            msg.append(";responseBody=").append(responseBody);
        }
        return msg.toString();
    }

    private StringBuilder createCommonRequestMessage(HttpServletRequest request) {
        StringBuilder msg = new StringBuilder();
        msg.append("uri=").append(request.getRequestURI());
        String queryString = request.getQueryString();
        if (queryString != null) {
            msg.append('?').append(queryString);
        }
        msg.append(";method=").append(request.getMethod());
        String client = IpUtil.getIpAddress(request);
        if (StringUtils.hasLength(client)) {
            msg.append(";client=").append(client);
        }
        HttpSession session = request.getSession(false);
        if (session != null) {
            msg.append(";session=").append(session.getId());
        }
        if(request.getRemoteUser()!=null) {
            msg.append(";user=").append(request.getRemoteUser());
        }
        return msg;
    }
}
