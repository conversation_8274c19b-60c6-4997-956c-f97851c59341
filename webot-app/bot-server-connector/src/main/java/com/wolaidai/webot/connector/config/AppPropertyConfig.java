package com.wolaidai.webot.connector.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties("app")
@Data
public class AppPropertyConfig {
    //是否测试环境
    private boolean envTest;
    //文件服务器地址
    private String fileServerUrl;

    private int socketPort = 9093;
    private String socketContextPath = "/";
    private String socketRequestPath = "/socket.io";
    private String botEngineUrl;

    private Integer taskCorePool = 8;
    private Integer taskMaxPool = 16;
    private Integer taskQueueCapacity = 100;

    private String aliAccessKeyId;
    private String aliAccessKeySecret;
    private String aliAppKey;

    private Integer checkMsgFlag = 0;
    
    private String baseFileDir;
    private String ossEndpoint;
    private String ossAccessKeyID;
    private String ossAccessKeySecret;
    private String ossBucketName;
    
    private boolean notifyFeedback;
    private String feedbackDetailUrl;
    private String webhookUrl;
    
    private String faceDocumentsUrl;
    private Integer faceTokenTimeoutMinutes = 10;
    private Integer maxFaceDetectFailureTimes = 5;
    private String faceDetectionUrl;
    private String satisfactionUrl;
    private String verifyUrl;

    private String publicAccountUrl;
    private boolean checkRedisConfig = true;

    private boolean faceDetectionVendorFlag = true;
    private String faceDetectionVendorUrl;
    private String faceDetectionVendorResultUrl;
    private String faceDetectionVendorCallbackUrl;
    private Integer faceDetectionErrorLimit;

    private Integer faceLinkIntervalMinutes = 3;
    private String customerInfoUrl;

    private String docQaUrl;
    private String docQaSearchDocId;

    private String mongoEncUrl;
    private String mongoEncAppId;
    private String mongoEncSecret;
    private String mongoEncDb;

    private String aiSummaryUrl;
    private Integer csProductId;
    private String summaryWebhookUrl;
    
    private String kefuUrlPre;
    
    private String testUserMobile;
}
