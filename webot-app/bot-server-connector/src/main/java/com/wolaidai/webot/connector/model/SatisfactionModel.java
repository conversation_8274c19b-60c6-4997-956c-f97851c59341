package com.wolaidai.webot.connector.model;

import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "提交满意度请求数据")
public class SatisfactionModel {
    
    @ApiModelProperty(value = "会话key")
    private String sessionKey;
    @ApiModelProperty(value = "评价星级")
    private Integer level;
    @ApiModelProperty(value = "评价文本内容")
    private String content;
    @ApiModelProperty(value = "评价标签")
    private List<String> labels = new ArrayList<>();

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getLabels() {
        return labels;
    }

    public void setLabels(List<String> labels) {
        this.labels = labels;
    }

}
