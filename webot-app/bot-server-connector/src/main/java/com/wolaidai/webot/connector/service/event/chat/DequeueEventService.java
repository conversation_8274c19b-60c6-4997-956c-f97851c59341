package com.wolaidai.webot.connector.service.event.chat;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.model.DequeueModel;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.business.ManualChatService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class DequeueEventService extends BaseEventService {
    private final ManualChatService manualChatService;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        JSONObject data = (JSONObject)model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        DequeueModel inqueueModel = JSON.parseObject(data.toString(), DequeueModel.class);
        String clientId = inqueueModel.getClientId();
        String accessKey = inqueueModel.getAccessKey();
        if(StringUtils.isNotBlank(clientId)&&StringUtils.isNotBlank(accessKey)){
            manualChatService.dequeue(clientId,accessKey);
            if(callback!=null){
                callback.sendAcknowledgement(new JSONObject().put("ret", 0).put("msg", "取消排队成功").put("timestamp", System.currentTimeMillis()));
            }
        }
        return null;
    }
}
