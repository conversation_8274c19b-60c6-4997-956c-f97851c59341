package com.wolaidai.webot.connector.constant;

public interface ManualConfigConstants {
    String AUTO_REPLY_TYPE = "autoreply";
    String AUTO_REPLY_NOTICE_MSG = "noticeMsg";
    String AUTO_REPLY_SERVICE_OFFLINE_MSG = "serviceOfflineMsg";
    String AUTO_REPLY_CUSTOMER_OFFLINE_MSG = "customerOfflineMsg";
    String AUTO_REPLY_SERVICE_TIMEOUT_OFFLINE_MSG = "serviceTimeoutOfflineMsg";
    String AUTO_REPLY_SERVICE_TIMEOUT_MSG = "serviceTimeoutMsg";
    String AUTO_REPLY_CUSTOMER_TIMEOUT_MSG = "customerTimeoutMsg";
    String AUTO_REPLY_QUEUE_EXCEED_MSG = "queueExceedMsg";
    String AUTO_REPLY_WELCOME_MSG = "welcomeMsg";
    String AUTO_REPLY_CLOSE_SESSION_MSG = "closeSessionMsg";
    String AUTO_REPLY_CUSTOMER_QUEUE_MSG = "customerQueueMsg";
    String AUTO_REPLY_BLACKLIST_MSG = "blackListMsg";

    String SATISFACTION_TYPE = "satisfaction";
    String SERVICEUSER_TYPE = "serviceuser";
    String WORKBENCH_TYPE = "workbench";
    String WORKTIME_TYPE = "worktime";

    String AVG_RESP = "avgResp";
    String FIRST_RESP = "firstResp";
    String SESSION_RESP = "sessionResp";

}
