package com.wolaidai.webot.connector.service.event.chat.inner;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.constant.ManualConfigConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.constant.SocketNamespace;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.model.SocketRoomModel;
import com.wolaidai.webot.connector.model.inner.SessionKickModel;
import com.wolaidai.webot.connector.service.SocketService;
import com.wolaidai.webot.connector.service.business.ChatRecordService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.data.mysql.entity.chat.*;
import com.wolaidai.webot.data.mysql.repo.ChatMembersSnapshotRepo;
import com.wolaidai.webot.data.mysql.repo.ChatRoomRepo;
import com.wolaidai.webot.data.mysql.repo.ChatSessionRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class SessionKickEventService extends BaseEventService {

    private final StringRedisTemplate csStringRedisTemplate;
    private final ChatRoomRepo chatRoomRepo;
    private final UserStateRepo userStateRepo;
    private final ChatSessionRepo chatSessionRepo;
    private final SocketService socketService;
    private final ChatRecordService chatRecordService;
    private final ChatMembersSnapshotRepo chatMembersSnapshotRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject)model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        SessionKickModel sessionModel = JSON.parseObject(data.toString(), SessionKickModel.class);
        if (ManualConfigConstants.WORKBENCH_TYPE.equals(sessionModel.getSource())) {
            Integer roomId = sessionModel.getRoomId();
            String kickEmail = sessionModel.getKickEmail();
            if (roomId == null || StringUtils.isBlank(kickEmail)) {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret",-1)
                            .put("msg", "param lost").put("timestamp", System.currentTimeMillis()));
                }
                return null;
            }
            ChatRoomEntity chatRoomEntity = chatRoomRepo.findById(roomId).orElse(null);
            if (chatRoomEntity == null) {
                if (callback != null ) {
                    callback.sendAcknowledgement(new JSONObject().put("ret",-1)
                            .put("msg", "该群聊已不存在").put("timestamp", System.currentTimeMillis()));
                }
                return null;
            }
            Integer orgId = chatRoomEntity.getOrgId();
            Integer sessionKey = sessionModel.getSessionKey();
            ChatSessionEntity sessionEntity = chatSessionRepo.findByIdAndOrgId(sessionKey, orgId);
            if (sessionEntity == null) {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret",-1)
                            .put("msg", "会话不存在").put("timestamp", System.currentTimeMillis()));
                }
                return null;
            }
            UserStateEntity userState = userStateRepo.findByOrgIdAndEmail(orgId, kickEmail);
            if (userState == null) {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret",-1)
                            .put("msg", "该用户账户异常").put("timestamp", System.currentTimeMillis()));
                }
                return null;
            }
            //非群主不能踢人
            String email = sessionModel.getEmail();
            if (!Objects.equals(email, chatRoomEntity.getCreator())) {
                if (callback != null ) {
                    callback.sendAcknowledgement(new JSONObject().put("ret",-1)
                            .put("msg", "你不是群主,无权移除其他成员").put("timestamp", System.currentTimeMillis()));
                }
                return null;
            }
            Date dateNow = new Date();
            chatRoomEntity.getRoomUsers().removeIf(c -> kickEmail.equals(c.getUser().getEmail()));
            chatRoomEntity.setUpdateTime(dateNow);
            chatRoomRepo.save(chatRoomEntity);
            String eventMsg = String.format("%s %s被移除了群聊", ObjectUtils.defaultIfNull(userState.getNickName(), ""), ObjectUtils.defaultIfNull(userState.getName(), ""));
            HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
            ChatRecordEntity chatRecord = chatRecordService.createChatRecord(null, email, null, eventMsg,
                    ChatRecordEntity.EVENT_TYPE, sessionEntity, chatMembersSnapshotRepo.findFirstBySessionIdOrderByIdDesc(sessionKey), null, new com.alibaba.fastjson.JSONObject().fluentPut("sessionKey", sessionKey));
            //保存参与会话快照
            ChatMembersSnapshotEntity snapshotEntity =
                    new ChatMembersSnapshotEntity(sessionKey, chatRoomEntity.getRoomUsers().stream().map(ChatRoomUsersEntity::getUser)
                            .map(UserStateEntity::getEmail).collect(Collectors.toList()));
            chatMembersSnapshotRepo.save(snapshotEntity);
            String toSocketId = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, kickEmail), "socketId");
            if (StringUtils.isNotBlank(toSocketId)) { //移除聊天室
                socketService.joinOrLeaveGlobalRoom(SocketNamespace.CHAT,
                        SocketRoomModel.REMOVE_TYPE, Collections.singletonList("session_" + sessionKey), toSocketId);
                models.add(new SocketPacketModel(null, toSocketId, SocketEvent.INFO, MessageUtil.toJsonString(chatRecord)));
            }
            //设置退群事件,保存会话信息
            sessionEntity.setLastMsg(eventMsg);
            sessionEntity.setLastMsgTime(dateNow);
            sessionEntity.setLastMsgSender(email);
            sessionEntity.setUpdateTime(dateNow);
            chatSessionRepo.save(sessionEntity);
            //广播群
            models.add(new SocketPacketModel("session_" + sessionKey, toSocketId, SocketEvent.EVENT,
                    new com.alibaba.fastjson.JSONObject().fluentPut("type", SocketEvent.INNER_SESSION_RELOAD_TYPE).toJSONString()));
        }
        return models;
    }
}
