package com.wolaidai.webot.connector.service.event.chat.inner;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.constant.ManualConfigConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.constant.SocketNamespace;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.model.SocketRoomModel;
import com.wolaidai.webot.connector.model.inner.SessionInitModel;
import com.wolaidai.webot.connector.service.SocketService;
import com.wolaidai.webot.connector.service.business.ChatRecordService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.data.mysql.entity.chat.*;
import com.wolaidai.webot.data.mysql.repo.*;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Transactional
public class SessionInitEventService extends BaseEventService {

    private final StringRedisTemplate csStringRedisTemplate;
    private final SocketService socketService;
    private final UserStateRepo userStateRepo;
    private final ChatSessionRepo chatSessionRepo;
    private final ChatRoomRepo chatRoomRepo;
    private final ChatRecordService chatRecordService;
    private final ComplexChatSessionRepo complexChatSessionRepo;
    private final ChatMembersSnapshotRepo chatMembersSnapshotRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject)model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        SessionInitModel sessionModel = JSON.parseObject(data.toString(), SessionInitModel.class);
        if (ManualConfigConstants.WORKBENCH_TYPE.equals(sessionModel.getSource())) {
            Integer orgId = sessionModel.getOrgId();
            Integer sessionId = sessionModel.getSessionKey();
            String from = sessionModel.getEmail();
            UserStateEntity userFrom = userStateRepo.findByOrgIdAndEmail(orgId, from);
            if (userFrom == null) {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret", -1)
                            .put("msg", "账户异常").put("timestamp", System.currentTimeMillis()));
                }
                return null;
            }
            List<String> toEmails = sessionModel.getToEmails();
            Date dateNow = new Date();
            int toSize = toEmails.size(); //发起聊天的人数
            ChatSessionEntity sessionEntity;
            if (toSize == 1) { //单聊查找历史有无单聊过
                List<ChatSessionEntity> allSingleSessions = complexChatSessionRepo.findByOrgIdAndUserEmailAndRoomIsNull(orgId, from);//所有相关的单聊会话
                if (CollectionUtils.isNotEmpty(allSingleSessions)) {
                    ChatSessionEntity existSession = allSingleSessions.stream().filter(s -> s.getMembers().stream().anyMatch(c -> c.getUser().getEmail().equals(toEmails.get(0)))).findAny().orElse(null);
                    if (existSession != null) {
                        sessionEntity = existSession;
                        sessionId = sessionEntity.getId();
                    }
                }
            }
            if (sessionId == null) { //新开会话
                List<String> socketIds = new ArrayList<>();
                sessionEntity = new ChatSessionEntity();
                sessionEntity.setOrgId(orgId);
                sessionEntity.setCreator(from); //聊天发起人
                //添加发起人
                socketIds.add(model.getSocketId());
                sessionEntity.getMembers().add(new ChatMembersEntity(ChatMembersEntity.STATUS_ACTIVE, sessionEntity, userFrom));
                sessionEntity.setCreateTime(dateNow); //聊天发起人
                HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                ChatRoomEntity chatRoomEntity = null;
                List<String> nameList = new ArrayList<>();
                for (String email : toEmails) {
                    UserStateEntity userTo = userStateRepo.findByOrgIdAndEmail(orgId, email);
                    if (userTo == null) {
                        if (callback != null) {
                            callback.sendAcknowledgement(new JSONObject().put("ret", -1)
                                    .put("msg", "账户异常").put("timestamp", System.currentTimeMillis()));
                        }
                        return null;
                    }
                    //添加会话成员
                    sessionEntity.getMembers().add(new ChatMembersEntity(
                            (toSize == 1 ? ChatMembersEntity.STATUS_INACTIVE : ChatMembersEntity.STATUS_ACTIVE), sessionEntity, userTo));
                    chatSessionRepo.save(sessionEntity);
                    if (toSize > 1) { //发起的是群聊
                        if (chatRoomEntity == null) {
                            chatRoomEntity = new ChatRoomEntity();
                            chatRoomEntity.setOrgId(orgId);
                            chatRoomEntity.getRoomUsers().add(new ChatRoomUsersEntity(chatRoomEntity, userFrom));
                            chatRoomEntity.setCreator(from);
                            chatRoomEntity.setCreateTime(dateNow);
                        }
                        nameList = userStateRepo.findByOrgIdAndEmailIn(orgId, toEmails);
                        String toSocketId = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, email), "socketId");
                        if (StringUtils.isNotBlank(toSocketId)) {
                            socketIds.add(toSocketId);
                        }
                        //添加聊天室成员
                        chatRoomEntity.getRoomUsers().add(new ChatRoomUsersEntity(chatRoomEntity, userTo));
                    }
                }
                chatSessionRepo.save(sessionEntity);
                sessionId = sessionEntity.getId();
                //发起的是群聊
                if (toSize > 1) {
                    //保存参与会话快照
                    ChatMembersSnapshotEntity snapshotEntity = new ChatMembersSnapshotEntity(sessionId,
                            chatRoomEntity.getRoomUsers().stream().map(ChatRoomUsersEntity :: getUser).map(UserStateEntity::getEmail).collect(Collectors.toList()));
                    chatMembersSnapshotRepo.save(snapshotEntity);
                    ChatRecordEntity chatRecord = chatRecordService.createChatRecord(null, from, null,
                            String.format("%s %s邀请%s加入了群聊", ObjectUtils.defaultIfNull(userFrom.getNickName(), ""), ObjectUtils.defaultIfNull(userFrom.getName(), ""),
                                    String.join("、", nameList)), ChatRecordEntity.EVENT_TYPE, sessionEntity, snapshotEntity, null, new com.alibaba.fastjson.JSONObject().fluentPut("sessionKey", sessionEntity.getId()));
                    models.add(new SocketPacketModel("session_" + sessionId, null, SocketEvent.INFO, MessageUtil.toJsonString(chatRecord)));
                    //保存聊天室信息
                    chatRoomEntity.setUpdateTime(dateNow);
                    chatRoomRepo.save(chatRoomEntity);
                    sessionEntity.setRoom(chatRoomEntity);
                    sessionEntity.setLastMsg(chatRecord.getContent());
                }
                //更新会话信息
                sessionEntity.setLastMsgSender(from);
                sessionEntity.setLastMsgTime(dateNow);
                sessionEntity.setUpdateTime(dateNow);
                chatSessionRepo.save(sessionEntity);
                socketService.joinOrLeaveGlobalRoom(SocketNamespace.CHAT, SocketRoomModel.ADD_TYPE, Collections.singletonList("session_" + sessionId), socketIds.toArray(new String[]{}));
            } else {
                sessionEntity = chatSessionRepo.findById(sessionId).orElse(null);
                if (sessionEntity == null) {
                    if (callback != null) {
                        callback.sendAcknowledgement(new JSONObject().put("ret", -1)
                                .put("msg", "账户异常").put("timestamp", System.currentTimeMillis()));
                    }
                    return null;
                }
                //更新会话成员状态的时间以便排序
                sessionEntity.getMembers().stream().filter(u -> u.getUser().equals(userFrom)).forEach(u -> {
                    if (ChatMembersEntity.STATUS_INACTIVE.equals(u.getStatus())) {
                        u.setStatus(ChatMembersEntity.STATUS_ACTIVE);
                    }
                    u.setUpdateTime(new Date());
                });
                chatSessionRepo.save(sessionEntity);
            }
            if (callback != null) {
                callback.sendAcknowledgement(new JSONObject().put("ret", 0).put("sessionKey", sessionId).put("timestamp", System.currentTimeMillis()));
            }
        }
        return models;
    }
}
