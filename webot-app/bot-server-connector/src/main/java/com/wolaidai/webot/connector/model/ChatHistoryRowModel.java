package com.wolaidai.webot.connector.model;

import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
public class ChatHistoryRowModel extends BaseModel {
    private String sender;
    private String type;
    private String eventKey;
    private String content;
    private String msgId;
    private Date date;
    private Boolean recall;
    private Date recallTime;
    private Integer skillGroupId;
    private Map<String, Object> media;
    private Map<String, Object> extend;
    private Map<String, Object> menu;
    private Map<String, Object> manual;

    public ChatHistoryRowModel(ChatHistoryEntity c) {
        this.sender = c.getSender();
        this.type = c.getType();
        this.eventKey = c.getEventKey();
        this.content = c.getContent();
        this.msgId = c.getMsgId();
        this.date = c.getDate();
        this.recall = c.getRecall();
        this.recallTime = c.getRecallTime();
        this.skillGroupId = c.getSkillGroupId();
        this.media = c.getMedia();
        this.extend = c.getExtend();
        this.menu = c.getMenu();
        this.manual = c.getManual();
    }
}
