package com.wolaidai.webot.connector.service.impl;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.constant.RedisConstants;
import com.wolaidai.webot.connector.handler.SocketServerWrapper;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.model.SocketRoomModel;
import com.wolaidai.webot.connector.service.SocketService;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoNamespace;
import io.socket.socketio.server.SocketIoServer;
import io.socket.socketio.server.SocketIoSocket;
import org.apache.commons.lang3.ArrayUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class SocketServiceImpl implements SocketService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    @Lazy
    private SocketServerWrapper socketServerWrapper;

    @Resource
    private RedisTemplate csRedisTemplate;

    @Resource
    private StringRedisTemplate csStringRedisTemplate;

    @Override
    public boolean sendMessage(String namespace, SocketPacketModel socketPacketModel) {
        SocketIoServer socketIoServer = socketServerWrapper.getSocketIoServer();
        if(!socketIoServer.hasNamespace(namespace)){
            LOGGER.error("namespace:{} not exist",namespace);
            return false;
        }
        SocketIoNamespace ns = socketIoServer.namespace(namespace);
        if (socketPacketModel != null) {
            if (socketPacketModel.getRoom() != null) {
                SocketIoSocket[] socketIoSockets = ns.getAdapter().listClients(socketPacketModel.getRoom());
                if (!ArrayUtils.isEmpty(socketIoSockets)) {
                    for (SocketIoSocket socketIoSocket : socketIoSockets) {
                        if (!Objects.equals(socketIoSocket.getId(), socketPacketModel.getSocketId())) {
                            send(socketIoSocket,socketPacketModel);
                        }
                    }
                }
            }else{
                if(socketPacketModel.getSocketId() != null){
                    SocketIoSocket[] socketIoSockets = ns.getAdapter().listClients(socketPacketModel.getSocketId());
                    if (!ArrayUtils.isEmpty(socketIoSockets)) {
                        for (SocketIoSocket socketIoSocket : socketIoSockets) {
                            if (Objects.equals(socketIoSocket.getId(), socketPacketModel.getSocketId())) {
                                send(socketIoSocket, socketPacketModel);
                                break;
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    public boolean joinOrLeaveLocalRoom(String namespace, int type, List<String> rooms, String... socketIds) {
        SocketIoServer socketIoServer = socketServerWrapper.getSocketIoServer();
        if(!socketIoServer.hasNamespace(namespace)){
            LOGGER.error("namespace:{} not exist",namespace);
            return false;
        }
        SocketIoNamespace ns = socketIoServer.namespace(namespace);
        for (String socketId : socketIds) {
            if(socketId!=null) {
                SocketIoSocket[] socketIoSockets = ns.getAdapter().listClients(socketId);
                if (!ArrayUtils.isEmpty(socketIoSockets)) {
                    for (SocketIoSocket socketIoSocket : socketIoSockets) {
                        if(type==SocketRoomModel.ADD_TYPE) {
                            socketIoSocket.joinRoom(rooms.toArray(new String[]{}));
                        }else if(type==SocketRoomModel.REMOVE_TYPE){
                            socketIoSocket.leaveRoom(rooms.toArray(new String[]{}));
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    public boolean joinOrLeaveGlobalRoom(String namespace, int type, List<String> rooms, String... socketIds) {
        long start = System.currentTimeMillis();
        String requestId = UUID.randomUUID().toString();
        long publish = csStringRedisTemplate.getConnectionFactory().getConnection().publish(String.format(RedisConstants.SOCKET_ROOMS_CHANNEL_PREFIX, namespace).getBytes(),
                JSON.toJSONString(new SocketRoomModel(namespace, rooms,Arrays.asList(socketIds),type,requestId)).getBytes(StandardCharsets.UTF_8));
        long actualCount = 0;
        boolean flag = false;
        while (true) {
            Object o = csStringRedisTemplate.opsForList().leftPop(String.format(RedisKey.ROOM_REQUEST_ID_KEY,requestId), 5, TimeUnit.SECONDS);
            if(o!=null){
                actualCount++;
                if(actualCount>=publish) {
                    flag = true;
                    break;
                }
            }else{
                break;
            }
        }
        long cost = System.currentTimeMillis()-start;
        LOGGER.info("joinOrLeaveGlobalRoom room cost {}ms,type:{},rooms:{},socketIds:{}",cost,type,rooms,socketIds);
        return flag;
    }

    private void send(SocketIoSocket socketIoSocket,SocketPacketModel socketPacketModel) {
        LOGGER.info("send socket message,sender:{},data:{}",socketIoSocket.getId(),JSON.toJSONString(socketPacketModel));
        Object[] data = null;
        if(socketPacketModel.getData() instanceof String){
            data = new Object[]{new JSONObject(socketPacketModel.getData().toString())};
        }else{
            data = new Object[]{new JSONObject(socketPacketModel.getData())};
        }
        if(socketPacketModel.getCallbackId()==null) {
            socketIoSocket.send(socketPacketModel.getEvent(), data);
        }else{
            socketIoSocket.send(socketPacketModel.getEvent(), data, args -> {
                if(args!=null&&args.length>0) {
                    socketPacketModel.setData(Arrays.stream(args).map(Objects::toString).collect(Collectors.toList()).toArray());
                }else{
                    socketPacketModel.setData(null);
                }
                csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_RESPONSE_CHANNEL_PREFIX, socketIoSocket.getNamespace().getName()), socketPacketModel);
            });
        }
    }
}
