package com.wolaidai.webot.connector.utils;

import ws.schild.jave.Encoder;
import ws.schild.jave.EncoderException;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.encode.AudioAttributes;
import ws.schild.jave.encode.EncodingAttributes;

import javax.sound.sampled.*;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;

public class VoiceUtils {

    public static void truncateVoice(File source,File target,int beginTime,int endTime,String format) throws EncoderException {
        AudioAttributes audio = new AudioAttributes();
        if("wav".equalsIgnoreCase(format)) {
            audio.setCodec("pcm_s16le");
        }else if("mp3".equalsIgnoreCase(format)){
            audio.setCodec("libmp3lame");
        }else{
            throw new RuntimeException("format只支持wav和mp3");
        }
        audio.setBitRate(64000);
        audio.setChannels(1);
        audio.setSamplingRate(8000);
        EncodingAttributes attrs = new EncodingAttributes();
        attrs.setOutputFormat(format);
        if(endTime!=0&&beginTime<=endTime) {
            attrs.setOffset(BigDecimal.valueOf(beginTime).divide(BigDecimal.valueOf(1000), 0, RoundingMode.DOWN).floatValue());
            attrs.setDuration(BigDecimal.valueOf(endTime).divide(BigDecimal.valueOf(1000), 0, RoundingMode.UP).floatValue());
        }
        attrs.setAudioAttributes(audio);
        Encoder encoder = new Encoder();
        encoder.encode(new MultimediaObject(source), target, attrs);
    }

    public static void voiceFileConversion(File source, File target, String format) throws EncoderException {
        AudioAttributes audio = new AudioAttributes();
        if("wav".equalsIgnoreCase(format)) {
            audio.setCodec("pcm_s16le");
        }else if("mp3".equalsIgnoreCase(format)){
            audio.setCodec("libmp3lame");
        }else{
            throw new RuntimeException("format只支持wav和mp3");
        }
        audio.setBitRate(64000);
        audio.setChannels(1);
        audio.setSamplingRate(8000);
        EncodingAttributes attrs = new EncodingAttributes();
        attrs.setOutputFormat(format);
        attrs.setAudioAttributes(audio);
        Encoder encoder = new Encoder();
        encoder.encode(new MultimediaObject(source), target, attrs);
    }

    public static void splitToLeftAndRightChannel(File sourceFile, File leftTargetFile, File rightTargetFile) throws IOException, UnsupportedAudioFileException {
        AudioFileFormat fileFormat = AudioSystem.getAudioFileFormat(sourceFile);
        AudioFileFormat.Type targetFileType = fileFormat.getType();
        AudioFormat audioFormat = fileFormat.getFormat();

        AudioInputStream inputAIS = AudioSystem.getAudioInputStream(sourceFile);

        ByteArrayOutputStream leftbaos = new ByteArrayOutputStream();
        ByteArrayOutputStream rightbaos = new ByteArrayOutputStream();

        byte[] bytes = new byte[(audioFormat.getSampleSizeInBits() / 8) * 2];

        while (true) {
            int readsize = inputAIS.read(bytes);
            if (readsize == -1) {
                break;
            }
            rightbaos.write(bytes, 0, bytes.length / 2);
            leftbaos.write(bytes, bytes.length / 2, bytes.length / 2);
        }

        byte[] leftData = leftbaos.toByteArray();
        byte[] rightData = rightbaos.toByteArray();

        AudioFormat outFormat = new AudioFormat(audioFormat.getEncoding(), audioFormat.getSampleRate(), audioFormat.getSampleSizeInBits(), 1, audioFormat.getFrameSize() / 2, audioFormat.getFrameRate(), audioFormat.isBigEndian());

        ByteArrayInputStream leftbais = new ByteArrayInputStream(leftData);
        AudioInputStream leftoutputAIS = new AudioInputStream(leftbais, outFormat, leftData.length / outFormat.getFrameSize());
        AudioSystem.write(leftoutputAIS, targetFileType, leftTargetFile);

        ByteArrayInputStream rightbais = new ByteArrayInputStream(rightData);
        AudioInputStream rightoutputAIS = new AudioInputStream(rightbais, outFormat, rightData.length / outFormat.getFrameSize());
        AudioSystem.write(rightoutputAIS, targetFileType, rightTargetFile);
    }
}
