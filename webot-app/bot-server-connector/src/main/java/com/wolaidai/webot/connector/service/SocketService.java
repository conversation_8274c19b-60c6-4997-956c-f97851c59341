package com.wolaidai.webot.connector.service;

import com.wolaidai.webot.connector.model.SocketPacketModel;

import java.util.List;

public interface SocketService {
    boolean sendMessage(String namespace, SocketPacketModel socketPacketModel);

    boolean joinOrLeaveLocalRoom(String namespace, int type, List<String> rooms, String... socketIds);

    boolean joinOrLeaveGlobalRoom(String namespace, int type, List<String> rooms, String... socketIds);
}
