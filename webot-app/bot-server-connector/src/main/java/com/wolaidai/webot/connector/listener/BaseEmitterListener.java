package com.wolaidai.webot.connector.listener;

import io.socket.engineio.server.Emitter;
import io.socket.socketio.server.SocketIoSocket;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.List;

public abstract class BaseEmitterListener implements Emitter.Listener{
    protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    protected String getClientIp(SocketIoSocket socket){
        List<String> clientIp = socket.getInitialHeaders().get("Client-Ip");
        if(!CollectionUtils.isEmpty(clientIp)){
            return clientIp.get(0);
        }
        return null;
    }

    protected String getUa(SocketIoSocket socket){
        List<String> ua = socket.getInitialHeaders().get("User-Agent");
        if(!CollectionUtils.isEmpty(ua)){
            return ua.get(0);
        }
        return null;
    }

    public <T> T get(Object[] array,int index){
        return (T) ArrayUtils.get(array, index);
    }

    public SocketIoSocket.ReceivedByLocalAcknowledgementCallback getCallback(Object[] array){
        if(array==null||array.length==0){
            return null;
        }
        Object o = ArrayUtils.get(array, array.length-1);
        if(o==null||!(o instanceof SocketIoSocket.ReceivedByLocalAcknowledgementCallback)){
            return null;
        }
        return (SocketIoSocket.ReceivedByLocalAcknowledgementCallback)o;
    }

    @Override
    public void call(Object... args) {
        SocketIoSocket socket = (SocketIoSocket) args[0];
        LOGGER.info("Client " + socket.getId() + " (" + getClientIp(socket) + ") has connected.");
    }
}
