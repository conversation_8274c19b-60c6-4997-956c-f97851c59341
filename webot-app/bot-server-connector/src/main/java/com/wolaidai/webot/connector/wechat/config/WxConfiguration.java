package com.wolaidai.webot.connector.wechat.config;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.connector.wechat.handler.wx.CustomClickEventHandler;
import com.wolaidai.webot.connector.wechat.handler.wx.KfSessionMpHandler;
import com.wolaidai.webot.connector.wechat.handler.wx.LogMpHandler;
import com.wolaidai.webot.connector.wechat.handler.wx.MsgMpHandler;
import com.wolaidai.webot.data.mongodb.repo.WxAuthorizersRepo;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.redis.RedissonWxRedisOps;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedisConfigImpl;
import me.chanjar.weixin.open.api.WxOpenService;
import me.chanjar.weixin.open.api.impl.WxOpenInRedissonConfigStorage;
import me.chanjar.weixin.open.api.impl.WxOpenMessageRouter;
import me.chanjar.weixin.open.api.impl.WxOpenServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.stream.Collectors;

import static me.chanjar.weixin.common.api.WxConsts.XmlMsgType.EVENT;
import static me.chanjar.weixin.mp.constant.WxMpEventConstants.CustomerService.*;

@AllArgsConstructor
@Configuration
@EnableConfigurationProperties({WxOpenProperties.class,WxMpProperties.class})
public class WxConfiguration {
    private static final Logger LOGGER = LoggerFactory.getLogger(WxConfiguration.class);
    private final WxOpenProperties wxOpenProperties;
    private final WxMpProperties wxMpProperties;
    private final RedissonClient redissonClient;
    private final ApplicationContext applicationContext;
    private final WxAuthorizersRepo wxAuthorizersRepo;

    @Bean
    public WxOpenService wxOpenService() {
        WxOpenService service = null;
        WxOpenInRedissonConfigStorage inRedissonConfigStorage = new WxOpenInRedissonConfigStorage(redissonClient);
        if(!StringUtils.isAnyBlank(wxOpenProperties.getComponentAppId(),wxOpenProperties.getComponentSecret(),wxOpenProperties.getComponentToken(),wxOpenProperties.getComponentAesKey())) {
            service = new WxOpenServiceImpl();
            inRedissonConfigStorage.setComponentAppId(wxOpenProperties.getComponentAppId().trim());
            inRedissonConfigStorage.setComponentAppSecret(wxOpenProperties.getComponentSecret().trim());
            inRedissonConfigStorage.setComponentToken(wxOpenProperties.getComponentToken().trim());
            inRedissonConfigStorage.setComponentAesKey(wxOpenProperties.getComponentAesKey().trim());
            service.setWxOpenConfigStorage(inRedissonConfigStorage);
            wxAuthorizersRepo.findAll().forEach(v->{
                JSONObject authorizationInfo = v.getAuthorization_info();
                if(authorizationInfo!=null){
                    String authorizerAppid = authorizationInfo.getString("authorizer_appid");
                    String authorizerRefreshToken = authorizationInfo.getString("authorizer_refresh_token");
                    if(StringUtils.isNoneBlank(authorizerAppid,authorizerRefreshToken)){
                        inRedissonConfigStorage.updateAuthorizerRefreshToken(authorizerAppid,authorizerRefreshToken);
                    }
                }
            });
        }
        return service;
    }

    @Bean
    public WxMpService wxMpService() {
        WxMpService service = null;
        // 代码里 getConfigs()处报错的同学，请注意仔细阅读项目说明，你的IDE需要引入lombok插件！！！！
        List<WxMpProperties.MpConfig> configs = wxMpProperties.getConfigs();
        if(configs!=null) {
            service = new WxMpServiceImpl();
            service.setMultiConfigStorages(configs
                    .stream().map(a -> {
                        WxMpDefaultConfigImpl configStorage = new WxMpRedisConfigImpl(new RedissonWxRedisOps(redissonClient), "wechat");
                        configStorage.setAppId(a.getAppId());
                        configStorage.setSecret(a.getSecret());
                        configStorage.setToken(a.getToken());
                        configStorage.setAesKey(a.getAesKey());
                        return configStorage;
                    }).collect(Collectors.toMap(WxMpDefaultConfigImpl::getAppId, a -> a, (o, n) -> o)));
        }
        return service;
    }

    @Bean
    public WxMpMessageRouter wxMpMessageRouter(WxMpService wxMpService) {
        if(wxMpService==null){
            return null;
        }
        final WxMpMessageRouter newRouter = new WxMpMessageRouter(wxMpService);

        // 记录所有事件的日志 （异步执行）
        newRouter.rule().handler(applicationContext.getBean(LogMpHandler.class)).next();

        KfSessionMpHandler kfSessionHandler = applicationContext.getBean(KfSessionMpHandler.class);
        // 接收客服会话管理事件
        newRouter.rule().async(false).msgType(EVENT).event(KF_CREATE_SESSION)
                .handler(kfSessionHandler).end();
        newRouter.rule().async(false).msgType(EVENT).event(KF_CLOSE_SESSION)
                .handler(kfSessionHandler).end();
        newRouter.rule().async(false).msgType(EVENT).event(KF_SWITCH_SESSION)
                .handler(kfSessionHandler).end();
        newRouter.rule().async(false).event(WxConsts.EventType.CLICK).eventKey("CUSTOM").handler(applicationContext.getBean(CustomClickEventHandler.class)).end();
        // 默认
        newRouter.rule().async(false).matcher(message -> StringUtils.equalsAny(message.getMsgType(), WxConsts.XmlMsgType.TEXT, WxConsts.XmlMsgType.IMAGE, WxConsts.XmlMsgType.VIDEO, WxConsts.XmlMsgType.VOICE)).handler(applicationContext.getBean(MsgMpHandler.class)).end();

        return newRouter;
    }

    @Bean
    public WxOpenMessageRouter wxOpenMessageRouter(WxOpenService wxOpenService) {
        if(wxOpenService==null){
            return null;
        }
        final WxOpenMessageRouter newRouter = new WxOpenMessageRouter(wxOpenService);

        // 记录所有事件的日志 （异步执行）
        newRouter.rule().handler(applicationContext.getBean(LogMpHandler.class)).next();

        KfSessionMpHandler kfSessionHandler = applicationContext.getBean(KfSessionMpHandler.class);
        // 接收客服会话管理事件
        newRouter.rule().async(false).msgType(EVENT).event(KF_CREATE_SESSION)
            .handler(kfSessionHandler).end();
        newRouter.rule().async(false).msgType(EVENT).event(KF_CLOSE_SESSION)
            .handler(kfSessionHandler).end();
        newRouter.rule().async(false).msgType(EVENT).event(KF_SWITCH_SESSION)
            .handler(kfSessionHandler).end();
        newRouter.rule().async(false).event(WxConsts.EventType.CLICK).eventKey("CUSTOM").handler(applicationContext.getBean(CustomClickEventHandler.class)).end();
        // 默认
        newRouter.rule().async(false).matcher(message -> StringUtils.equalsAny(message.getMsgType(), WxConsts.XmlMsgType.TEXT, WxConsts.XmlMsgType.IMAGE, WxConsts.XmlMsgType.VIDEO, WxConsts.XmlMsgType.VOICE)).handler(applicationContext.getBean(MsgMpHandler.class)).end();

        return newRouter;
    }



}
