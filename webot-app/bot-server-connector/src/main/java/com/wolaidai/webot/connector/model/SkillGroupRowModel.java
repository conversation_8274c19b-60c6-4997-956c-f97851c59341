package com.wolaidai.webot.connector.model;

import com.wolaidai.webot.data.mysql.entity.bot.SkillGroupEntity;
import lombok.Data;

@Data
public class SkillGroupRowModel extends BaseModel {
    private Integer id;
    private String name;
    private Integer status;
    private Integer type;

    public SkillGroupRowModel(SkillGroupEntity sg) {
        this.id = sg.getId();
        this.name = sg.getName();
        this.status = sg.getStatus();
        this.type = sg.getType();
    }

}
