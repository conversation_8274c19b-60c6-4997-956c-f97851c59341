package com.wolaidai.webot.connector.service.event.chat;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.constant.SocketNamespace;
import com.wolaidai.webot.connector.entity.Event;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.model.SocketRoomModel;
import com.wolaidai.webot.connector.model.TransModel;
import com.wolaidai.webot.connector.service.SocketService;
import com.wolaidai.webot.connector.service.business.ChatHistoryService;
import com.wolaidai.webot.connector.service.business.KeyExpireService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionTransferListEntity;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.mysql.repo.SessionTransferListRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@AllArgsConstructor
public class SessionTransEventService extends BaseEventService {

    private final StringRedisTemplate csStringRedisTemplate;

    private final SessionListRepo sessionListRepo;

    private final SessionTransferListRepo sessionTransferListRepo;

    private final ChatHistoryService chatHistoryService;

    private final KeyExpireService keyExpireService;

    private final SocketService socketService;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject)model.getData();
        JSONObject manual = data.getJSONObject("manual");
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        TransModel transModel = JSON.parseObject(manual.toString(), TransModel.class);
        SessionListEntity sessionList = sessionListRepo.findBySessionKey(transModel.getSessionKey());
        if(sessionList == null) {
            if (callback != null) {
                callback.sendAcknowledgement(new JSONObject().put("ret", -1)
                        .put("msg", "会话不存在").put("timestamp", System.currentTimeMillis()));
            }
            LOGGER.warn("会话不存在,data:{}", data);
            return null;
        }
        Integer orgId = sessionList.getOrgId();
        String sessionKey = transModel.getSessionKey();
        Integer transId = transModel.getTransId();
        String result = transModel.getResult();
        String from = transModel.getFrom();
        String to = transModel.getTo();
        HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
       //倒计时
        String transTimerKey = String.format(RedisKey.CS_SESSION_TRANS_KEY, transId);
        String fromSocketId = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, from), "socketId");
        if ("success".equals(result)) {
            Optional<SessionTransferListEntity> transferOp = sessionTransferListRepo.findById(transId);
            if (callback != null && !transferOp.isPresent()) {
                callback.sendAcknowledgement(new JSONObject().put("ret", -1)
                        .put("msg", "转接记录不存在").put("timestamp", System.currentTimeMillis()));
                return null;
            }
            if (callback != null && Objects.equals(false, keyExpireService.hasExpireKey(transTimerKey))) { //服务端超时
                callback.sendAcknowledgement(new JSONObject().put("ret", -1)
                        .put("msg", "转接已超时").put("timestamp", System.currentTimeMillis()));
                return null;
            }
            //转接成功后取消置顶
            sessionListRepo.updateTransferById(sessionList.getId(), 0, from, to, sessionList.getTransferToUsers().fluentAdd(to).toString());
            //发送消息事件给客户
            models.add(new SocketPacketModel(sessionKey, model.getSocketId(), SocketEvent.MESSAGE,
                    MessageUtil.toJsonString(chatHistoryService.createChatHistory(null, sessionList.getClientId(), sessionList.getClientTypeId(),
                    "cs", "system", SocketEvent.EVENT, SocketEvent.SESSION_TRANS_TYPE, null,
                            orgId, sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(), null,
                    "客服".concat(transModel.getToName()).concat("正在为您服务"), null,  null, null, null,
                    new com.alibaba.fastjson.JSONObject().fluentPut("sessionKey", sessionKey).fluentPut("email", to).fluentPut("nickName", transModel.getToName()), true))));
            //加入目标客服聊天窗口
            socketService.joinOrLeaveGlobalRoom(SocketNamespace.CHAT, SocketRoomModel.ADD_TYPE, Collections.singletonList(sessionKey), model.getSocketId());
            //退出原来客服的聊天窗口
            if(StringUtils.isNotBlank(fromSocketId)) {
                socketService.joinOrLeaveGlobalRoom(SocketNamespace.CHAT, SocketRoomModel.REMOVE_TYPE, Collections.singletonList(sessionKey), fromSocketId);
            }
            csStringRedisTemplate.opsForList().rightPush(RedisKey.EVENT_CLIENT_KEY, JSON.toJSONString(new Event(UUID.randomUUID().toString(), Event.TRANS_SUCCESS_KEY, orgId,
                    JSON.parseObject(new JSONObject().put("transId", transId).toString()), new Date())));
        } else if ("reject".equals(result)) {
            csStringRedisTemplate.opsForList().rightPush(RedisKey.EVENT_CLIENT_KEY, JSON.toJSONString(new Event(UUID.randomUUID().toString(), Event.TRANS_REJECT_KEY, orgId,
                    JSON.parseObject(new JSONObject().put("transId", transId).toString()), new Date())));
        } else if ("timeout".equals(result)) {
            csStringRedisTemplate.opsForList().rightPush(RedisKey.EVENT_CLIENT_KEY, JSON.toJSONString(new Event(UUID.randomUUID().toString(), Event.TRANS_TIMEOUT_KEY, orgId,
                    JSON.parseObject(new JSONObject().put("transId", transId).toString()), new Date())));
        } else {
            LOGGER.warn("未知转接结果,result:{},data:{}", result, JSON.toJSONString(data));
            return null;
        }
        //删除计时key
        keyExpireService.removeExpireKey(transTimerKey);
        //通知发起人
        if (StringUtils.isNotBlank(fromSocketId)) {
            models.add(new SocketPacketModel(null, fromSocketId, SocketEvent.EVENT,
                            new com.alibaba.fastjson.JSONObject().fluentPut("type", SocketEvent.SESSION_TRANS_RESULT_TYPE).fluentPut("manual", manual).toJSONString()));
        }
        if (callback != null) {
            callback.sendAcknowledgement(new JSONObject().put("ret", 0).put("timestamp", System.currentTimeMillis()));
        }
        return models;
    }
}
