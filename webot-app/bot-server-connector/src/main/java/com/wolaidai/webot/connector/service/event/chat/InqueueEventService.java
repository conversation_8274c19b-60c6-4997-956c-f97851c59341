package com.wolaidai.webot.connector.service.event.chat;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.constant.ManualConfigConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.entity.Event;
import com.wolaidai.webot.connector.model.ConversationContextModel;
import com.wolaidai.webot.connector.model.InqueueModel;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.business.ChatHistoryService;
import com.wolaidai.webot.connector.service.business.ConversationService;
import com.wolaidai.webot.connector.service.business.ManualChatService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.connector.service.worktime.CsWorkTimeService;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import com.wolaidai.webot.data.mysql.repo.BlackListRepo;
import com.wolaidai.webot.data.mysql.repo.QueueListRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@AllArgsConstructor
public class InqueueEventService extends BaseEventService {

    private final StringRedisTemplate csStringRedisTemplate;

    private final ConversationService conversationService;

    private final ChatHistoryService chatHistoryService;

    private final ManualChatService manualChatService;

    private final CsWorkTimeService csWorkTimeService;

    private final SessionListRepo sessionListRepo;

    private final QueueListRepo queueListRepo;
    private final BlackListRepo blackListRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject)model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        String socketId = model.getSocketId();
        InqueueModel inqueueModel = JSON.parseObject(data.toString(), InqueueModel.class);
        String clientId = inqueueModel.getClientId();
        String accessKey = inqueueModel.getAccessKey();
        String businessId = inqueueModel.getBusinessId();
        if(StringUtils.isNotBlank(clientId)&&StringUtils.isNotBlank(accessKey)&&StringUtils.isNotBlank(businessId)) {
            ConversationContextModel conversation = conversationService.getConversation(socketId, clientId, inqueueModel.getClientType(), inqueueModel.getAccount(), accessKey, inqueueModel.getBusinessId(), true);
            if(conversation==null){
                return null;
            }
            ConversationContextModel.ContextInfo contextInfo = conversation.getInfo();
            Integer blacklistId = blackListRepo.findByOrgIdAndPhoneAndClientId(contextInfo.getOrgId(), inqueueModel.getAccount(), inqueueModel.getClientId());
            if(blacklistId!=null){
                JSONObject blackListConfig = manualChatService.getAutoReplyConfig(contextInfo.getOrgId(), contextInfo.getClientType(), ManualConfigConstants.AUTO_REPLY_BLACKLIST_MSG);
                if (blackListConfig != null && blackListConfig.getBoolean("status")){
                    if(callback!=null) {
                        callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", blackListConfig.getString("content")).put("timestamp", System.currentTimeMillis()));
                        return null;
                    }
                }
            }
            if(contextInfo.getCid()!=null){
                conversationService.updateOpFlagByCid(contextInfo.getCid());
            }

            //该clientId存在在线会话
            String onlineSessionClientId = sessionListRepo.findClientIdByOnlineStatusAndCreateTime(contextInfo.getOrgId(), clientId, DateUtils.truncate(new Date(), Calendar.DATE));
            if(onlineSessionClientId!=null){
                LOGGER.error("已在会话中，clientId:{}, orgId:{}", clientId, contextInfo.getOrgId());
                if(callback!=null){
                    callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", "已在会话中").put("timestamp", System.currentTimeMillis()));
                    return null;
                }
            }
            //该clientId是否已在排队中
            QueueListEntity queueList = queueListRepo.findIdByInQueueStatusAndCreateTime(contextInfo.getOrgId(), clientId, DateUtils.truncate(new Date(), Calendar.DATE));
            if(queueList!=null){
                LOGGER.error("已在排队中，clientId:{}, orgId:{}", clientId, contextInfo.getOrgId());
                manualChatService.notifyQueueStatus(queueList.getOrgId(), queueList.getId(), queueList.getCustomerType(), false);
                return null;
            }

            boolean isWorkTime = csWorkTimeService.isWorkTime(contextInfo.getOrgId());
            boolean hasOnline = manualChatService.onlineCount(contextInfo.getOrgId())>0;
            if(!isWorkTime||!hasOnline){
                JSONObject serviceOfflineMsgJson = manualChatService.getAutoReplyConfig(contextInfo.getOrgId(), contextInfo.getClientType(),ManualConfigConstants.AUTO_REPLY_SERVICE_OFFLINE_MSG);
                if(serviceOfflineMsgJson!=null){
                    if(serviceOfflineMsgJson.getBoolean("status")) {
                        String content = serviceOfflineMsgJson.getString("content");
                        ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(clientId, contextInfo.getClientType(), "cs", "system", "event", "service:offline",
                                contextInfo.getAccount(), contextInfo.getOrgId(), contextInfo.getBotId(), contextInfo.getSkillGroupId(), contextInfo.getGcid(), content, false);
                        models.add(new SocketPacketModel(null, socketId, SocketEvent.MESSAGE, JSON.toJSONString(chatHistory)));
                        if(callback!=null) {
                            callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", !hasOnline ? "客服不在线" : "非工作时间").put("timestamp", System.currentTimeMillis()));
                        }
                    }
                }
            } else {
                boolean queueExceed = false;
                JSONObject queueExceedMsgJson = manualChatService.getAutoReplyConfig(contextInfo.getOrgId(), contextInfo.getClientType(),ManualConfigConstants.AUTO_REPLY_QUEUE_EXCEED_MSG);
                if(queueExceedMsgJson!=null&&queueExceedMsgJson.getBoolean("status")){
                    long queueCount = manualChatService.queueCount(contextInfo.getOrgId());
                    if(queueCount>=queueExceedMsgJson.getInt("exceed")){
                        queueExceed = true;
                        if(callback!=null) {
                            callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", queueExceedMsgJson.getString("content")).put("timestamp", System.currentTimeMillis()));
                        }
                    }
                }
                if(!queueExceed) {
                    JSONObject customerDetail = new JSONObject();
                    customerDetail.put("ua", model.getUa());
                    customerDetail.put("ip",model.getIp());
                    csStringRedisTemplate.opsForList().rightPush(RedisKey.EVENT_CLIENT_KEY, JSON.toJSONString(new Event(UUID.randomUUID().toString(), Event.INQUEUE_KEY, contextInfo.getOrgId(),
                            JSON.parseObject(new JSONObject().put("clientId", clientId).put("clientType", inqueueModel.getClientType()).put("botId", contextInfo.getBotId()).put("gcid", contextInfo.getGcid()).put("gcTime", contextInfo.getGcTime().getTime())
                                    .put("businessId", inqueueModel.getBusinessId()).put("businessName", inqueueModel.getBusinessName()).put("customerDetail", customerDetail).put("account", contextInfo.getAccount()).toString()), new Date())));
                    if (callback != null) {
                        callback.sendAcknowledgement(new JSONObject().put("ret", 0).put("msg", "请求排队成功").put("timestamp", System.currentTimeMillis()));
                    }
                }
            }
        }
        return models;
    }
}
