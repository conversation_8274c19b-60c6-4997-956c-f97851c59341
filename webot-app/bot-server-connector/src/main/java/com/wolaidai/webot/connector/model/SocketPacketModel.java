package com.wolaidai.webot.connector.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class SocketPacketModel<T extends Serializable> extends BaseModel{
    private String room;
    private String socketId;
    private String event;
    private T data;
    private String callbackId;

    public SocketPacketModel(String room, String socketId, String event, T data) {
        this.room = room;
        this.socketId = socketId;
        this.event = event;
        this.data = data;
    }

    public SocketPacketModel(String room, String socketId, String event, T data, String callbackId) {
        this.room = room;
        this.socketId = socketId;
        this.event = event;
        this.data = data;
        this.callbackId = callbackId;
    }
}
