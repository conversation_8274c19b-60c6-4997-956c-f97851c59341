package com.wolaidai.webot.connector.config;

import com.wolaidai.webot.common.util.OssFileClient;
import com.wolaidai.webot.data.mongodb.config.MongodbConfig;
import com.wolaidai.webot.data.mysql.config.MysqlConfig;
import com.wolaidai.webot.data.redis.config.RedisConfig;
import com.wolaidai.webot.data.redis.model.ConnectionInfo;
import com.wolaidai.webot.data.redis.utils.RedisUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import java.util.concurrent.ThreadPoolExecutor;
@AllArgsConstructor
@Configuration
@EnableAsync
@EnableScheduling
@ComponentScan
@Import({MysqlConfig.class, MongodbConfig.class, RedisConfig.class})
@EnableConfigurationProperties({AppPropertyConfig.class})
public class AppConfig {
    private final AppPropertyConfig appPropertyConfig;
    private final RedisProperties redisProperties;
    
    @Autowired
    public void init() {
        if (StringUtils.isBlank(appPropertyConfig.getBaseFileDir())) {
            OssFileClient.init(appPropertyConfig.getOssEndpoint(), appPropertyConfig.getOssAccessKeyID(), appPropertyConfig.getOssAccessKeySecret());
        }
    }

    @Bean
    public LocalValidatorFactoryBean validator(MessageSource messageSource) {
        LocalValidatorFactoryBean validatorFactoryBean = new LocalValidatorFactoryBean();
        validatorFactoryBean.setValidationMessageSource(messageSource);
        return validatorFactoryBean;
    }

    @Bean
    public CommonsMultipartResolver multipartResolver() {
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();
        multipartResolver.setMaxUploadSize(-1);
        return multipartResolver;
    }

    @Bean(destroyMethod="shutdown")
    public RedissonClient redisson(){
        Config config = new Config();
        ConnectionInfo connectionInfo = RedisUtils.parseUrl(redisProperties.getUrl());
        SingleServerConfig serverConfig = config.useSingleServer();
        serverConfig.setAddress(redisProperties.getUrl());
        serverConfig.setDatabase(1);
        if(StringUtils.isNotBlank(connectionInfo.getUsername())) {
            serverConfig.setUsername(connectionInfo.getUsername());
        }
        if(StringUtils.isNotBlank(connectionInfo.getPassword())) {
            serverConfig.setPassword(connectionInfo.getPassword());
        }
        return Redisson.create(config);
    }

    @Bean("threadPool")
    public ThreadPoolTaskExecutor threadPool(){
        ThreadPoolTaskExecutor callThreadPool = new ThreadPoolTaskExecutor();
        callThreadPool.setCorePoolSize(appPropertyConfig.getTaskCorePool());
        callThreadPool.setMaxPoolSize(appPropertyConfig.getTaskMaxPool());
        callThreadPool.setQueueCapacity(appPropertyConfig.getTaskQueueCapacity());
        callThreadPool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        callThreadPool.setWaitForTasksToCompleteOnShutdown(true);
        callThreadPool.setAwaitTerminationSeconds(10);
        callThreadPool.initialize();
        return callThreadPool;
    }
}
