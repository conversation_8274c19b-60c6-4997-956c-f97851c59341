package com.wolaidai.webot.connector.service.worktime;

import java.util.Date;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.connector.service.BaseService;
import com.wolaidai.webot.connector.service.business.ManualChatService;

import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class CsWorkTimeService extends BaseService {

    private final ManualChatService manualChatService;

    public boolean isWorkTime(int orgId){
        Date now = new Date();
        JSONObject configJson = manualChatService.getCsWorkTimeConfig(orgId);
        if(null != configJson){
            String dateFormat = "yyyy/MM/dd HH:mm";
            JSONArray specificArray = configJson.optJSONArray("specific");
            if (null != specificArray && specificArray.length() > 0) {
                for (int i = 0; i < specificArray.length(); i++) {
                    JSONObject specificJson = specificArray.optJSONObject(i);
                    Date startTime = DateUtil.parseDate(specificJson.getString("startTime"),dateFormat);
                    Date endTime = DateUtil.parseDate(specificJson.getString("endTime"),dateFormat);
                    if(startTime!=null&&endTime!=null&&now.getTime()>=startTime.getTime()&&now.getTime()<endTime.getTime()){
                        return true;
                    }
                }
            }
            JSONArray holidaysArray = configJson.optJSONArray("holidays");
            if (null != holidaysArray && holidaysArray.length() > 0) {
                for (int i = 0; i < holidaysArray.length(); i++) {
                    JSONObject holidayJson = holidaysArray.optJSONObject(i);
                    Date startTime = DateUtil.parseDate(holidayJson.getString("startTime"),dateFormat);
                    Date endTime = DateUtil.parseDate(holidayJson.getString("endTime"),dateFormat);
                    if(startTime!=null&&endTime!=null&&now.getTime()>=startTime.getTime()&&now.getTime()<endTime.getTime()){
                        return false;
                    }
                }
            }
            JSONArray workdaysArray = configJson.optJSONArray("workdays");
            if (null != workdaysArray && workdaysArray.length() > 0) {
                for (int i = 0; i < workdaysArray.length(); i++) {
                    JSONObject workdaysJson = workdaysArray.optJSONObject(i);
                    JSONArray days = workdaysJson.optJSONArray("days");
                    for (int j = 0; j < days.length(); j++) {
                        int day = days.optInt(j);
                        day = (day==7?0:day);
                        if(day==now.getDay()){
                            String startTime = workdaysJson.getString("startTime");
                            String endTime = workdaysJson.getString("endTime");
                            if(startTime!=null&&endTime!=null){
                                String time = DateFormatUtils.format(now, "HH:mm");
                                if(time.compareTo(startTime)>=0&&time.compareTo(endTime)<0){
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }
}
