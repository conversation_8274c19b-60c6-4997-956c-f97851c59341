package com.wolaidai.webot.connector.service.event.chat.inner;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.constant.ManualConfigConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.constant.SocketNamespace;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.model.SocketRoomModel;
import com.wolaidai.webot.connector.model.inner.SessionJoinModel;
import com.wolaidai.webot.connector.service.SocketService;
import com.wolaidai.webot.connector.service.business.ChatRecordService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.data.mysql.entity.chat.*;
import com.wolaidai.webot.data.mysql.repo.ChatMembersSnapshotRepo;
import com.wolaidai.webot.data.mysql.repo.ChatRoomRepo;
import com.wolaidai.webot.data.mysql.repo.ChatSessionRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Transactional
public class SessionJoinEventService extends BaseEventService {

    private final StringRedisTemplate csStringRedisTemplate;
    private final ChatRoomRepo chatRoomRepo;
    private final UserStateRepo userStateRepo;
    private final SocketService socketService;
    private final ChatSessionRepo chatSessionRepo;
    private final ChatRecordService chatRecordService;
    private final ChatMembersSnapshotRepo chatMembersSnapshotRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject)model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        SessionJoinModel sessionModel = JSON.parseObject(data.toString(), SessionJoinModel.class);
        if (ManualConfigConstants.WORKBENCH_TYPE.equals(sessionModel.getSource())) {
            Integer sessionId = sessionModel.getSessionKey();
            String from = sessionModel.getEmail();
            ChatSessionEntity sessionEntity = chatSessionRepo.findById(sessionId).orElse(null);
            if (sessionEntity != null) {
                Integer orgId = sessionEntity.getOrgId();
                HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                ChatRoomEntity chatRoomEntity = sessionEntity.getRoom();
                UserStateEntity userFrom = null;
                Date dateNow = new Date();
                List<String> toEmails = sessionModel.getToEmails();
                if (chatRoomEntity == null) { //单聊变群聊
                    //原有双方加入聊天室
                    Set<ChatMembersEntity> originalMembers = sessionEntity.getMembers();
                    List<UserStateEntity> originalUsers = originalMembers.stream().map(ChatMembersEntity::getUser).collect(Collectors.toList());
                    chatRoomEntity = new ChatRoomEntity();
                    chatRoomEntity.setOrgId(orgId);
                    //新创建一个会话
                    sessionEntity = new ChatSessionEntity();
                    sessionEntity.setOrgId(orgId);
                    sessionEntity.setRoom(chatRoomEntity);
                    for (UserStateEntity user : originalUsers) {
                        //将单聊的另一方加入被邀集合中,后续保存roomUsers,chatMembers,发送邀请消息
                        if (!from.equals(user.getEmail())) {
                            toEmails.add(user.getEmail());
                            continue;
                        }
                        userFrom = user;
                        chatRoomEntity.getRoomUsers().add(new ChatRoomUsersEntity(chatRoomEntity, user));
                        sessionEntity.getMembers().add(new ChatMembersEntity(ChatMembersEntity.STATUS_ACTIVE, sessionEntity, user));
                    }
                    chatRoomEntity.setCreator(from);
                    chatRoomEntity.setCreateTime(dateNow);
                    chatRoomRepo.save(chatRoomEntity);
                    //保存新会话
                    sessionEntity.setLastMsgTime(dateNow);
                    sessionEntity.setCreator(from); //聊天发起人
                    sessionEntity.setCreateTime(dateNow);
                    chatSessionRepo.save(sessionEntity);
                    sessionId = sessionEntity.getId();
                    socketService.joinOrLeaveGlobalRoom(SocketNamespace.CHAT, SocketRoomModel.ADD_TYPE, Collections.singletonList("session_" + sessionId), model.getSocketId());
                }
                List<String> socketIds = new ArrayList<>();
                for (String email : toEmails) {
                    UserStateEntity userState = userStateRepo.findByOrgIdAndEmail(orgId, email);
                    if (userState == null) {
                        if (callback != null) {
                            callback.sendAcknowledgement(new JSONObject().put("ret", -1)
                                    .put("msg", "该用户未激活账号").put("timestamp", System.currentTimeMillis()));
                        }
                        return null;
                    }
                    String toSocketId = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, email), "socketId");
                    if (StringUtils.isNotBlank(toSocketId)) { //加入聊天室
                        socketIds.add(toSocketId);
                    }
                    chatRoomEntity.getRoomUsers().add(new ChatRoomUsersEntity(chatRoomEntity, userState));
                    sessionEntity.getMembers().add(new ChatMembersEntity(ChatMembersEntity.STATUS_ACTIVE, sessionEntity, userState));
                }
                String nickName, name;
                if (userFrom == null) {
                    String onlineEmailKey = String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, sessionModel.getEmail());
                    nickName = hashOperations.get(onlineEmailKey, "nickName");
                    name = hashOperations.get(onlineEmailKey, "name");
                } else {
                    nickName = userFrom.getNickName();
                    name = userFrom.getName();
                }
                List<String> nameList = userStateRepo.findByOrgIdAndEmailIn(orgId, toEmails);
                String eventMsg = String.format("%s %s邀请%s加入了群聊", ObjectUtils.defaultIfNull(nickName, ""), ObjectUtils.defaultIfNull(name, ""), String.join("、", nameList));
                //保存参与会话快照
                ChatMembersSnapshotEntity snapshotEntity = new ChatMembersSnapshotEntity(sessionId,
                        chatRoomEntity.getRoomUsers().stream().map(ChatRoomUsersEntity::getUser).map(UserStateEntity::getEmail).collect(Collectors.toList()));
                chatMembersSnapshotRepo.save(snapshotEntity);
                ChatRecordEntity chatRecord = chatRecordService.createChatRecord(null, from, null, eventMsg,
                        ChatRecordEntity.EVENT_TYPE, sessionEntity, snapshotEntity, null, new com.alibaba.fastjson.JSONObject().fluentPut("sessionKey", sessionEntity.getId()));
                socketService.joinOrLeaveGlobalRoom(SocketNamespace.CHAT, SocketRoomModel.ADD_TYPE, Collections.singletonList("session_" + sessionId), socketIds.toArray(new String[]{}));
                models.add(new SocketPacketModel("session_" + sessionModel.getSessionKey(), null, SocketEvent.INFO, MessageUtil.toJsonString(chatRecord)));
                //更新会话、聊天室信息
                chatRoomEntity.setUpdateTime(dateNow);
                chatRoomRepo.save(chatRoomEntity);
                sessionEntity.setLastMsg(eventMsg);
                sessionEntity.setLastMsgTime(dateNow);
                sessionEntity.setLastMsgSender(from);
                sessionEntity.setUpdateTime(dateNow);
                chatSessionRepo.save(sessionEntity);
               if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret", 0).put("sessionKey", sessionId).put("timestamp", System.currentTimeMillis()));
                }
            }
        }
        return models;
    }
}
