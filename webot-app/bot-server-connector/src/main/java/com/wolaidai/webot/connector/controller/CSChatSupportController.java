package com.wolaidai.webot.connector.controller;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.common.util.StringUtil;
import com.wolaidai.webot.connector.service.business.ChatHistoryService;
import com.wolaidai.webot.connector.service.business.SessionListService;
import com.wolaidai.webot.connector.utils.CommonUtil;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.data.mysql.entity.chat.UserVerification;
import com.wolaidai.webot.data.mysql.entity.chat.UserVerificationHistory;
import com.wolaidai.webot.data.mysql.repo.UserVerificationHistoryRepo;
import com.wolaidai.webot.data.mysql.repo.UserVerificationRepo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wolaidai.webot.connector.constant.RedisConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.constant.SocketNamespace;
import com.wolaidai.webot.connector.constant.WebStatusConstants;
import com.wolaidai.webot.connector.model.ChatHistoryRowModel;
import com.wolaidai.webot.connector.model.GetKfHistoriesModel;
import com.wolaidai.webot.connector.model.ResponseModel;
import com.wolaidai.webot.connector.model.SatisfactionModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.business.ManualChatService;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.repo.ChatHistoryRepo;
import com.wolaidai.webot.data.mysql.entity.chat.SatisfactionDataEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.repo.SatisfactionDataRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;

@AllArgsConstructor
@RestController
@Api(tags = "人工客户聊天窗口支持相关")
public class CSChatSupportController extends BaseController {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final SessionListRepo sessionListRepo;
    private final ChatHistoryRepo chatHistoryRepo;
    private final SatisfactionDataRepo satisfactionDataRepo;
    private final UserVerificationRepo userVerificationRepo;
    private final UserVerificationHistoryRepo userVerificationHistoryRepo;
    private final ManualChatService manualChatService;
    private final SessionListService sessionListService;
    private final ChatHistoryService chatHistoryService;
    private final StringRedisTemplate csStringRedisTemplate;
    private final RedisTemplate<String, Object> csRedisTemplate;



    @GetMapping("/history/{sessionKey}")
    @ApiOperation(value = "获取人工聊天记录")
    public ResponseModel getHistories(@PathVariable String sessionKey, GetKfHistoriesModel model) {
        SessionListEntity session = null;
        if (StringUtils.isBlank(sessionKey) || null == (session = sessionListRepo.findBySessionKey(sessionKey))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话不存在");
        }
        Date date = null != model.getDate() ? new Date(model.getDate()) : new Date();
        int pageSize = null != model.getPageSize() ? Math.min(100, model.getPageSize()) : 30;
        List<ChatHistoryEntity> list = null;
        if (null != session.getOfflineTime() && date.after(session.getOfflineTime())) {
            list = chatHistoryRepo.findByClientIdAndDateLessThanEqualOrderByDateDesc(session.getClientId(), sessionKey, date, session.getOfflineTime(), PageRequest.ofSize(pageSize));
        } else {
            list = chatHistoryRepo.findByClientIdOrderByDateDesc(session.getClientId(), date, PageRequest.ofSize(pageSize));
        }
        List<ChatHistoryRowModel> result = new ArrayList<>();
        for (ChatHistoryEntity c : list) {
            ChatHistoryRowModel row = new ChatHistoryRowModel(c);
            if ("event".equals(c.getType()) && "SWITCH_SKILLGROUP".equals(c.getContent())) {
                row.setEventKey("biz:switch");
                row.setContent(null != row.getExtend() ? (String) row.getExtend().get("skillGroupName") : "切换业务");
            }
            //处理用户敏感信息
            processUserSensitiveInfo(row);
            result.add(row);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", result);
    }

    private void processUserSensitiveInfo(ChatHistoryRowModel rowModel) {
        if("text".equals(rowModel.getType()) && "user".equals(rowModel.getSender())) {
            String content = rowModel.getContent();
            Date todayStart = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            Boolean hisFlag = false;
            if(rowModel.getDate().before(todayStart)) {
                hisFlag = true;
            }
            content = CommonUtil.processUserSensitiveInfo(content, hisFlag);
            rowModel.setContent(content);
        }
    }

    @GetMapping("/satisfaction/{sessionKey}/config")
    @ApiOperation(value = "获取满意度评价配置")
    public ResponseModel getSatisfactionConfig(@PathVariable String sessionKey) {
        SessionListEntity sessionList = null;
        if (StringUtils.isBlank(sessionKey) || null == (sessionList = sessionListRepo.findBySessionKey(sessionKey))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话不存在");
        }
        SatisfactionDataEntity satisfactionData = sessionList.getSatisfactionData();
        if (null != satisfactionData) {
            if (satisfactionData.getStatus() == SatisfactionDataEntity.STATUS_TREATED) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "您已对当前服务进行过评价");
            }
            if (DateUtils.addMinutes(satisfactionData.getCreateTime(), 30).before(new Date())) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "已超过评价时间");
            }
        }
        JSONObject config = manualChatService.getSatisfactionConfig(sessionList.getOrgId());
        if (null == config) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "满意度评价配置不存在");
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", JSON.parse(config.toString()));
    }

    @PostMapping("/satisfaction")
    @ApiOperation(value = "提交满意度评价")
    public ResponseModel commitSatisfaction(SatisfactionModel model) throws Exception {
        if (null == model.getLevel() || model.getLevel() < 1 || model.getLevel() > 5) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "评价等级不合法");
        }
        SessionListEntity sessionList = null;
        if (StringUtils.isBlank(model.getSessionKey()) || null == (sessionList = sessionListRepo.findBySessionKey(model.getSessionKey()))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话不存在");
        }
        Date now = new Date();
        SatisfactionDataEntity satisfactionData = sessionList.getSatisfactionData();
        if (null != satisfactionData) {
            if (satisfactionData.getStatus() == SatisfactionDataEntity.STATUS_TREATED) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "您已对当前服务进行过评价");
            }
            if (DateUtils.addMinutes(satisfactionData.getCreateTime(), 30).before(now)) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "已超过评价时间");
            }
        } else {
            satisfactionData = new SatisfactionDataEntity();
            satisfactionData.setClientId(sessionList.getClientId());
            satisfactionData.setClientTypeId(sessionList.getClientTypeId());
            satisfactionData.setOrgId(sessionList.getOrgId());
            satisfactionData.setServiceUser(sessionList.getLastServiceUser());
            satisfactionData.setOrigin(sessionList.getOrigin());
            satisfactionData.setSession(sessionList);
            satisfactionData.setCreateTime(now);
        }
        satisfactionData.setContent(model.getContent());
        if (null != model.getLabels() && model.getLabels().size() > 0) {
            satisfactionData.setLabels((JSONArray) JSON.toJSON(model.getLabels()));
        }
        satisfactionData.setLevel(model.getLevel());
        satisfactionData.setStatus(SatisfactionDataEntity.STATUS_TREATED);
        satisfactionData.setUpdateTime(now);
        satisfactionDataRepo.save(satisfactionData);
        sessionList.setSatisfactionData(satisfactionData);
        sessionListRepo.updateSatisfactionLevelById(sessionList.getId(), model.getLevel());
        HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
        String socketId = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, sessionList.getOrgId(), sessionList.getLastServiceUser()), "socketId");
        if (StringUtils.isNotBlank(socketId)) {
            JSONObject manual = new JSONObject().put("type", SocketEvent.SATISFACTION_COMMIT_TYPE).put("content", "客户已评价").put("manual", new JSONObject().put("sessionKey", model.getSessionKey()).put("appraiseLevel", satisfactionData.getLevel()).put("appraiseStatus", satisfactionData.getStatus()));
            csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), Collections.singletonList(new SocketPacketModel<String>(null, socketId, SocketEvent.EVENT, manual.toString())));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "评价成功");
    }


    @GetMapping("/user/verify/link/valid")
    @ApiOperation(value = "身份验证链接是否有效")
    public ResponseModel isVerifyLinkValid(@RequestParam String token) {
        if(StringUtils.isEmpty(token)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "token不能为空");
        }

        UserVerification userVerification = userVerificationRepo.findByToken(token);
        String info = validateVerifyLink(userVerification);
        if(StringUtils.isNotEmpty(info)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, info);
        }

        HashMap<String, Object> data = new HashMap<>();
        data.put("valid", 1);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "有效链接", data);
    }

    private String validateVerifyLink(UserVerification userVerification) {
        if(userVerification == null) {
            return "无效链接";
        }

        SessionListEntity sessionList = userVerification.getSession();
        if(sessionList == null) {
            return "会话不存在";
        }
        if(sessionList.getStatus() == SessionListEntity.STATUS_OFFLINE) {
            return "会话已结束";
        }

        Integer status = userVerification.getStatus();
        if(status == UserVerification.STATUS_NON_REGISTERED || status == UserVerification.STATUS_VERIFIED ||
                (status == UserVerification.STATUS_VERIFY_FAILED && userVerification.getFailureTimes() >= 3)) {
            return "链接已失效";
        }
        return null;
    }


    @PostMapping("/user/verify")
    @ApiOperation(value = "提交身份验证")
    public ResponseModel commitVerify(@RequestBody HashMap<String, Object> reqInfo) {
        //1-注册用户  2-非注册用户
        String errorMsg = validateVerifyReq(reqInfo);
        if(StringUtils.isNotEmpty(errorMsg)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, errorMsg);
        }
        Integer type = (Integer) reqInfo.get("type");
        String token = (String) reqInfo.get("token");
        UserVerification userVerification = userVerificationRepo.findByToken(token);
        errorMsg = validateVerifyLink(userVerification);
        if(StringUtils.isNotEmpty(errorMsg)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, errorMsg);
        }

        SessionListEntity sessionList = userVerification.getSession();
        String notifyMsg = null;
        if(type == 1) {
            notifyMsg = processRegisteredUser(userVerification, reqInfo, sessionList);
        } else if(type == 2){
            notifyMsg = "身份验证-非注册用户";
            userVerification.setStatus(UserVerification.STATUS_NON_REGISTERED);
            userVerification.setUpdateTime(LocalDateTime.now());
            userVerificationRepo.save(userVerification);
        }

        generateVerifyHistoryAndSendNotify(userVerification, sessionList, token, notifyMsg);

        HashMap<String, Object> data = new HashMap();
        Integer pass = 1;
        if(userVerification.getStatus() == UserVerification.STATUS_VERIFY_FAILED) {
            pass = 0;
            data.put("failTimes", userVerification.getFailureTimes());
        }
        data.put("pass", pass);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "验证完成", data);
    }



    private String validateVerifyReq(HashMap<String, Object> reqInfo) {
        //1-注册用户  2-非注册用户
        Integer type = (Integer)reqInfo.getOrDefault("type", 0);
        String token = (String)reqInfo.getOrDefault("token", "");
        if(!Arrays.asList(1, 2).contains(type)) {
            return "type不合法";
        }
        if(StringUtils.isEmpty(token)) {
            return "token不能为空";
        }
        if(type == 1) {
            String mobile = reqInfo.getOrDefault("mobile", "").toString();
            String idLast4 = reqInfo.getOrDefault("idLast4", "").toString();
            String name = reqInfo.getOrDefault("name", "").toString();
            if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(idLast4) || StringUtils.isEmpty(name)) {
                return "注册用户请提供手机号码、身份证后四位、姓名";
            }
            if (!StringUtil.isValidPhone(mobile)) {
                return "手机号码格式错误";
            }
        }
        return null;
    }

    private String processRegisteredUser(UserVerification userVerification, HashMap<String, Object> reqInfo, SessionListEntity sessionList) {
        String mobile = reqInfo.getOrDefault("mobile", "").toString();
        String idLast4 = reqInfo.getOrDefault("idLast4", "").toString();
        String name = reqInfo.getOrDefault("name", "").toString();
        String notifyMsg = "";

        LocalDateTime now = LocalDateTime.now();
        UserVerificationHistory history = new UserVerificationHistory();
        history.setMobile(mobile);
        history.setIdLast4(idLast4);
        history.setName(name);
        history.setUserVerification(userVerification);
        history.setStatus(UserVerificationHistory.STATUS_NOT_VERIFIED);
        history.setCreateTime(now);
        history.setUpdateTime(now);
        userVerificationHistoryRepo.save(history);

        //调用接口根据手机号获取用户信息
        String errorMsg = null;
        JSONObject mobileReq = new JSONObject().put("mobile", mobile);
        com.alibaba.fastjson.JSONObject customerInfo = null;
        try {
            String response = HttpClientUtil.post(appPropertyConfig.getCustomerInfoUrl(), mobileReq.toString());
            customerInfo = JSON.parseObject(response);
        } catch (Exception e) {
            LOGGER.error("身份验证，根据手机号获取用户信息发生错误, req:{}", reqInfo, e);
        }

        if(customerInfo == null || !customerInfo.containsKey("code") || customerInfo.getInteger("code") != 0) {
            LOGGER.info("身份验证，获取用户信息失败, req:{}", reqInfo);
            errorMsg = "获取用户信息失败";
        } else {
            JSONArray result = customerInfo.getJSONArray("result");
            if(result == null || result.size() == 0) {
                errorMsg = "用户不存在";
            } else {
                com.alibaba.fastjson.JSONObject customer = result.getJSONObject(0);
                String mobileFromApi = customer.getString("mobile");
                String nameFromApi = customer.getString("name");
                String idFromApi = customer.getString("cnid");
                String idLast4FromApi = StringUtils.isEmpty(idFromApi) ? "" : idFromApi.substring(idFromApi.length() - 4);
                if(!mobile.equals(mobileFromApi) || !idLast4.equals(idLast4FromApi) || !name.equals(nameFromApi)) {
                    errorMsg = "身份信息不匹配";
                    LOGGER.info("身份验证，信息不匹配，mobile:{}, name:{}, idLast4:{}, mobileFromApi:{}, nameFromApi:{}, idLast4FromApi:{}", mobile, name, idLast4, mobileFromApi, nameFromApi, idLast4FromApi);
                } else {
                    notifyMsg = "身份验证成功";
                    history.setStatus(UserVerificationHistory.STATUS_VERIFIED);
                    history.setUpdateTime(LocalDateTime.now());
                    userVerificationHistoryRepo.save(history);
                    userVerification.setStatus(UserVerification.STATUS_VERIFIED);
                    userVerification.setUpdateTime(LocalDateTime.now());
                    userVerificationRepo.save(userVerification);

                    //更新session_list中的数据
                    String customerName = customer.getString("name");
                    if(StringUtils.isNotEmpty(customerName)) {
                        sessionList.setCustomerName(customerName);
                    }
                    int customerType = "VIP".equals(customer.getString("isVip")) ? 1 : 0;
                    sessionListService.encryptCustomerInfo(result);
                    sessionList.setCustomerType(customerType);
                    sessionList.getCustomerDetail().put("customers", result);
                    sessionList.setUpdateTime(new Date());
                    sessionListRepo.save(sessionList);
                }
            }
        }

        if(!StringUtils.isEmpty(errorMsg)) {
            notifyMsg = "身份验证失败-" + errorMsg;
            history.setStatus(UserVerificationHistory.STATUS_VERIFY_FAILED);
            history.setMsg(errorMsg);
            history.setUpdateTime(LocalDateTime.now());
            userVerificationHistoryRepo.save(history);
            userVerification.setStatus(UserVerification.STATUS_VERIFY_FAILED);
            userVerification.setFailureTimes(userVerification.getFailureTimes() + 1);
            userVerification.setUpdateTime(LocalDateTime.now());
            userVerificationRepo.save(userVerification);
        }
        return notifyMsg;
    }


    private void generateVerifyHistoryAndSendNotify(UserVerification userVerification, SessionListEntity sessionList, String token, String notifyMsg) {
        //生成chat_history数据
        com.alibaba.fastjson.JSONObject manualInfo = new com.alibaba.fastjson.JSONObject()
                .fluentPut("sessionKey", sessionList.getSessionKey())
                .fluentPut("verifyToken", token)
                .fluentPut("verifyStatus", userVerification.getStatus())
                .fluentPut("verifyMsg", notifyMsg)
                .fluentPut("verifyFailTimes", userVerification.getFailureTimes());
        ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(sessionList.getClientId(), sessionList.getClientTypeId(),
                "cs", "system", "event", "verify", null,
                sessionList.getOrgId(), sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(),
                notifyMsg, null, null, manualInfo, true);

        HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
        String clientSocketId = hashOperations.get(String.format(RedisKey.GLOBAL_CONVERSATION_KEY, sessionList.getClientId(), sessionList.getBotId()), "socketId");
        List<SocketPacketModel<String>> infoList = new ArrayList<>();
        String data = new com.alibaba.fastjson.JSONObject()
                .fluentPut("type", "verify:notify")
                .fluentPut("time", System.currentTimeMillis())
                .fluentPut("manual", manualInfo).toJSONString();
        SocketPacketModel<String> notifyModel = new SocketPacketModel<>(sessionList.getSessionKey(), clientSocketId, SocketEvent.EVENT, data);
        infoList.add(notifyModel);
        SocketPacketModel<String> chatModel = new SocketPacketModel<>(sessionList.getSessionKey(), clientSocketId, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory));
        infoList.add(chatModel);
        String channel = String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT);
        csRedisTemplate.convertAndSend(channel, infoList);
    }
}
