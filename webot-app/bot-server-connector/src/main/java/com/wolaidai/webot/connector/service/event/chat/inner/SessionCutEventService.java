package com.wolaidai.webot.connector.service.event.chat.inner;

import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.data.mysql.entity.chat.ChatSessionEntity;
import com.wolaidai.webot.data.mysql.repo.ChatSessionRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@AllArgsConstructor
public class SessionCutEventService extends BaseEventService {

    private final StringRedisTemplate csStringRedisTemplate;
    private final ChatSessionRepo chatSessionRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        org.json.JSONObject data = (org.json.JSONObject)model.getData();
        JSONObject manual = data.getJSONObject("manual");
        Integer sessionKey = manual.getInt("sessionKey");
        String email = manual.getString("email");
        ChatSessionEntity sessionEntity = chatSessionRepo.findById(sessionKey).orElse(null);
        if(sessionEntity != null) {
            csStringRedisTemplate.opsForHash().put(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY,
                    sessionEntity.getOrgId(), email), "currentInnerSessionKey", String.valueOf(sessionEntity.getId()));
            sessionEntity.getMembers().stream().filter(cm -> cm.getUser().getEmail().equals(email))
                    .findFirst().ifPresent(cm -> cm.setUnreadMsgCount(0));
            sessionEntity.setUpdateTime(new Date());
            chatSessionRepo.save(sessionEntity);
        }
        return null;
    }
}
