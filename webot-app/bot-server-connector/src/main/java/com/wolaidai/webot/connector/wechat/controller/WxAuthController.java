package com.wolaidai.webot.connector.wechat.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.wechat.config.WxOpenProperties;
import com.wolaidai.webot.data.mongodb.entity.WxAuthorizersEntity;
import com.wolaidai.webot.data.mongodb.repo.WxAuthorizersRepo;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.open.bean.result.WxOpenAuthorizerInfoResult;
import me.chanjar.weixin.open.bean.result.WxOpenQueryAuthResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URL;
import java.util.Date;
import java.util.Objects;

@AllArgsConstructor
@RestController
@RequestMapping("/wx/auth")
public class WxAuthController extends BaseWxController{

    private WxOpenProperties wxOpenProperties;

    private WxAuthorizersRepo wxAuthorizersRepo;

    @GetMapping
    public void gotoAuthUrl(String accessKey, HttpServletResponse response) throws IOException {
        response.getWriter().write(String.format("<html><meta http-equiv=\"refresh\" content=\"0; url='%s/wx/auth/pre?accessKey=%s'\"></html>", Objects.toString(request.getContextPath(),""),Objects.toString(accessKey,"")));
    }

    @GetMapping("/pre")
    public void gotoPreAuthUrl(String accessKey, HttpServletResponse response){
        String url = wxOpenProperties.getAuthCallbackUrl()+"?accessKey="+accessKey;
        try {
            url = wxOpenService.getWxOpenComponentService().getPreAuthUrl(url);
            URL callbackUrl = new URL(wxOpenProperties.getAuthCallbackUrl());
            response.addHeader("Referer", callbackUrl.getProtocol()+"://"+callbackUrl.getHost());
            response.sendRedirect(url);
        } catch (WxErrorException | IOException e) {
            logger.error("gotoPreAuthUrl error", e);
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/result")
    @ResponseBody
    public void authResult(@RequestParam("auth_code") String authorizationCode, String accessKey, HttpServletResponse response){
        SerializeConfig config = new SerializeConfig();
        config.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;
        try {
            WxOpenQueryAuthResult queryAuthResult = wxOpenService.getWxOpenComponentService().getQueryAuth(authorizationCode);
            logger.info("accessKey:{},getQueryAuth:{}", accessKey, queryAuthResult);
            String appId = queryAuthResult.getAuthorizationInfo().getAuthorizerAppid();
            WxOpenAuthorizerInfoResult authorizerInfo = wxOpenService.getWxOpenComponentService().getAuthorizerInfo(appId);
            JSONObject authorizationInfoJson = (JSONObject) JSON.toJSON(queryAuthResult.getAuthorizationInfo(), config);
            JSONObject authorizerInfoJson = (JSONObject) JSON.toJSON(authorizerInfo.getAuthorizerInfo(), config);
            WxAuthorizersEntity wxAuthorizersEntity = wxAuthorizersRepo.findByAppId(appId);
            if(wxAuthorizersEntity==null){
                wxAuthorizersEntity = new WxAuthorizersEntity();
                wxAuthorizersEntity.setCreateTime(new Date());
            }
            wxAuthorizersEntity.setAccessKey(accessKey);
            wxAuthorizersEntity.setStatus(1);
            wxAuthorizersEntity.setAuthorization_info(authorizationInfoJson);
            wxAuthorizersEntity.setAuthorizer_info(authorizerInfoJson);
            wxAuthorizersEntity.setUpdateTime(new Date());
            wxAuthorizersRepo.save(wxAuthorizersEntity);
            response.sendRedirect(wxOpenProperties.getAuthReturnUrl());
        } catch (WxErrorException | IOException e) {
            logger.error("authResult error", e);
            throw new RuntimeException(e);
        }
    }
}
