package com.wolaidai.webot.connector.init;

import com.welabx.privacy.crypto.MongoCryptoHelper;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class DataEncInitializer implements CommandLineRunner {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataEncInitializer.class);

    @Autowired
    protected AppPropertyConfig appPropertyConfig;

    @Override
    public void run(String... args) throws Exception {
        try {
            MongoCryptoHelper.getInstance().init(appPropertyConfig.getMongoEncUrl(), appPropertyConfig.getMongoEncAppId(), appPropertyConfig.getMongoEncSecret());
            MongoCryptoHelper.getInstance().setEncryptDatabases(appPropertyConfig.getMongoEncDb());
        } catch (Exception e) {
            LOGGER.error("mongodb encryption init error", e);
            throw new RuntimeException("Mongodb encryption init failed. Application cannot start.");
        }
    }

}
