package com.wolaidai.webot.connector.service.business;

import java.time.Duration;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.data.mysql.entity.config.CombineCommonConfigEntity;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.repo.BotCommonConfigRepo;
import com.wolaidai.webot.data.mysql.repo.CommonConfigRepo;

import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class ConfigService {
    private final long TIMEOUT_MINUTES = 3;

    private RedisTemplate<String, String> csStringRedisTemplate;
    private RedisTemplate<String, String> botStringRedisTemplate;
    private CommonConfigRepo commonConfigRepo;
    private BotCommonConfigRepo botCommonConfigRepo;

    public String read(Integer orgId, String key, String hashKey, String... type) {
        Object value = null;
        boolean isBotType = null != type && type.length > 0 && CommonConfigEntity.isBotType(type[0]);
        if (StringUtils.isNotBlank(hashKey)) {
            value = isBotType ? botStringRedisTemplate.opsForHash().get(key, hashKey) : csStringRedisTemplate.opsForHash().get(key, hashKey);
        } else {
            value = isBotType ? botStringRedisTemplate.opsForValue().get(key) : csStringRedisTemplate.opsForValue().get(key);
        }
        int len = 0;
        if (null == value && null != type && (len = type.length) > 0) {
            CombineCommonConfigEntity conf = null;
            if (len > 2 && StringUtils.isNotBlank(type[2])) {
                conf = isBotType ? botCommonConfigRepo.findOneByOrgIdAndTypeAndSubType1AndSubType2(orgId, type[0], type[1], type[2]) : commonConfigRepo.findOneByOrgIdAndTypeAndSubType1AndSubType2(orgId, type[0], type[1], type[2]);
            } else if (len > 1 && StringUtils.isNotBlank(type[1])) {
                conf = isBotType ? botCommonConfigRepo.findOneByOrgIdAndTypeAndSubType1(orgId, type[0], type[1]) : commonConfigRepo.findOneByOrgIdAndTypeAndSubType1(orgId, type[0], type[1]);
            } else if (StringUtils.isNotBlank(type[0])) {
                conf = isBotType ? botCommonConfigRepo.findOneByOrgIdAndType(orgId, type[0]) : commonConfigRepo.findOneByOrgIdAndType(orgId, type[0]);
            }
            if (null != conf) {
                String content = conf.getContent();
				if (null == content) {
                    content = "";
                }
                if (StringUtils.isNotBlank(hashKey)) {
                    if (isBotType) {
                        botStringRedisTemplate.opsForHash().put(key, hashKey, content);
                        botStringRedisTemplate.expire(key, Duration.ofMinutes(TIMEOUT_MINUTES));
                    } else {
                        csStringRedisTemplate.opsForHash().put(key, hashKey, content);
                        csStringRedisTemplate.expire(key, Duration.ofMinutes(TIMEOUT_MINUTES));
                    }
                } else {
                    if (isBotType) {
                        botStringRedisTemplate.opsForValue().set(key, content, Duration.ofMinutes(TIMEOUT_MINUTES));
                    } else {
                        csStringRedisTemplate.opsForValue().set(key, content, Duration.ofMinutes(TIMEOUT_MINUTES));
                    }
                }
                return content;
            }
        }
        return null == value ? null : value.toString();
    }

    public JSONObject readData(Integer orgId, String key, String... type) {
        boolean isBotType = null != type && type.length > 0 && CommonConfigEntity.isBotType(type[0]);
        HashOperations<String, String, Object> hash = isBotType ? botStringRedisTemplate.opsForHash() : csStringRedisTemplate.opsForHash();
        Map<String, Object> map = hash.entries(key);
        if (null != map && map.size() > 0) {
            return new JSONObject(map);
        }
        JSONObject json = new JSONObject();
        int len = 0;
        if (null != type && (len = type.length) > 0) {
            List<? extends CombineCommonConfigEntity> list = null;
            if (len > 2 && StringUtils.isNotBlank(type[2])) {
                list = isBotType ? botCommonConfigRepo.findByOrgIdAndTypeAndSubType1AndSubType2(orgId, type[0], type[1], type[2]) : commonConfigRepo.findByOrgIdAndTypeAndSubType1AndSubType2(orgId, type[0], type[1], type[2]);
            } else if (len > 1 && StringUtils.isNotBlank(type[1])) {
                list = isBotType ? botCommonConfigRepo.findByOrgIdAndTypeAndSubType1(orgId, type[0], type[1]) : commonConfigRepo.findByOrgIdAndTypeAndSubType1(orgId, type[0], type[1]);
            } else if (StringUtils.isNotBlank(type[0])) {
                list = isBotType ? botCommonConfigRepo.findByOrgIdAndType(orgId, type[0]) : commonConfigRepo.findByOrgIdAndType(orgId, type[0]);
            }
            if (null != list && list.size() > 0) {
                for (CombineCommonConfigEntity conf : list) {
                    String subType1 = conf.getSubType1();
                    String subType2 = conf.getSubType2();
                    String content = conf.getContent();
					if (null == content) {
                        content = "";
                    }
                    if (StringUtils.isNotBlank(subType2)) {
                        Object o = json.get(subType1);
                        if (null != o && o instanceof JSONObject) {
                            ((JSONObject) o).put(subType2, content);
                        } else {
                            JSONObject item = new JSONObject();
                            item.put(subType2, content);
                            o = item;
                        }
                        json.put(subType1, o);
                        hash.put(key, subType2, content);
                    } else {
                        json.put(subType1, content);
                        hash.put(key, subType1, content);
                    }
                }
                if (isBotType) {
                    botStringRedisTemplate.expire(key, Duration.ofMinutes(TIMEOUT_MINUTES));
                } else {
                    csStringRedisTemplate.expire(key, Duration.ofMinutes(TIMEOUT_MINUTES));
                }
            }
        }
        if (len > 1 && StringUtils.isNotBlank(type[1])) {
            return json.getJSONObject(type[1]);
        }
        return json;
    }
}
