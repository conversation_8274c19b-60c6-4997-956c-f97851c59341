package com.wolaidai.webot.connector.service.message.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.connector.constant.*;
import com.wolaidai.webot.connector.entity.Event;
import com.wolaidai.webot.connector.entity.EventProcessResult;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.model.SocketRoomModel;
import com.wolaidai.webot.connector.service.SocketService;
import com.wolaidai.webot.connector.service.business.ChatHistoryService;
import com.wolaidai.webot.connector.service.business.ConversationService;
import com.wolaidai.webot.connector.service.business.KeyExpireService;
import com.wolaidai.webot.connector.service.business.ManualChatService;
import com.wolaidai.webot.connector.service.business.SessionListService;
import com.wolaidai.webot.connector.service.message.MessageProcessService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.connector.wechat.service.WechatCpService;
import com.wolaidai.webot.connector.wechat.service.WechatMessageService;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.repo.QueueListRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@AllArgsConstructor
public class MessageProcessServiceImpl implements MessageProcessService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final StringRedisTemplate csStringRedisTemplate;

    private final RedisTemplate csRedisTemplate;

    private final SocketService socketService;

    private final SessionListRepo sessionListRepo;

    private final ChatHistoryService chatHistoryService;

    private final ManualChatService manualChatService;

    private final QueueListRepo queueListRepo;
    private final ConversationService conversationService;
    private final WechatMessageService wechatMessageService;
    private final WechatCpService wechatCpService;
    private final KeyExpireService keyExpireService;
    private final SessionListService sessionListService;

    @Override
    public EventProcessResult process(Event event) {
        EventProcessResult eventProcessResult = new EventProcessResult();
        String eventKey = event.getEventKey();
        try {
            JSONObject content = event.getContent();
            if (Event.QUEUE_DONE_KEY.equals(eventKey)) {
                String sessionKey = content.getString("sessionKey");
                String nickName = content.getString("nickName");
                HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
                if (sessionList != null) {
                    boolean isWechat = wechatMessageService.isWechat(sessionList.getClientId(), sessionList.getBotId());
                    boolean isWorkWechat = wechatCpService.isWorkWechat(sessionList.getClientId(), sessionList.getBotId());
                    sessionListRepo.updateStatusById(sessionList.getId(), SessionListEntity.STATUS_ONLINE);

                    manualChatService.notifyQueueStatus(event.getOrgId(), -1, sessionList.getCustomerType(), true);

                    String csSocketId = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, event.getOrgId(), sessionList.getLastServiceUser()), "socketId");
                    String clientSocketId = hashOperations.get(String.format(RedisKey.GLOBAL_CONVERSATION_KEY, sessionList.getClientId(), sessionList.getBotId()), "socketId");
                    socketService.joinOrLeaveGlobalRoom(SocketNamespace.CHAT, SocketRoomModel.ADD_TYPE, Collections.singletonList(sessionKey), csSocketId, clientSocketId);

                    hashOperations.put(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, sessionList.getOrgId(), sessionList.getLastServiceUser()), "nickName", nickName);

                    String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, sessionList.getClientId(), sessionList.getBotId());
                    if (csStringRedisTemplate.hasKey(conversationKey)) {
                        Map<String,String> map = new HashMap<>();
                        map.put("sessionKey", sessionKey);
                        map.put("scene", "cs");
                        hashOperations.putAll(conversationKey, map);
                        String cid = hashOperations.get(conversationKey, "cid");
                        if(cid!=null){
                            conversationService.endBotConversation(cid);
                            hashOperations.delete(conversationKey,"cid");
                        }
                    }
                    Map<String, Object> manual = new HashMap<>();
                    manual.put("sessionKey", sessionKey);
                    manual.put("email", sessionList.getServiceUser());
                    manual.put("nickName", nickName);
                    List<SocketPacketModel> packetList = new ArrayList<>();
                    ChatHistoryEntity chatHistory1 = chatHistoryService.createChatHistory(sessionList.getClientId(), sessionList.getClientTypeId(), "cs", "system", "event", "queue:done", null,
                            sessionList.getOrgId(), sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(), "已轮到您咨询", null, null, manual, true);
                    packetList.add(new SocketPacketModel(sessionKey, csSocketId, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory1)));
                    ChatHistoryEntity chatHistory2 = chatHistoryService.createChatHistory(sessionList.getClientId(), sessionList.getClientTypeId(), "cs", "system", "event", "session:begin", null,
                            sessionList.getOrgId(), sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(), String.format("客服 %s 正在为您服务", nickName), null, null, manual, true);
                    //微信合并为一条消息发送给用户
                    String wechatMsg = chatHistory1.getContent() + "。" + chatHistory2.getContent();
                    if(isWechat){
                        wechatMessageService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),wechatMsg);
                    }else if(isWorkWechat){
                        wechatCpService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),wechatMsg);
                    }
                    packetList.add(new SocketPacketModel(sessionKey, csSocketId, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory2)));

                    org.json.JSONObject noticeMsgJson = manualChatService.getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_NOTICE_MSG);
                    if (noticeMsgJson != null) {
                        if (noticeMsgJson.getBoolean("status")) {
                            ChatHistoryEntity chatHistory3 = chatHistoryService.createChatHistory(sessionList.getClientId(), sessionList.getClientTypeId(), "cs", "system", "text", null, null,
                                    sessionList.getOrgId(), sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(), noticeMsgJson.getString("content"), null, null, manual, true);
                            if(isWechat){
                                wechatMessageService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),chatHistory3.getContent());
                            }else if(isWorkWechat){
                                wechatCpService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),chatHistory3.getContent());
                            }
                            packetList.add(new SocketPacketModel(sessionKey, csSocketId, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory3)));
                        }
                    }

                    org.json.JSONObject welcomeMsgJson = manualChatService.getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_WELCOME_MSG);
                    if (welcomeMsgJson != null) {
                        if (welcomeMsgJson.getBoolean("status")) {
                            ChatHistoryEntity chatHistory4 = chatHistoryService.createChatHistory(sessionList.getClientId(), sessionList.getClientTypeId(), "cs", "system", "text", null, null,
                                    sessionList.getOrgId(), sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(), welcomeMsgJson.getString("content"), null, null, manual, true);
                            if(isWechat){
                                wechatMessageService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),chatHistory4.getContent());
                            }else if(isWorkWechat){
                                wechatCpService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),chatHistory4.getContent());
                            }
                            packetList.add(new SocketPacketModel(sessionKey, csSocketId, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory4)));
                            sessionListRepo.updateLastMsgById(sessionList.getId(), MessageUtil.escapeHtml(chatHistory4.getContent(), chatHistory4.getType()), chatHistory4.getDate());
                        }
                    }
                    org.json.JSONObject customerTimeoutMsg = manualChatService.getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_CUSTOMER_TIMEOUT_MSG);
                    if (customerTimeoutMsg != null && customerTimeoutMsg.getBoolean("status")) {
                        int timeoutMinutes = customerTimeoutMsg.getInt("timeoutMinutes");
                        if (timeoutMinutes > 0) {
                            keyExpireService.setExpireKey(String.format(RedisKey.CS_LAST_REPLY_SESSION_KEY, sessionList.getSessionKey()), timeoutMinutes, TimeUnit.MINUTES);
                        }
                    }
                    org.json.JSONObject customerOfflineMsg = manualChatService.getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_CUSTOMER_OFFLINE_MSG);
                    if (customerOfflineMsg != null && customerOfflineMsg.getBoolean("status")) {
                        int timeoutMinutes = customerOfflineMsg.getInt("timeoutMinutes");
                        if (timeoutMinutes > 0) {
                            keyExpireService.setExpireKey(String.format(RedisKey.CS_OFFLINE_SESSION_KEY, sessionList.getSessionKey()), timeoutMinutes, TimeUnit.MINUTES);
                        }
                    }
                    packetList.add(new SocketPacketModel(sessionKey, null, SocketEvent.EVENT, new JSONObject().fluentPut("type", "queue:done").fluentPut("manual", new JSONObject().fluentPut("email", sessionList.getServiceUser()).fluentPut("nickName", nickName)).toJSONString()));
                    packetList.add(new SocketPacketModel(String.format(SocketRoomConstants.GLOBAL_ORG_ROOM_NAME, event.getOrgId()), csSocketId, SocketEvent.EVENT, new JSONObject().fluentPut("type", "queue:done").toJSONString()));
                    csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), packetList);
                }
            } else if (Event.QUEUING_KEY.equals(eventKey)) {
                csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), Collections.singletonList(new SocketPacketModel(String.format(SocketRoomConstants.GLOBAL_ORG_ROOM_NAME, event.getOrgId()), null, SocketEvent.EVENT, new JSONObject().fluentPut("type", "queue:queuing").toJSONString())));
                HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, content.getString("clientId"), content.getInteger("botId"));
                if (csStringRedisTemplate.hasKey(conversationKey)) {
                    hashOperations.put(conversationKey, "scene", "cs");
                }
                manualChatService.notifyQueueStatus(event.getOrgId(), content.getIntValue("queueId"), content.getIntValue("customerType"), false);
            } else if (Event.QUEUE_TIMEOUT_KEY.equals(eventKey)) {
                Integer queueId = content.getInteger("queueId");
                if(queueId!=null) {
                    HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                    String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, content.getString("clientId"), content.getInteger("botId"));
                    if (csStringRedisTemplate.hasKey(conversationKey)) {
                        hashOperations.put(conversationKey, "scene", "bot");
                    }
                    String clientSocketId = hashOperations.get(String.format(RedisKey.GLOBAL_CONVERSATION_KEY, content.getString("clientId"), content.getInteger("botId")), "socketId");
                    List<SocketPacketModel> packetList = new ArrayList<>();
                    packetList.add(new SocketPacketModel(String.format(SocketRoomConstants.GLOBAL_ORG_ROOM_NAME, event.getOrgId()), null, SocketEvent.EVENT, new JSONObject().fluentPut("type", "queue:timeout").toJSONString()));
                    if (clientSocketId != null) {
                        queueListRepo.findById(queueId).ifPresent(v->{
                            org.json.JSONObject serviceOfflineMsgJson = manualChatService.getAutoReplyConfig(v.getOrgId(), v.getClientTypeId(),ManualConfigConstants.AUTO_REPLY_SERVICE_OFFLINE_MSG);
                            if(serviceOfflineMsgJson!=null){
                                if(serviceOfflineMsgJson.getBoolean("status")) {
                                    String serviceOfflineMsgContent = serviceOfflineMsgJson.getString("content");
                                    ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(v.getClientId(), v.getClientTypeId(), "cs", "system", "event", "queue:timeout",
                                            null, v.getOrgId(), v.getBotId(), v.getBusinessId(), v.getGcid(), serviceOfflineMsgContent, false);
                                    packetList.add(new SocketPacketModel(null, clientSocketId, SocketEvent.MESSAGE, JSON.toJSONString(chatHistory)));

                                }
                            }
                        });
                    }
                    csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), packetList);
                    manualChatService.notifyQueueStatus(event.getOrgId(), queueId, content.getIntValue("customerType"), true);
                }
            } else if (Event.CLOSE_SESSION_KEY.equals(eventKey)) {
                manualChatService.offlineSession(content.getString("sessionKey"), content.getInteger("closeType"));
            } else if(Event.USER_STATE_KEY.equals(eventKey)) {
                String email = content.getString("email");
                Integer state = content.getInteger("state");
                csStringRedisTemplate.opsForHash().put(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, event.getOrgId(), email),"state",String.valueOf(state));
                csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), Collections.singletonList(new SocketPacketModel(String.format(SocketRoomConstants.GLOBAL_ORG_ROOM_NAME, event.getOrgId()), null, SocketEvent.EVENT, new JSONObject().fluentPut("type", "user:state").fluentPut("manual", content).toJSONString())));
            } else if(Event.TRANS_SESSION_KEY.equals(eventKey)) {
                //倒计时
                String transId = content.getString("transId");
                keyExpireService.setExpireKey(String.format(RedisKey.CS_SESSION_TRANS_KEY, transId),90, TimeUnit.SECONDS);
                HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                String toSocketId = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, event.getOrgId(), content.getString("to")), "socketId");
                if (StringUtils.isNotBlank(toSocketId)) {
                    String sessionKey = content.getString("sessionKey");
                    String offlineSessionKey = String.format(RedisKey.CS_OFFLINE_SESSION_KEY, sessionKey);
                    String timeMinutes = csStringRedisTemplate.opsForValue().get(offlineSessionKey);
                    if(timeMinutes!=null){
                        keyExpireService.setExpireKey(offlineSessionKey, Integer.parseInt(timeMinutes), TimeUnit.MINUTES);
                    }
                    SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
                    sessionListService.decryptCustomerInfo(sessionList);
                    //转接给目标客服
                    csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT),
                            Collections.singletonList(new SocketPacketModel(null, toSocketId, SocketEvent.EVENT,
                                    new JSONObject().fluentPut("type", SocketEvent.SESSION_TRANS_TYPE).fluentPut("manual",
                                            new JSONObject().fluentPut("sessionKey", sessionKey)
                                                    .fluentPut("transId", content.getInteger("transId")).fluentPut("from", content.getString("from")).fluentPut("fromName", content.getString("fromName"))
                                                    .fluentPut("to", content.getString("to")).fluentPut("toName", content.getString("toName")).fluentPut("remark", content.getString("remark"))
                                                    .fluentPut("customerName", sessionList.getCustomerName()).fluentPut("customerDetail", sessionList.getCustomerDetail())
                                                    .fluentPut("businessName", sessionList.getBusinessName())).toJSONString())));
                }
            } else if(Event.SUMMARY_NOTIFY_KEY.equals(eventKey)){
                HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                JSONArray details = content.getJSONArray("details");
                for (int i = 0; i < details.size(); i++) {
                    JSONObject detail = details.getJSONObject(i);
                    String socketId = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, event.getOrgId(), detail.getString("email")), "socketId");
                    if(StringUtils.isNotBlank(socketId)){
                        csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT),
                                Collections.singletonList(new SocketPacketModel(null, socketId, SocketEvent.EVENT, new JSONObject().fluentPut("type", "summary:notify").fluentPutAll(detail).fluentPut("time", content.get("time")).fluentPut("target", content.get("target")).toJSONString())));
                    }
                }
            }else {
                LOGGER.error("不支持该eventKey:{}", eventKey);
                eventProcessResult.setSuccess(false);
                eventProcessResult.setErrorMsg("eventKey错误");
                return eventProcessResult;
            }
        } catch (Exception e) {
            LOGGER.error("事件处理失败,eventKey:{}", eventKey, e);
            eventProcessResult.setSuccess(false);
            eventProcessResult.setErrorMsg(e.getMessage());
        }
        return eventProcessResult;
    }
}
