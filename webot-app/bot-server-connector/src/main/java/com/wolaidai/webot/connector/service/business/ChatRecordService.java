package com.wolaidai.webot.connector.service.business;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.service.BaseService;
import com.wolaidai.webot.connector.utils.MsgIdUtils;
import com.wolaidai.webot.data.mysql.entity.chat.ChatMembersSnapshotEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ChatRecordEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ChatSessionEntity;
import com.wolaidai.webot.data.mysql.repo.ChatRecordRepo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@AllArgsConstructor
public class ChatRecordService extends BaseService {

    private final ChatRecordRepo chatRecordRepo;

    public ChatRecordEntity createChatRecord(String msgId, String from, String to, String content,
                                             String type, ChatSessionEntity sessionEntity, ChatMembersSnapshotEntity snapshotEntity, JSONObject media, JSONObject manual) {
        ChatRecordEntity recordEntity = new ChatRecordEntity();
        recordEntity.setOrgId(sessionEntity.getOrgId());
        recordEntity.setSession(sessionEntity);
        recordEntity.setSnapshot(snapshotEntity);
        if (StringUtils.isBlank(msgId)) {
            msgId = MsgIdUtils.generateMsgId();
        }
        recordEntity.setMsgId(msgId);
        recordEntity.setFromEmail(from);
        recordEntity.setToEmail(to);
        recordEntity.setContent(content);
        recordEntity.setType(type);
        recordEntity.setEventKey(SocketEvent.INNER_SESSION_RELOAD_TYPE);
        recordEntity.setMedia(media);
        recordEntity.setManual(manual);
        recordEntity.setDate(new Date());
        chatRecordRepo.save(recordEntity);
        return recordEntity;
    }
}
