package com.wolaidai.webot.connector.model;

import lombok.Data;

@Data
public class PageableModel extends BaseModel {
    // first page is 0
    private int pageNumber = 0;
    private int pageSize = 20;
    private long total;
    private String sort;
    private String direction;

    public int getTotalPages() {
        return getPageSize() == 0 ? 0 : (int) Math.ceil((double) total / (double) getPageSize());
    }
}
