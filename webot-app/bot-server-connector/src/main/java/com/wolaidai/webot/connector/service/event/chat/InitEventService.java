package com.wolaidai.webot.connector.service.event.chat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.constant.*;
import com.wolaidai.webot.connector.model.*;
import com.wolaidai.webot.connector.service.SocketService;
import com.wolaidai.webot.connector.service.business.ChatHistoryService;
import com.wolaidai.webot.connector.service.business.ConversationService;
import com.wolaidai.webot.connector.service.business.KeyExpireService;
import com.wolaidai.webot.connector.service.business.ManualChatService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.connector.wechat.utils.MsgUtils;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mysql.entity.bot.RecommendQaEntity;
import com.wolaidai.webot.data.mysql.entity.bot.WelcomeEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ChatSessionEntity;
import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.repo.*;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class InitEventService extends BaseEventService {
    private final QueueListRepo queueListRepo;
    private final WelcomeRepo welcomeRepo;
    private final ChatHistoryService chatHistoryService;
    private final ConversationService conversationService;
    private final StringRedisTemplate csStringRedisTemplate;
    private final SocketService socketService;
    private final SessionListRepo sessionListRepo;
    private final ManualChatService manualChatService;
    private final ComplexChatSessionRepo complexChatSessionRepo;
    private final UserStateRepo userStateRepo;
    private final ChatRoomRepo chatRoomRepo;
    private final KeyExpireService keyExpireService;
    private final AppPropertyConfig appPropertyConfig;
    private final RecommendQaRepo recommendQaRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject) model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        String socketId = model.getSocketId();
        String source = data.optString("source");
        if (Objects.equals(source, "H5")) {
            BotInitModel botInitModel = JSON.parseObject(data.toString(), BotInitModel.class);
            String clientId = botInitModel.getClientId();
            Integer clientType = botInitModel.getClientType();
            String accessKey = botInitModel.getAccessKey();
            String skillGroupId = botInitModel.getSkillGroupId();
            if (StringUtils.isNotBlank(clientId) && StringUtils.isNotBlank(accessKey) && StringUtils.isNotBlank(skillGroupId)) {
                ConversationContextModel botConversation = conversationService.getConversation(socketId, clientId, clientType, botInitModel.getAccount(), accessKey, skillGroupId, true, Objects.equals(botInitModel.getReconnect(),true));
                if (botConversation == null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret", 1).put("msg", "param invalid"));
                    return null;
                }
                ConversationContextModel.ContextInfo contextInfo = botConversation.getInfo();
                if(contextInfo.getSessionKey()!=null){
                    keyExpireService.removeExpireKey(String.format(RedisKey.CS_CLIENT_STATUS_TIMEOUT,contextInfo.getSessionKey()));
                    models.add(new SocketPacketModel(contextInfo.getSessionKey(), socketId, SocketEvent.EVENT, new JSONObject()
                            .put("type", SocketEvent.CLIENT_STATUS_TYPE).put("manual", new JSONObject().put("sessionKey", contextInfo.getSessionKey()).put("status",1)).toString()));
                }
                HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                String socketKey = String.format(RedisKey.H5_SOCKET_KEY,socketId);
                Map<String, String> socketData = new HashMap<>();
                socketData.put("orgId", String.valueOf(contextInfo.getOrgId()));
                socketData.put("clientId", clientId);
                socketData.put("clientType", String.valueOf(clientType));
                socketData.put("botId", String.valueOf(contextInfo.getBotId()));
                socketData.put("time",String.valueOf(System.currentTimeMillis()));
                hashOperations.putAll(socketKey,socketData);
                csStringRedisTemplate.expire(socketKey,1, TimeUnit.DAYS);
                List<String> allSessionKeys = sessionListRepo.findSessionKeysByClientId(contextInfo.getOrgId(), clientId, DateUtils.truncate(new Date(), Calendar.DATE));
                if (!CollectionUtils.isEmpty(allSessionKeys)) {
                    socketService.joinOrLeaveLocalRoom(SocketNamespace.CHAT, SocketRoomModel.ADD_TYPE, allSessionKeys, contextInfo.getSocketId());
                }
                QueueListEntity queueList = queueListRepo.findIdByInQueueStatusAndCreateTime(contextInfo.getOrgId(), clientId, DateUtils.truncate(new Date(), Calendar.DATE));
                if (queueList!=null) {
                    if(callback!=null){
                        callback.sendAcknowledgement(new JSONObject().put("scene", "cs"));
                    }
                    if(!"cs".equals(contextInfo.getScene())){
                        hashOperations.put(String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, queueList.getBotId()), "scene","cs");
                    }
                    manualChatService.notifyQueueStatus(contextInfo.getOrgId(),queueList.getId(),queueList.getCustomerType(),false);
                 }else {
                    if (botConversation.isNew() && !Objects.equals(botInitModel.getReconnect(), true)) {
                        WelcomeEntity welcome = welcomeRepo.findByBotId(contextInfo.getBotId());
                        if (welcome != null) {
                            ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(null, clientId, contextInfo.getClientType(), "bot", "bot", "text", null, contextInfo.getAccount(),
                                    contextInfo.getOrgId(), contextInfo.getBotId(), contextInfo.getSkillGroupId(), contextInfo.getGcid(), contextInfo.getCid(), welcome.getContent(), null, null, null, null, null, true);
                            models.add(new SocketPacketModel(null, socketId, SocketEvent.MESSAGE, JSON.toJSONString(chatHistory)));
                        }
                    }
                    if (!Objects.equals(botInitModel.getReconnect(), true) && StringUtils.isNotBlank(botInitModel.getAccount()) 
                            && "bot".equals(contextInfo.getScene())){
                        // 查询用户状态
                        JSONObject hotQuestions = queryUserStateAndRtHotQuestions(botInitModel.getAccount(), contextInfo.getSkillGroupId());
                        if (Objects.nonNull(hotQuestions)) {
                            String content = MsgUtils.getH5MenuStr(hotQuestions);
                            ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(null, clientId, contextInfo.getClientType(), "bot", "bot", "menu", null, contextInfo.getAccount(),
                                    contextInfo.getOrgId(), contextInfo.getBotId(), contextInfo.getSkillGroupId(), contextInfo.getGcid(), contextInfo.getCid(), content, null, null, null, hotQuestions.toMap(), null, true);
                            models.add(new SocketPacketModel(null, socketId, SocketEvent.MESSAGE, JSON.toJSONString(chatHistory)));
                        }
                    }
                    if (callback!=null) {
                        callback.sendAcknowledgement(new JSONObject().put("scene", contextInfo.getScene()));
                    }
                }
            }
        } else if (Objects.equals(source, "cs") || Objects.equals(source, ManualConfigConstants.WORKBENCH_TYPE)) {
            CsInitModel csInitModel = JSON.parseObject(data.toString(), CsInitModel.class);
            Integer orgId = csInitModel.getOrgId();
            String email = csInitModel.getEmail();
            UserStateEntity userState = userStateRepo.findByOrgIdAndEmail(orgId, email);
            if (userState == null) {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret",-1)
                            .put("msg", "该用户未激活账号").put("timestamp",System.currentTimeMillis()));
                }
                return null;
            }
            socketService.joinOrLeaveLocalRoom(SocketNamespace.CHAT, SocketRoomModel.ADD_TYPE, Collections.singletonList(String.format(SocketRoomConstants.GLOBAL_ORG_ROOM_NAME, orgId)), socketId);
            //客服会话
            List<String> allSessionKeys = sessionListRepo.findSessionKeysByEmail(orgId, email, DateUtils.truncate(new Date(), Calendar.DATE));
            if (!allSessionKeys.isEmpty()) {
                socketService.joinOrLeaveLocalRoom(SocketNamespace.CHAT, SocketRoomModel.ADD_TYPE, allSessionKeys, socketId);
            }
            //内部会话
            List<ChatSessionEntity> sessions = complexChatSessionRepo.findByOrgIdAndUserEmail(orgId, email);//所有相关会话
            if (!CollectionUtils.isEmpty(sessions)) {
                //剔除已经退出的群聊会话
                sessions.removeIf(s -> s.getRoom() != null
                        && s.getRoom().getRoomUsers().stream().noneMatch(u -> u.getUser().getEmail().equals(email)));
                socketService.joinOrLeaveLocalRoom(SocketNamespace.CHAT, SocketRoomModel.ADD_TYPE, sessions.stream().map(v -> "session_" + v.getId()).collect(Collectors.toList()), socketId);
            }
            HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
            String onlineEmailKey = String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, email);
            String lastSocketId = hashOperations.get(onlineEmailKey, "socketId");
            hashOperations.put(onlineEmailKey, "socketId", socketId);
            String nickName = userState.getNickName();
            if(StringUtils.isNotBlank(nickName)) {
                hashOperations.put(onlineEmailKey, "nickName", nickName);
            }
            keyExpireService.removeExpireKey(onlineEmailKey);
            //清楚当前会话标记
            hashOperations.delete(onlineEmailKey,"currentSessionKey");
            hashOperations.delete(onlineEmailKey,"currentInnerSessionKey");
            String socketKey = String.format(RedisKey.CS_SOCKET_KEY,socketId);
            Map<String, String> socketData = new HashMap<>();
            socketData.put("orgId", String.valueOf(orgId));
            socketData.put("email", email);
            if(StringUtils.isNotBlank(nickName)) {
                socketData.put("nickName", nickName);
            }
            if (StringUtils.isNotBlank(userState.getName())) {
                hashOperations.put(onlineEmailKey, "name", userState.getName());
            }
            socketData.put("time", String.valueOf(System.currentTimeMillis()));
            hashOperations.putAll(socketKey,socketData);
            csStringRedisTemplate.expire(socketKey,1, TimeUnit.DAYS);
            if(StringUtils.isNotBlank(lastSocketId)&&!Objects.equals(lastSocketId, socketId)){
                models.add(new SocketPacketModel(null, lastSocketId, SocketEvent.EVENT, new JSONObject().put("type","user:kick").toString()));
            }
            if (callback != null) {
                callback.sendAcknowledgement(new JSONObject().put("ret", 0).put("msg", "success"));
            }
        }
        return models;
    }

    public JSONObject queryUserStateAndRtHotQuestions(String account, Integer skillGroupId) {
        if (StringUtils.isBlank(account)){
            return null;
        }

	    try {
            String res = HttpClientUtil.get(appPropertyConfig.getKefuUrlPre() + "v1/user/query-by-mobile/no-auth?mobile=" + account);
            if (StringUtils.isBlank(res)) {
                return null;
            }
            com.alibaba.fastjson.JSONObject resJson = JSON.parseObject(res);
            if (!"0".equals(resJson.getString("code"))) {
                return null;
            }
            Integer userState = resJson.getInteger("result");


            List<RecommendQuestionRowModel> hotList = queryHotList(skillGroupId);
            if (CollectionUtils.isEmpty(hotList)){
                return null;
            }

            UserStateEnum userStateEnum = UserStateEnum.valueOfCode(String.valueOf(userState));
            if (Objects.isNull(userStateEnum)) {
                return null;
            }
            Optional<RecommendQuestionRowModel> first = hotList.stream().filter(hot -> userStateEnum.msg.equals(hot.getCategory())).findFirst();

            if (!first.isPresent()){
                return null;
            }
            StringBuilder hotQuestions = new StringBuilder();

            JSONObject hotQuestionJson = new JSONObject();
            hotQuestionJson.put("head_content", "热点问题\n");
            hotQuestionJson.put("tail_content","");
            List<JSONObject> contentJsonList = new ArrayList<>();
            hotQuestions.append("热点问题").append("\n");
            for (int i = 0; i < first.get().getQas().size(); i++) {
                JSONObject contentJson = new JSONObject();
                contentJson.put("content", (i + 1) + "." + first.get().getQas().get(i));
                contentJsonList.add(contentJson);
            }
            hotQuestionJson.put("list", contentJsonList);
            


            return hotQuestionJson;

        } catch (Exception e) {
            LOGGER.warn("queryUserStateAndSendHotQuestions, error, mobile: {}", account, e);
	    }
        
        return null;

    }

    private List<RecommendQuestionRowModel> queryHotList(Integer skillGroupId) {
        List<RecommendQaEntity> list = recommendQaRepo.findBySkillGroupId(skillGroupId);
        List<RecommendQuestionRowModel> result = new ArrayList<>();
        if (list.size() > 0) {
            com.alibaba.fastjson.JSONObject json = list.get(0).getQuestion();
            JSONArray categories = null;
            com.alibaba.fastjson.JSONObject details = null;
            if (null != json && null != (categories = json.getJSONArray("categories")) && null != (details = json.getJSONObject("details"))) {
                final com.alibaba.fastjson.JSONObject d = details;
                result = categories.stream().map(i -> new RecommendQuestionRowModel((String) i, d.getJSONArray((String) i))).collect(Collectors.toList());
            }
        }        
        return result;
    }
}
