package com.wolaidai.webot.connector.service.event.chat;

import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.fluent.Request;
import org.json.JSONObject;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.wolaidai.webot.common.util.OssFileClient;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.business.ApiService;
import com.wolaidai.webot.connector.service.business.ChatHistoryService;
import com.wolaidai.webot.connector.service.business.SessionListService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.connector.wechat.service.WechatCpService;
import com.wolaidai.webot.connector.wechat.service.WechatMessageService;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.face.FaceDetectionEntity;
import com.wolaidai.webot.data.mysql.repo.FaceDetectionRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;

import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class FaceDetectionService extends BaseEventService {

    private final SessionListRepo sessionListRepo;
    private final ApiService apiService;
    private final AppPropertyConfig appPropertyConfig;
    private final StringRedisTemplate csStringRedisTemplate;
    private final FaceDetectionRepo faceDetectionRepo;
    private final ChatHistoryService chatHistoryService;
    private final WechatMessageService wechatMessageService;
    private final WechatCpService wechatCpService;
    private final SessionListService sessionListService;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject) model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        JSONObject manualObjc = data.getJSONObject("manual");
        String sessionKey = manualObjc.getString("sessionKey");
        SessionListEntity sessionList = null;
        if (StringUtils.isBlank(sessionKey) || null == (sessionList = sessionListRepo.findBySessionKey(sessionKey))) {
            if (callback != null) {
                callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", "会话不存在").put("timestamp", System.currentTimeMillis()));
            }
            LOGGER.warn("会话不存在,data:{}", data);
            return null;
        }
        if (Objects.equals(sessionList.getStatus(), SessionListEntity.STATUS_OFFLINE)) {
            if (callback != null) {
                callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", "会话已结束").put("timestamp", System.currentTimeMillis()));
            }
            LOGGER.warn("会话已结束,data:{}", data);
            return null;
        }
        sessionListService.decryptCustomerInfo(sessionList);
        com.alibaba.fastjson.JSONObject detail = sessionList.getCustomerDetail();
        com.alibaba.fastjson.JSONArray customers = null != detail ? detail.getJSONArray("customers") : null;
        com.alibaba.fastjson.JSONObject json = null != customers && customers.size() > 0 ? customers.getJSONObject(0) : null;
        String mobile = null;
        if (null == json || StringUtils.isBlank(mobile = json.getString("mobile"))) {
            if (callback != null) {
                callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", "客户手机号码不存在").put("timestamp", System.currentTimeMillis()));
            }
            LOGGER.warn("客户手机号码不存在,data:{}", data);
            return null;
        }
        FaceDetectionEntity fd = faceDetectionRepo.findBySessionId(sessionList.getId());
        if (null != fd) {
            String key = String.format(RedisKey.CS_FACE_DETECTION, fd.getToken());
            String msg = null;
            if (Objects.equals(fd.getCode(), 0)) {
                msg = "已发起人脸验证请求，验证成功，请勿重复操作";
            } else if (csStringRedisTemplate.hasKey(key) && (null == fd.getFailureTimes() || fd.getFailureTimes() < appPropertyConfig.getMaxFaceDetectFailureTimes())) {
                msg = "已发起人脸验证请求，请勿重复操作";
            }
            if (null != msg) {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", msg).put("timestamp", System.currentTimeMillis()));
                }
                LOGGER.warn("已发起人脸验证请求,data:{}", data);
                return null;
            }
        }
        String url = apiService.getFaceDocument(mobile);
        try {
            InputStream is = Request.Get(url).execute().returnContent().asStream();
            String token = UUID.randomUUID().toString();
            String fileDir = "cs/facedection/" + DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now()) + "/" + token + "/";
            String file = fileDir + "origin_" + token + ".jpg";
            OssFileClient.putObject(appPropertyConfig.getOssBucketName(), file, is);
            String key = String.format(RedisKey.CS_FACE_DETECTION, token);
            csStringRedisTemplate.opsForHash().put(key, "failureTimes", "0");
            csStringRedisTemplate.opsForHash().put(key, "photo", file);
            csStringRedisTemplate.opsForHash().put(key, "clientTypeId", String.valueOf(sessionList.getClientTypeId()));
            csStringRedisTemplate.expire(key, Duration.ofMinutes(appPropertyConfig.getFaceTokenTimeoutMinutes()));
            fd = new FaceDetectionEntity();
            fd.setSource("online");
            fd.setOrgId(sessionList.getOrgId());
            fd.setSession(sessionList);
            fd.setMobile(mobile);
            fd.setToken(token);
            fd.setFileId(Base64.getEncoder().encodeToString(file.getBytes()));
            fd.setServiceUser(sessionList.getLastServiceUser());
            fd.setCreateTime(new Date());
            fd.setUpdateTime(fd.getCreateTime());
            faceDetectionRepo.save(fd);
            String msg = "请点击以下链接进行身份认证<br><a href=\"" + appPropertyConfig.getFaceDetectionUrl() + "?token=" + token + "\">人脸验证</a>";
            com.alibaba.fastjson.JSONObject manual = new com.alibaba.fastjson.JSONObject().fluentPut("sessionKey", sessionKey).fluentPut("faceToken", token);
            models.add(
                    new SocketPacketModel<String>(sessionKey, null, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistoryService.createChatHistory(sessionList.getClientId(), sessionList.getClientTypeId(), "cs", "system", "text", null, null, sessionList.getOrgId(), sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(), msg, null, null, manual, true))));
            if (wechatMessageService.isWechat(sessionList.getClientId(), sessionList.getBotId())) {
                wechatMessageService.sendWechatTextMsg(sessionList.getClientId(), sessionList.getBotId(), msg);
            } else if (wechatCpService.isWorkWechat(sessionList.getClientId(), sessionList.getBotId())) {
                wechatCpService.sendWechatTextMsg(sessionList.getClientId(), sessionList.getBotId(), msg);
            }
            if (callback != null) {
                Integer count = faceDetectionRepo.countBySessionId(sessionList.getId());
                String message = null;
                if (count > 1) {
                    message = String.format("人脸验证请求下发成功，该会话已经成功发起%s次人脸验证请求", count);
                } else {
                    message = "人脸验证请求下发成功";
                }
                callback.sendAcknowledgement(new JSONObject().put("ret", 0).put("msg", message).put("timestamp", System.currentTimeMillis()));
            }
        } catch (Exception e) {
            LOGGER.warn("获取客户照片失败,url:{}", url, e);
            if (callback != null) {
                callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", "获取客户照片失败").put("timestamp", System.currentTimeMillis()));
            }
            return null;
        }
        return models;
    }

}
