package com.wolaidai.webot.connector.service.event.chat.inner;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.constant.ManualConfigConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.constant.SocketNamespace;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.model.SocketRoomModel;
import com.wolaidai.webot.connector.model.inner.SessionUpdModel;
import com.wolaidai.webot.connector.service.business.ChatRecordService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.data.mysql.entity.chat.ChatRecordEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ChatRoomEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ChatSessionEntity;
import com.wolaidai.webot.data.mysql.repo.ChatMembersSnapshotRepo;
import com.wolaidai.webot.data.mysql.repo.ChatRoomRepo;
import com.wolaidai.webot.data.mysql.repo.ChatSessionRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
@AllArgsConstructor
public class SessionUpdEventService extends BaseEventService {

    private final StringRedisTemplate csStringRedisTemplate;
    private final ChatRoomRepo chatRoomRepo;
    private final ChatSessionRepo chatSessionRepo;
    private final ChatRecordService chatRecordService;
    private final ChatMembersSnapshotRepo chatMembersSnapshotRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject)model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        SessionUpdModel sessionModel = JSON.parseObject(data.toString(), SessionUpdModel.class);
        if (ManualConfigConstants.WORKBENCH_TYPE.equals(sessionModel.getSource())) {
            Integer roomId = sessionModel.getRoomId();
            String roomName = sessionModel.getRoomName();
            if (roomId == null || StringUtils.isBlank(roomName)) {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret",-1)
                            .put("msg", "param lost").put("timestamp", System.currentTimeMillis()));
                }
                return null;
            }
            ChatRoomEntity chatRoomEntity = chatRoomRepo.findById(roomId).orElse(null);
            if (chatRoomEntity == null || ChatRoomEntity.INACTIVE_STATUS == chatRoomEntity.getStatus()) {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret",-1)
                            .put("msg", "该群聊已不存在").put("timestamp", System.currentTimeMillis()));
                }
                return null;
            }
            String email = sessionModel.getEmail();
            if (chatRoomEntity.getRoomUsers().stream().noneMatch(cru -> cru.getUser().getEmail().equals(email))) {
                if (callback != null) {
                    callback.sendAcknowledgement(new JSONObject().put("ret",-1)
                            .put("msg", "非群成员无法修改群名").put("timestamp", System.currentTimeMillis()));
                }
                return null;
            }
            chatRoomEntity.setName(roomName);
            chatRoomEntity.setUpdateTime(new Date());
            chatRoomRepo.save(chatRoomEntity);
            HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
            Integer orgId = chatRoomEntity.getOrgId();
            Integer sessionKey = sessionModel.getSessionKey();
            ChatSessionEntity sessionEntity = chatSessionRepo.findByIdAndOrgId(sessionKey, orgId);
            String eventMsg = String.format("%s %s修改群名为 ", ObjectUtils.defaultIfNull(hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, email), "nickName"), ""),
                    ObjectUtils.defaultIfNull(hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, email), "name"), "")).concat("\"").concat(roomName).concat("\"");
            models.add(new SocketPacketModel("session_" + sessionModel.getSessionKey(), null,
                    SocketEvent.INFO, MessageUtil.toJsonString(chatRecordService.createChatRecord(null, email, null,
                    eventMsg, ChatRecordEntity.EVENT_TYPE, sessionEntity, chatMembersSnapshotRepo.findFirstBySessionIdOrderByIdDesc(sessionKey),
                    null, new com.alibaba.fastjson.JSONObject().fluentPut("sessionKey", sessionKey)))));
            sessionEntity.setLastMsg(eventMsg);
            sessionEntity.setLastMsgSender(email);
            sessionEntity.setLastMsgTime(chatRoomEntity.getUpdateTime());
            chatSessionRepo.save(sessionEntity);
        }
        return models;
    }
}
