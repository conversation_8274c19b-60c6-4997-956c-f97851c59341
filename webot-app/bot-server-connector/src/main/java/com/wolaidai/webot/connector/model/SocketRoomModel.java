package com.wolaidai.webot.connector.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class SocketRoomModel extends BaseModel{
    public static final int ADD_TYPE = 1;
    public static final int REMOVE_TYPE = 2;
    private String namespace;
    private List<String> rooms;
    private List<String> socketIds;
    private int type;
    private String requestId;
}
