package com.wolaidai.webot.connector.service.event.chat;

import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.business.KeyExpireService;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class TransRemainingEventService extends BaseEventService {

    private final StringRedisTemplate csStringRedisTemplate;
    private final KeyExpireService keyExpireService;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        JSONObject data = (JSONObject) model.getData();
        JSONObject manual = data.getJSONObject("manual");
        if (manual != null) {
            int transId = manual.getInt("transId");
            //倒计时
            String transTimerKey = String.format(RedisKey.CS_SESSION_TRANS_KEY, transId);
            long remaining = 0;
            long expire = keyExpireService.getExpireTime(transTimerKey);
            if(expire>0) {
                expire = expire / 1000;
                remaining = expire > 60 ? 60 : expire;
            }
            if (model.getCallback() != null) {
                model.getCallback().sendAcknowledgement(new JSONObject().put("ret", 0)
                        .put("timestamp", System.currentTimeMillis()).put("manual", new JSONObject().put("remaining", remaining)));
            }
        }
        return null;
    }
}
