package com.wolaidai.webot.connector.exception;

import com.wolaidai.webot.connector.constant.WebStatusConstants;
import com.wolaidai.webot.connector.model.ResponseModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.ServletWebRequest;

import javax.servlet.RequestDispatcher;

@RestController
public class CommonErrorController implements ErrorController {
    private static final Logger LOGGER = LoggerFactory.getLogger(CommonErrorController.class);

    @RequestMapping("/error")
    public ResponseEntity<Object> handleError(ServletWebRequest request) {
        Object status = request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE, RequestAttributes.SCOPE_REQUEST);
        if (status != null) {
            Integer statusCode = Integer.valueOf(status.toString());
            LOGGER.error("请求异常,url:{},status:{}", request.getAttribute(RequestDispatcher.FORWARD_SERVLET_PATH, RequestAttributes.SCOPE_REQUEST), statusCode, request.getAttribute(RequestDispatcher.ERROR_EXCEPTION, RequestAttributes.SCOPE_REQUEST));
            if (statusCode == HttpStatus.NOT_FOUND.value()) {
                return new ResponseEntity(new ResponseModel(WebStatusConstants.RESPONSE_CODE_RESOURCE_NOT_EXIST, "路径错误"), HttpStatus.NOT_FOUND);
            } else {
                return new ResponseEntity(new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "服务异常"), HttpStatus.valueOf(statusCode));
            }
        }
        LOGGER.error("服务状态码为空,url:{}", request.getAttribute(RequestDispatcher.FORWARD_SERVLET_PATH, RequestAttributes.SCOPE_REQUEST));
        return new ResponseEntity(new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "服务异常"), HttpStatus.OK);
    }
}
