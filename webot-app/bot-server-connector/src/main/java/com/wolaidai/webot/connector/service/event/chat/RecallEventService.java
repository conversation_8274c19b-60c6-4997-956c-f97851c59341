package com.wolaidai.webot.connector.service.event.chat;

import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.data.mongodb.repo.ComplexChatHistoryRepo;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class RecallEventService extends BaseEventService {

    private final SessionListRepo sessionListRepo;

    private final ComplexChatHistoryRepo complexChatHistoryRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject)model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        String msgId = data.getString("msgId");
        JSONObject manual = data.getJSONObject("manual");
        String sessionKey = manual.getString("sessionKey");
        SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
        if(sessionList!=null) {
            complexChatHistoryRepo.updateRecallByClientIdAndMsgId(sessionList.getClientId(),msgId);
            models.add(new SocketPacketModel(sessionKey,model.getSocketId(), SocketEvent.EVENT, new JSONObject().put("type",SocketEvent.RECALL_TYPE).put("msgId",msgId).toString()));
            if(callback!=null){
                callback.sendAcknowledgement(new JSONObject().put("ret",0).put("timestamp",System.currentTimeMillis()));
            }
        }
        return models;
    }
}
