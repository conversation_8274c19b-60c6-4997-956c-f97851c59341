package com.wolaidai.webot.connector.service.event.chat;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.event.BaseEventService;
import com.wolaidai.webot.data.mongodb.repo.ComplexChatHistoryRepo;
import com.wolaidai.webot.data.mysql.entity.chat.SatisfactionDataEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.repo.SatisfactionDataRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import io.socket.socketio.server.SocketIoSocket;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@AllArgsConstructor
public class SatisfactionCommitEventService extends BaseEventService {

    private final SessionListRepo sessionListRepo;
    private final SatisfactionDataRepo satisfactionDataRepo;
    private final ComplexChatHistoryRepo complexChatHistoryRepo;

    @Override
    public List<SocketPacketModel> process(MessageModel model) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject) model.getData();
        SocketIoSocket.ReceivedByLocalAcknowledgementCallback callback = model.getCallback();
        JSONObject manual = data.getJSONObject("manual");
        if(manual!=null){
            int level = manual.getInt("level");
            String sessionKey = manual.getString("sessionKey");
            SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
            if(sessionList!=null){
                SatisfactionDataEntity satisfactionData = sessionList.getSatisfactionData();
                if(satisfactionData!=null){
                    if(satisfactionData.getStatus()==SatisfactionDataEntity.STATUS_TREATED){
                        if(callback!=null){
                            callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", "您已对当前服务进行过评价").put("timestamp", System.currentTimeMillis()));
                        }
                    }if(DateUtils.addMinutes(satisfactionData.getCreateTime(),30).before(new Date())){
                        if(callback!=null){
                            callback.sendAcknowledgement(new JSONObject().put("ret", -1).put("msg", "已超过评价时间").put("timestamp", System.currentTimeMillis()));
                        }
                    }else{
                        JSONArray labels = manual.optJSONArray("labels");
                        if (null != labels) {
                            satisfactionData.setLabels(JSON.parseArray(labels.toString()));
                        }
                        satisfactionData.setContent(manual.optString("content"));
                        satisfactionData.setStatus(SatisfactionDataEntity.STATUS_TREATED);
                        satisfactionData.setUpdateTime(new Date());
                        satisfactionData.setLevel(level);
                        satisfactionDataRepo.save(satisfactionData);
                        sessionListRepo.updateSatisfactionLevelById(sessionList.getId(), level);
                        complexChatHistoryRepo.updateSatisfactionBySessionKey(sessionKey, level, satisfactionData.getLabels(), satisfactionData.getContent());
                        models.add(new SocketPacketModel(sessionKey, model.getSocketId(), SocketEvent.EVENT,
                                new JSONObject().put("type","satisfaction:commit").put("content","客户已评价")
                                        .put("manual",new JSONObject().put("sessionKey",sessionKey).put("appraiseLevel",satisfactionData.getLevel()).put("appraiseStatus",satisfactionData.getStatus())).toString()));
                        if(callback!=null){
                            callback.sendAcknowledgement(new JSONObject().put("ret", 0).put("msg", "评价成功").put("timestamp", System.currentTimeMillis()));
                        }
                    }
                }
            }
        }
        return models;
    }
}
