package com.wolaidai.webot.connector.service.business;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.connector.constant.ManualConfigConstants;
import com.wolaidai.webot.connector.constant.RedisConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.constant.SocketNamespace;
import com.wolaidai.webot.connector.entity.Event;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.BaseService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.connector.wechat.service.WechatCpService;
import com.wolaidai.webot.connector.wechat.service.WechatMessageService;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.entity.ConversationEntity;
import com.wolaidai.webot.data.mongodb.repo.ConversationRepo;
import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionTransferListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.repo.BotRepo;
import com.wolaidai.webot.data.mysql.repo.QueueListRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.mysql.repo.SessionTransferListRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@AllArgsConstructor
public class ExpireProcessService extends BaseService{
    private final RedissonClient redissonClient;
    private final StringRedisTemplate csStringRedisTemplate;
    private final ManualChatService manualChatService;
    private final ChatHistoryService chatHistoryService;
    private final ConversationRepo conversationRepo;
    private final BotRepo botRepo;
    private final SessionListRepo sessionListRepo;
    private final RedisTemplate csRedisTemplate;
    private final SessionTransferListRepo sessionTransferListRepo;
    private final UserStateRepo userStateRepo;
    private final WechatMessageService wechatMessageService;
    private final WechatCpService wechatCpService;
    private final QueueListRepo queueListRepo;
    private final KeyExpireService keyExpireService;
    private final SessionListService sessionListService;

    @Async
    public void processKeyExpireEvent(String key) {
        RLock lock = redissonClient.getLock(String.format(RedisKey.LOCK_KEY, "expire:" + key));
        try {
            boolean res = lock.tryLock(0, 3, TimeUnit.SECONDS);
            if (res) {
                LOGGER.info("expire key:{}", key);
                long start = System.currentTimeMillis();
                if (key.startsWith(StringUtils.substringBeforeLast(RedisKey.BOT_CONVERSATION_KEY, ":"))) {
                    String cid = StringUtils.substringAfterLast(key, ":");
                    ConversationEntity conversation = conversationRepo.findByCid(cid);
                    if(conversation!=null){
                        if(!Objects.equals(conversation.getStatus(),ConversationEntity.END_STATUS)){
                            ConversationEntity conversationEntity = conversationRepo.findByCid(cid);
                            if(conversationEntity!=null){
                                String gcid = conversationEntity.getGcid();
                                List<QueueListEntity> queueListEntities = queueListRepo.findByOrgIdAndGcidAndStatus(conversationEntity.getOrgId(), gcid, QueueListEntity.INQUEUE_STATUS);
                                //如果有排队的会话，将该机器会话延长10分钟，如果无排队会话，删除缓存中的全局会话
                                if(queueListEntities != null && queueListEntities.size() > 0){
                                    keyExpireService.setExpireKey(String.format(RedisKey.BOT_CONVERSATION_KEY, conversationEntity.getCid()), 10, TimeUnit.MINUTES);
                                } else {
                                    botRepo.findById(conversation.getBotId()).ifPresent(v -> {
                                        csStringRedisTemplate.delete(String.format(RedisKey.GLOBAL_CONVERSATION_KEY, conversation.getClientId(), v.getId()));
                                    });
                                    if(Objects.equals(conversation.getStatus(),ConversationEntity.START_STATUS)){
                                        conversation.setStatus(ConversationEntity.END_STATUS);
                                        conversation.setEndTime(new Date());
                                        conversationRepo.save(conversation);
                                    }
                                }
                            }
                        }
                    }
                } else if (key.startsWith(StringUtils.substringBeforeLast(RedisKey.CS_FIRSTRESP_SESSION_KEY, ":"))) {
                    String sessionKey = StringUtils.substringAfterLast(key, ":");
                    sessionListRepo.updateFirstRespTimeoutBySessionKey(sessionKey,1,new Date());
                    csStringRedisTemplate.opsForHash().put(String.format(RedisKey.CS_RESP_DETAIL, sessionKey),"firstReplyTimeout","1");
                    csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), Arrays.asList(new SocketPacketModel(sessionKey, null, SocketEvent.EVENT, new JSONObject().put("type","respTimeout:firstReply").put("sessionKey",sessionKey).toString())));
                } else if (key.startsWith(StringUtils.substringBeforeLast(RedisKey.CS_SESSIONRESP_SESSION_KEY, ":"))) {
                    String sessionKey = StringUtils.substringAfterLast(key, ":");
                    sessionListRepo.updateRespTimeoutBySessionKey(sessionKey,1,new Date());
                    csStringRedisTemplate.opsForHash().put(String.format(RedisKey.CS_RESP_DETAIL, sessionKey),"sessionReplyTimeout","1");
                    csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), Arrays.asList(new SocketPacketModel(sessionKey, null, SocketEvent.EVENT, new JSONObject().put("type","respTimeout:sessionReply").put("sessionKey",sessionKey).toString())));
                } else if (key.startsWith(StringUtils.substringBeforeLast(RedisKey.CS_LAST_REPLY_SESSION_KEY, ":"))) {
                    String sessionKey = StringUtils.substringAfterLast(key, ":");
                    SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
                    if (sessionList != null && sessionList.getStatus() != SessionListEntity.STATUS_OFFLINE) {
                        if (Objects.equals(sessionList.getLastMsgSender(), SessionListEntity.USER_SENDER)) {
                            JSONObject serviceTimeoutMsg = manualChatService.getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_SERVICE_TIMEOUT_MSG);
                            if (serviceTimeoutMsg != null && serviceTimeoutMsg.getBoolean("status")) {
                                sendSystemTimeoutReply(sessionList, serviceTimeoutMsg);
                            }
                        } else {
                            JSONObject customerTimeoutMsg = manualChatService.getAutoReplyConfig(sessionList.getOrgId(), sessionList.getClientTypeId(), ManualConfigConstants.AUTO_REPLY_CUSTOMER_TIMEOUT_MSG);
                            if (customerTimeoutMsg != null && customerTimeoutMsg.getBoolean("status")) {
                                sendSystemTimeoutReply(sessionList, customerTimeoutMsg);
                            }
                        }
                    }
                } else if (key.startsWith(StringUtils.substringBeforeLast(RedisKey.CS_OFFLINE_SESSION_KEY, ":"))) {
                    String sessionKey = StringUtils.substringAfterLast(key, ":");
                    SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
                    if (sessionList != null && !SessionListEntity.STATUS_OFFLINE.equals(sessionList.getStatus())) {
                        ZSetOperations<String, String> zSetOperations = csStringRedisTemplate.opsForZSet();
                        zSetOperations.remove(RedisKey.EXPIRE_KEY_ZSET, String.format(RedisKey.CS_LAST_REPLY_SESSION_KEY, sessionKey));
                        if (Objects.equals(sessionList.getLastMsgSender(), SessionListEntity.USER_SENDER)) {
                            manualChatService.offlineSession(sessionKey, SessionListEntity.CLOSE_TYPE_CS_TIMEOUT);
                        } else {
                            manualChatService.offlineSession(sessionKey, SessionListEntity.CLOSE_TYPE_CUSTOMER_TIMEOUT);
                        }
                    }
                } else if (key.startsWith(StringUtils.substring(RedisKey.CS_USER_ONLINE_EMAIL_KEY, 0, -5))) {
                    String onlineData = StringUtils.substringAfter(key, "orgId:");
                    if (StringUtils.isNotBlank(onlineData)) {
                        String[] split = StringUtils.split(onlineData, ":");
                        if (split.length == 2) {
                            String orgId = split[0];
                            String email = split[1];
                            csStringRedisTemplate.opsForList().rightPush(RedisKey.EVENT_CLIENT_KEY, JSON.toJSONString(new Event(UUID.randomUUID().toString(), Event.SERVICE_OFFLINE_KEY, Integer.parseInt(orgId),
                                    JSON.parseObject(new JSONObject().put("email", email).toString()), new Date())));
                        }
                    }
                } else if (key.startsWith(StringUtils.substringBeforeLast(RedisKey.CS_SESSION_TRANS_KEY, ":"))) {
                    String transId = StringUtils.substringAfterLast(key, ":");
                    Optional<SessionTransferListEntity> transferListOp = sessionTransferListRepo.findById(Integer.valueOf(transId));
                    transferListOp.ifPresent(entity -> {
                        Integer orgId = entity.getOrgId();
                        String fromServiceUser = entity.getFromServiceUser();
                        csStringRedisTemplate.opsForList().rightPush(RedisKey.EVENT_CLIENT_KEY,
                                JSON.toJSONString(new Event(UUID.randomUUID().toString(), Event.TRANS_TIMEOUT_KEY, orgId,
                                        JSON.parseObject(new JSONObject().put("transId", transId).toString()), new Date())));
                        //通知发起人
                        HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
                        String fromSocketId = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, orgId, fromServiceUser), "socketId");
                        if (StringUtils.isNotBlank(fromSocketId)) {
                            String fromName = "", toName = "";
                            UserStateEntity fromUser = userStateRepo.findByOrgIdAndEmail(orgId, fromServiceUser);
                            if (fromUser != null) {
                                fromName = fromUser.getNickName();
                            }
                            UserStateEntity toUser = userStateRepo.findByOrgIdAndEmail(orgId, entity.getToServiceUser());
                            if (toUser != null) {
                                toName = toUser.getNickName();
                            }
                            SessionListEntity session = entity.getSession();
                            sessionListService.decryptCustomerInfo(session);
                            csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT),
                                    Collections.singletonList(new SocketPacketModel(null, fromSocketId, SocketEvent.EVENT, new com.alibaba.fastjson.JSONObject()
                                            .fluentPut("type", SocketEvent.SESSION_TRANS_RESULT_TYPE).fluentPut("manual",
                                                    new com.alibaba.fastjson.JSONObject().fluentPut("sessionKey", session.getSessionKey()).fluentPut("result", "timeout")
                                                            .fluentPut("transId", transId).fluentPut("from", fromServiceUser).fluentPut("fromName", fromName)
                                                            .fluentPut("to", entity.getToServiceUser()).fluentPut("toName", toName).fluentPut("remark", entity.getRemark())
                                                            .fluentPut("customerName", session.getCustomerName()).fluentPut("customerDetail", session.getCustomerDetail())
                                                            .fluentPut("businessName", session.getBusinessName())).toJSONString())));
                        }
                    });
                } else if(key.startsWith(StringUtils.substringBeforeLast(RedisKey.CS_CLIENT_STATUS_TIMEOUT, ":"))){
                    String sessionKey = StringUtils.substringAfterLast(key, ":");
                    csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT),
                            Collections.singletonList(new SocketPacketModel(sessionKey, null, SocketEvent.EVENT, new com.alibaba.fastjson.JSONObject()
                                    .fluentPut("type", SocketEvent.CLIENT_STATUS_TYPE).fluentPut("manual",
                                            new com.alibaba.fastjson.JSONObject().fluentPut("sessionKey", sessionKey).fluentPut("status",0)).toJSONString())));
                }
                LOGGER.info("expire key:{},cost:{}ms", key, System.currentTimeMillis()-start);
            }
        } catch (Exception e) {
            LOGGER.error("redis key expire error",e);
        }
    }

    private void sendSystemTimeoutReply(SessionListEntity sessionList, JSONObject customerTimeoutMsg) {
        Map<String, Object> manual = new HashMap<>();
        manual.put("sessionKey", sessionList.getSessionKey());
        ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(sessionList.getClientId(), sessionList.getClientTypeId(), "cs", "system", "text", null, null,
                sessionList.getOrgId(), sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(), customerTimeoutMsg.getString("content"), null, null, manual, true);
        sessionList.setLastMsg(MessageUtil.escapeHtml(chatHistory.getContent(), chatHistory.getType()));
        sessionList.setLastMsgTime(chatHistory.getDate());
        sessionList.setUpdateTime(new Date());
        sessionListRepo.save(sessionList);
        if(wechatMessageService.isWechat(sessionList.getClientId(), sessionList.getBotId())){
            wechatMessageService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),chatHistory.getContent());
        }else if(wechatCpService.isWorkWechat(sessionList.getClientId(), sessionList.getBotId())){
            wechatCpService.sendWechatTextMsg(sessionList.getClientId(),sessionList.getBotId(),chatHistory.getContent());
        }
        csRedisTemplate.convertAndSend(String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT), Arrays.asList(new SocketPacketModel(sessionList.getSessionKey(), null, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory))));
    }
}
