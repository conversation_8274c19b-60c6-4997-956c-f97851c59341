package com.wolaidai.webot.connector.model;

import com.wolaidai.webot.data.mysql.entity.feedback.FeedbackCategoryEntity;
import lombok.Data;

@Data
public class FeedbackCategorySimpleRowModel extends BaseModel {
    private Integer id;
    private String content;

    public FeedbackCategorySimpleRowModel(FeedbackCategoryEntity f) {
        this.id = f.getId();
        this.content = f.getContent();
    }
}
