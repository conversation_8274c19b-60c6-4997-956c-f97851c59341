package com.wolaidai.webot.connector.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.connector.constant.WebStatusConstants;
import com.wolaidai.webot.connector.model.*;
import com.wolaidai.webot.connector.service.business.ApiService;
import com.wolaidai.webot.connector.service.business.ManualChatService;
import com.wolaidai.webot.connector.service.worktime.BotWorkTimeService;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.entity.TestChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.repo.ChatHistoryRepo;
import com.wolaidai.webot.data.mongodb.repo.ComplexTestChatHistoryRepo;
import com.wolaidai.webot.data.mysql.entity.bot.*;
import com.wolaidai.webot.data.mysql.entity.docqa.DocQaFileEntity;
import com.wolaidai.webot.data.mysql.entity.docqa.DocQaReleaseEntity;
import com.wolaidai.webot.data.mysql.repo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.xml.datatype.DatatypeConfigurationException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@AllArgsConstructor
@RestController
@Api(tags = "聊天窗口支持相关")
public class ChatSupportController extends BaseController {

    private final SkillGroupRepo skillGroupRepo;
    private final QaRepo qaRepo;
    private final RecommendQaRepo recommendQaRepo;
    private final NoticeRepo noticeRepo;
    private final BotRepo botRepo;
    private final ChatHistoryRepo chatHistoryRepo;
    private final ComplexTestChatHistoryRepo complexTestChatHistoryRepo;
    private final MaterialRepo materialRepo;
    private final DocQaFileRepo docQaFileRepo;
    private final DocQaReleaseRepo docQaReleaseRepo;
    private final ApiService apiService;
    private final BotWorkTimeService botWorkTimeService;
    private final ManualChatService manualChatService;
    private final ExecutorService executorService = Executors.newFixedThreadPool(8);
    
    public static final List<String> EXCLUDE_RECOMMEND_QUESTIONS = Collections.unmodifiableList(Arrays.asList("注册用户","逾期用户","债转用户","会员业务"));

    @GetMapping("/skillGroup")
    @ApiOperation(value = "获取技能组")
    public ResponseModel getSkillGroups(String accessKey) {
        if (StringUtil.isBlank(accessKey)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到机器人");
        }
        List<SkillGroupEntity> list = skillGroupRepo.findByAccessKey(accessKey);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", list.stream().map(i -> new SkillGroupRowModel(i)).collect(Collectors.toList()));
    }

    @GetMapping("/qa")
    @ApiOperation(value = "获取所有问题")
    public ResponseModel getQuestions(String accessKey,Integer skillGroupId) {
        if (StringUtil.isBlank(accessKey) || null == skillGroupId || skillGroupId < 1) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到机器人");
        }
        List<QaEntity> list = qaRepo.findByAccessKeyAndSkillGroupId(accessKey, skillGroupId);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", list.stream().map(i -> new QuestionRowModel(i)).collect(Collectors.toList()));
    }

    @GetMapping("/hot")
    @ApiOperation(value = "获取推荐问题")
    public ResponseModel getHotQAs(Integer skillGroupId) {
        if (null == skillGroupId || skillGroupId < 1) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到技能组");
        }
        List<RecommendQaEntity> list = recommendQaRepo.findBySkillGroupId(skillGroupId);
        List<RecommendQuestionRowModel> result = new ArrayList<>();
        if (list.size() > 0) {
            JSONObject json = list.get(0).getQuestion();
            JSONArray categories = null;
            JSONObject details = null;
            if (null != json && null != (categories = json.getJSONArray("categories")) && null != (details = json.getJSONObject("details"))) {
                final JSONObject d = details;
                result = categories.stream().filter(i -> !EXCLUDE_RECOMMEND_QUESTIONS.contains((String) i)).map(i -> new RecommendQuestionRowModel((String) i, d.getJSONArray((String) i))).collect(Collectors.toList());
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", result);
    }

    @GetMapping("/notice")
    @ApiOperation(value = "获取公告")
    public ResponseModel getNotice(Integer skillGroupId) {
        if (null == skillGroupId || skillGroupId < 1) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到技能组");
        }
        NoticeEntity n = noticeRepo.findBySkillGroupId(skillGroupId);
        NoticeResultModel result = null;
        if (null != n) {
            result = new NoticeResultModel(n);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", result);
    }

    @GetMapping("/worktime")
    @ApiOperation(value = "判断是否工作时间")
    public ResponseModel getWorktime(String accessKey) throws DatatypeConfigurationException {
        if (StringUtil.isNotBlank(accessKey)) {
            BotEntity bot = botRepo.findByCode(accessKey);
            if (null != bot) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", botWorkTimeService.isWorkTime(bot.getOrgId()));
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到机器人");
    }

    @GetMapping("/history")
    @ApiOperation(value = "获取聊天记录")
    public ResponseModel getHistories(GetHistoriesModel model) {
        BotEntity bot = null;
        if (StringUtil.isBlank(model.getAccessKey()) || null == (bot = botRepo.findByCode(model.getAccessKey()))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到机器人");
        }
        Date date = null != model.getDate() ? new Date(model.getDate()) : new Date();
        List<ChatHistoryEntity> list = null;
        List<String> excludeEventKeys = Arrays.asList("biz:switch", "verify");
        if (StringUtil.isNotBlank(model.getAccount())) {
            list = chatHistoryRepo.findByContentNotAndTypeNotAndEventKeyNotInAndRecallNotAndDateLessThanAndBotIdAndAccountOrderByDateDesc("SWITCH_SKILLGROUP", "feedback", excludeEventKeys, true, date, bot.getId(), model.getAccount(), PageRequest.ofSize(10));
        } else {
            list = chatHistoryRepo.findByContentNotAndTypeNotAndEventKeyNotInAndRecallNotAndDateLessThanAndBotIdAndClientIdOrderByDateDesc("SWITCH_SKILLGROUP","feedback", excludeEventKeys, true, date, bot.getId(), model.getClientId(), PageRequest.ofSize(10));
        }
        List<ChatHistoryRowModel> result = new ArrayList<>();
        for (ChatHistoryEntity c : list) {
            ChatHistoryRowModel row = new ChatHistoryRowModel(c);
            if ("voice".equals(c.getType()) && "bot".equals(c.getSender()) && (null == c.getMedia() || c.getMedia().size() == 0)) {
                MaterialEntity m = materialRepo.findByFileId(c.getContent());
                if (null != m) {
                    row.getMedia().put("duration", Math.round(m.getDuration() / 1000f));
                }
            }
            result.add(row);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", result);
    }

    @GetMapping("/test/history")
    @ApiOperation(value = "获取测试聊天记录")
    public ResponseModel getTestHistories(GetTestHistoriesModel model) {
        Date date = null != model.getDate() ? new Date(model.getDate()) : new Date();
        List<TestChatHistoryEntity> list = complexTestChatHistoryRepo.findByAccountAndDateBefore(model.getAccount(),model.getBotId(),model.getSkillGroupId(),model.getSkillId(),date,PageRequest.ofSize(10).withSort(Sort.Direction.DESC, "date"));
        List<TestChatHistoryRowModel> result = list.stream().map(v->new TestChatHistoryRowModel(v)).collect(Collectors.toList());
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", result);
    }

    @GetMapping("/test/reset")
    @ApiOperation(value = "测试重置机器人对话")
    public ResponseModel resetBot(Integer botId,String clientId) {
        apiService.resetBot(botId, clientId);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "success");
    }

    @GetMapping("/config")
    @ApiOperation(value = "获取聊天窗口菜单配置")
    public ResponseModel getConfig(String accessKey) {
        BotEntity bot = null;
        if (StringUtil.isBlank(accessKey) || null == (bot = botRepo.findByCode(accessKey))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到机器人");
        }
        org.json.JSONObject config = manualChatService.getBotMenuListConfig(bot.getOrgId(), accessKey);
        if (null==config) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到菜单配置");
        }
        List<SkillGroupEntity> list = skillGroupRepo.findByAccessKey(accessKey);
        JSONObject json = JSON.parseObject(config.toString());
        boolean lessSkillGroup = list.size() < 2;
        boolean isWorkTime = botWorkTimeService.isWorkTime(bot.getOrgId());
        if (lessSkillGroup || !isWorkTime) {
            JSONArray menuList = json.getJSONArray("menuList");
            for (int i = 0; i < menuList.size(); i++) {
                JSONObject item = menuList.getJSONObject(i);
                String code = item.getString("code");
                if ("business".equals(code)) {
                    item.put("isShow", !lessSkillGroup);
                } else if ("artificial".equals(code) || "tel".equals(code)) {
                    item.put("isShow", isWorkTime);
                }
            }
            json.put("menuList", menuList);
        }
        json.put("skillGroups", list.stream().map(i -> new SkillGroupRowModel(i)).collect(Collectors.toList()));
        //文档问答机器人需提供标签列表供用户选择
        if(bot.getType() == 2) {
            List<Integer> docSkillIdList = new ArrayList<>();
            Set<String> labelSet = new HashSet<>();
            bot.getBotSkillGroups().forEach(v -> {
                v.getSkillGroup().getSkillSkillGroups().forEach(t -> docSkillIdList.add(t.getSkill().getId()));});
            if(docSkillIdList.size() == 1) {
                DocQaReleaseEntity releaseEntity = docQaReleaseRepo.findFirstByOrgIdAndSkillIdAndStatusOrderByCreateTimeDesc(bot.getOrgId(), docSkillIdList.get(0), DocQaReleaseEntity.SUCCESS_STATUS);
                if(releaseEntity != null && releaseEntity.getAllDoc() != null && releaseEntity.getAllDoc().size() > 0) {
                    JSONArray IdArray = releaseEntity.getAllDoc();
                    List<Integer> docIdList = IdArray.toJavaList(Integer.class);
                    Iterable<DocQaFileEntity> fileList =  docQaFileRepo.findAllById(docIdList);
                    fileList.forEach(v -> {
                        if(v.getLabel() != null && v.getLabel().size() > 0) {
                            List<String> labelList = v.getLabel().toJavaList(String.class);
                            labelSet.addAll(labelList);
                        }
                    });
                }
            }
            if(labelSet.size() > 0) {
                List<String> labelList = new ArrayList<>(labelSet);
                Collections.sort(labelList);
                json.put("docLabels", labelList);
            }
        }

        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", json);
    }

    private Future<JSONObject> getReply(Integer skillId, String content) {
        return executorService.submit(() -> {
            org.json.JSONObject r = apiService.getReply(null, null, null, null, skillId, null, null, "text", content, null, null,true);
            org.json.JSONArray reply = r.optJSONArray("reply");
            if (null != reply && reply.length() > 0) {
                org.json.JSONObject jo = reply.getJSONObject(0);
                String type = "text";
                String c = jo.optString("content");
                MaterialEntity m = null;
                if (jo.has("material_id") && null != (m = materialRepo.findById(jo.optInt("material_id")).orElse(null))) {
                    type = Objects.equals(m.getType(), 1) ? "image" : "voice";
                    c = new StringBuilder(appPropertyConfig.getFileServerUrl()).append("/2/").append(m.getFileId()).toString();
                }
                return new JSONObject().fluentPut("type", type).fluentPut("content", c);
            }
            return null;
        });
    }

    @PostMapping("/qaReply/check")
    @ApiOperation(value = "测试回复")
    public ResponseModel testReply(@RequestBody @Valid TestReplyModel model) throws Exception {
        Integer skillId = model.getSkillId();
        if (null == skillId) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "技能id不能为空");
        }
        ArrayList<Future<JSONObject>> list = new ArrayList<>();
        Object content = model.getContent();
        if (content instanceof Collection) {
            JSONArray arr = (JSONArray) JSON.toJSON(content);
            for (int i = 0; i < arr.size(); i++) {
                list.add(getReply(skillId, arr.getString(i)));
            }
        } else if (content instanceof String) {
            list.add(getReply(skillId, (String) content));
        }
        if (list.size() == 1) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "success", list.get(0).get());
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "success", list.stream().map(i -> {
            try {
                return i.get();
            } catch (Exception e) {
            }
            return null;
        }).collect(Collectors.toList()));
    }

}
