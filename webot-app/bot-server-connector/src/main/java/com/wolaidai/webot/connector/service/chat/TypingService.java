package com.wolaidai.webot.connector.service.chat;

import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.DataProcessService;
import com.wolaidai.webot.connector.utils.CommonUtil;
import com.wolaidai.webot.data.mysql.entity.bot.BotEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.repo.BotRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class TypingService extends DataProcessService {
    private final StringRedisTemplate csStringRedisTemplate;

    private final BotRepo botRepo;

    private final SessionListRepo sessionListRepo;

    @Override
    protected List<SocketPacketModel> processData(MessageModel messageModel) {
        List<SocketPacketModel> models = new ArrayList<>();
        JSONObject data = (JSONObject) messageModel.getData();
        String clientId = data.getString("clientId");
        String accessKey = data.getString("accessKey");
        String content = data.getString("content");
        HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
        BotEntity bot = botRepo.findByCode(accessKey);
        if (bot == null) return null;
        String conversationKey = String.format(RedisKey.GLOBAL_CONVERSATION_KEY, clientId, bot.getId());
        String sessionKey = hashOperations.get(conversationKey, "sessionKey");
        if (sessionKey != null) {
            SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
            if (sessionList != null) {
                Map<String, String> onlineEmailMap = hashOperations.entries(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, sessionList.getOrgId(), sessionList.getLastServiceUser()));
                if (!onlineEmailMap.isEmpty() && sessionKey.equals(onlineEmailMap.get("currentSessionKey"))) {
                    String socketId = onlineEmailMap.get("socketId");
                    if (socketId != null) {
                        //处理用户输入的敏感信息
                        content = CommonUtil.processUserSensitiveInfo(content, false);
                        models.add(new SocketPacketModel(null, socketId, messageModel.getEvent(), new JSONObject().put("clientId", clientId).put("content", content)
                                .put("ip", messageModel.getIp()).put("ua", messageModel.getUa())
                                .put("manual", new JSONObject().put("email", sessionList.getLastServiceUser()).put("sessionKey", sessionKey)).toString()));
                    }
                }

            }
        }

        return models;
    }
}
