package com.wolaidai.webot.connector.model;

import com.wolaidai.webot.data.mongodb.entity.TestChatHistoryEntity;
import lombok.Data;

import java.util.Date;
import java.util.Map;
@Data
public class TestChatHistoryRowModel extends BaseModel {
    private String account;
    private String sender;
    private String type;
    private String content;
    private String msgId;
    private Date date;
    private Map<String, Object> extra;
    private Map<String, Object> menu;

    private Map<String, Object> media;

    public TestChatHistoryRowModel(TestChatHistoryEntity c) {
        this.account = c.getAccount();
        this.sender = c.getSender();
        this.type = c.getType();
        this.content = c.getContent();
        this.msgId = c.getMsgId();
        this.date = c.getDate();
        this.menu = c.getMenu();
        this.extra = c.getExtra();
        this.menu = c.getMenu();
        this.media = c.getMedia();
    }
}
