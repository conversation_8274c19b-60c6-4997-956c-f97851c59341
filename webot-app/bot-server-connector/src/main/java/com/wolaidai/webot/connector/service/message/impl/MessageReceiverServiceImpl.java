package com.wolaidai.webot.connector.service.message.impl;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.connector.entity.Event;
import com.wolaidai.webot.connector.entity.EventProcessResult;
import com.wolaidai.webot.connector.service.message.MessageProcessService;
import com.wolaidai.webot.connector.service.message.MessageReceiverService;
import com.wolaidai.webot.data.mysql.entity.event.MessageEventEntity;
import com.wolaidai.webot.data.mysql.repo.MessageEventRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service
@AllArgsConstructor
public class MessageReceiverServiceImpl implements MessageReceiverService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final StringRedisTemplate csStringRedisTemplate;

    private final MessageEventRepo messageEventRepo;

    private final MessageProcessService messageProcessService;

    private final ThreadPoolTaskExecutor threadPool;

    @Override
    @Async
    public void getMsg() {
        String msg;
        while(true){
            try {
                msg = csStringRedisTemplate.opsForList().leftPop(RedisKey.EVENT_SERVER_KEY, 10, TimeUnit.SECONDS);
                if(msg==null){
                    continue;
                }
                Event event = JSONObject.parseObject(msg, Event.class);
                MessageEventEntity messageEvent = new MessageEventEntity();
                messageEvent.setEventId(event.getEventId());
                messageEvent.setContent(event.getContent());
                messageEvent.setOrgId(event.getOrgId());
                messageEvent.setEventKey(event.getEventKey());
                messageEvent.setEventTime(event.getEventTime());
                messageEvent.setCreateTime(new Date());
                messageEventRepo.save(messageEvent);
                threadPool.execute(()->{
                    long start = System.currentTimeMillis();
                    LOGGER.info("开始执行消息处理,eventId:{},eventKey:{},activeCount:{},queueCount:{}",messageEvent.getEventId(),messageEvent.getEventKey(),threadPool.getActiveCount(),threadPool.getThreadPoolExecutor().getQueue().size());
                    EventProcessResult result = messageProcessService.process(event);
                    messageEvent.setStatus(MessageEventEntity.FINISH_STATUS);
                    if(!result.isSuccess()) {
                        messageEvent.setErrorMsg(result.getErrorMsg());
                    }
                    messageEvent.setUpdateTime(new Date());
                    long cost = System.currentTimeMillis()-start;
                    messageEvent.setCost(cost);
                    messageEvent.setDuration(messageEvent.getUpdateTime().getTime()-messageEvent.getCreateTime().getTime());
                    messageEventRepo.save(messageEvent);
                    LOGGER.info("结束执行消息处理,eventId:{},eventKey:{},cost:{}ms",messageEvent.getEventId(),messageEvent.getEventKey(),cost);
                });
            }catch (Exception e){
                LOGGER.error("获取消息异常",e);
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException ex) {
                }
            }
        }
    }

}
