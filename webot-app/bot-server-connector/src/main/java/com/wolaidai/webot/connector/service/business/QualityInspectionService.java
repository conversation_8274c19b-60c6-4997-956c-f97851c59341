package com.wolaidai.webot.connector.service.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.utils.CommonUtil;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.repo.ChatHistoryRepo;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class QualityInspectionService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final AppPropertyConfig appPropertyConfig;
    private final SessionListRepo sessionListRepo;
    private final ChatHistoryRepo chatHistoryRepo;

    /**
     * 异步提交质量检查
     */
    @Async("threadPool")
    @Transactional
    public void submitQualityInspectionAsync(SessionListEntity sessionList) {
        try {
            if (!appPropertyConfig.isQualityInspectionEnabled()) {
                LOGGER.debug("质量检查功能未启用, sessionId:{}", sessionList.getId());
                return;
            }

            if (StringUtils.isBlank(appPropertyConfig.getQualityInspectionSubmitUrl())) {
                LOGGER.error("质量检查提交URL未配置, sessionId:{}", sessionList.getId());
                return;
            }

            // 获取对话历史
            List<ChatHistoryEntity> chatHistoryList = getChatHistoryForInspection(sessionList.getSessionKey());
            if (CollectionUtils.isEmpty(chatHistoryList)) {
                LOGGER.info("质量检查对话历史为空, sessionId:{}", sessionList.getId());
                return;
            }

            // 检查是否有有效的用户和客服对话
            long userMsgCount = chatHistoryList.stream().filter(v -> v.getSender().equals("user")).count();
            long manualMsgCount = chatHistoryList.stream().filter(v -> v.getSender().equals("manual")).count();
            
            if (userMsgCount == 0 || manualMsgCount == 0) {
                LOGGER.info("质量检查对话内容不足, sessionId:{}, userMsgCount:{}, manualMsgCount:{}", 
                    sessionList.getId(), userMsgCount, manualMsgCount);
                return;
            }

            // 构建提交数据
            JSONObject submitData = buildSubmitData(sessionList, chatHistoryList);
            
            // 调用接口A提交质量检查
            String response = HttpClientUtil.post(appPropertyConfig.getQualityInspectionSubmitUrl(), 
                submitData.toJSONString(), 10000);
            
            LOGGER.info("质量检查提交接口返回, sessionId:{}, response:{}", sessionList.getId(), response);
            
            if (StringUtils.isBlank(response)) {
                LOGGER.error("质量检查提交接口返回为空, sessionId:{}", sessionList.getId());
                updateQualityInspectionStatus(sessionList.getId(), 3, null); // 3-检查失败
                return;
            }

            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.getIntValue("code") == 0 && responseJson.containsKey("data")) {
                JSONObject data = responseJson.getJSONObject("data");
                String taskId = data.getString("taskId");
                if (StringUtils.isNotBlank(taskId)) {
                    // 更新状态为检查中
                    updateQualityInspectionStatus(sessionList.getId(), 1, taskId); // 1-检查中
                    LOGGER.info("质量检查提交成功, sessionId:{}, taskId:{}", sessionList.getId(), taskId);
                } else {
                    LOGGER.error("质量检查提交返回taskId为空, sessionId:{}", sessionList.getId());
                    updateQualityInspectionStatus(sessionList.getId(), 3, null); // 3-检查失败
                }
            } else {
                LOGGER.error("质量检查提交失败, sessionId:{}, response:{}", sessionList.getId(), response);
                updateQualityInspectionStatus(sessionList.getId(), 3, null); // 3-检查失败
            }

        } catch (Exception e) {
            LOGGER.error("提交质量检查发生错误, sessionId:{}, error:{}", sessionList.getId(), e.getMessage(), e);
            updateQualityInspectionStatus(sessionList.getId(), 3, null); // 3-检查失败
        }
    }

    /**
     * 查询质量检查结果
     */
    @Transactional
    public void queryQualityInspectionResult(String taskId, String sessionKey) {
        try {
            if (StringUtils.isBlank(appPropertyConfig.getQualityInspectionQueryUrl())) {
                LOGGER.error("质量检查查询URL未配置, taskId:{}", taskId);
                return;
            }

            SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
            if (sessionList == null) {
                LOGGER.error("会话不存在, sessionKey:{}, taskId:{}", sessionKey, taskId);
                return;
            }

            // 构建查询参数
            JSONObject queryData = new JSONObject();
            queryData.put("taskId", taskId);

            // 调用接口B查询结果
            String response = HttpClientUtil.post(appPropertyConfig.getQualityInspectionQueryUrl(), 
                queryData.toJSONString(), 10000);
            
            LOGGER.info("质量检查查询接口返回, sessionId:{}, taskId:{}, response:{}", 
                sessionList.getId(), taskId, response);

            if (StringUtils.isBlank(response)) {
                LOGGER.error("质量检查查询接口返回为空, sessionId:{}, taskId:{}", sessionList.getId(), taskId);
                return;
            }

            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.getIntValue("code") == 0 && responseJson.containsKey("data")) {
                JSONObject data = responseJson.getJSONObject("data");
                Integer score = data.getInteger("score");

                // 更新质量检查结果
                updateQualityInspectionResult(sessionList.getId(), 2, score, new Date()); // 2-已完成
                LOGGER.info("质量检查结果更新成功, sessionId:{}, taskId:{}, score:{}",
                    sessionList.getId(), taskId, score);
            } else {
                LOGGER.error("质量检查查询失败, sessionId:{}, taskId:{}, response:{}", 
                    sessionList.getId(), taskId, response);
            }

        } catch (Exception e) {
            LOGGER.error("查询质量检查结果发生错误, taskId:{}, sessionKey:{}, error:{}", 
                taskId, sessionKey, e.getMessage(), e);
        }
    }

    /**
     * 获取用于质量检查的对话历史
     */
    private List<ChatHistoryEntity> getChatHistoryForInspection(String sessionKey) {
        List<ChatHistoryEntity> chatHistoryList = chatHistoryRepo.findHistoryListBySessionKey(sessionKey);
        if (CollectionUtils.isEmpty(chatHistoryList)) {
            return null;
        }

        // 过滤掉撤回消息和系统消息，只保留用户和客服的消息
        return chatHistoryList.stream()
                .filter(v -> v.getRecall() == null)
                .filter(v -> v.getSender().equals("user") || v.getSender().equals("manual"))
                .collect(Collectors.toList());
    }

    /**
     * 构建提交给质量检查接口的数据
     */
    private JSONObject buildSubmitData(SessionListEntity sessionList, List<ChatHistoryEntity> chatHistoryList) {
        JSONObject submitData = new JSONObject();
        submitData.put("sessionKey", sessionList.getSessionKey());
        submitData.put("orgId", sessionList.getOrgId());
        submitData.put("clientId", sessionList.getClientId());
        submitData.put("customerName", sessionList.getCustomerName());
        submitData.put("serviceUser", sessionList.getLastServiceUser());
        submitData.put("createTime", sessionList.getCreateTime());
        submitData.put("offlineTime", sessionList.getOfflineTime());
        submitData.put("durationSecond", sessionList.getDurationSecond());

        // 构建对话内容
        StringBuilder dialogueBuilder = new StringBuilder();
        for (ChatHistoryEntity chatHistory : chatHistoryList) {
            String content = chatHistory.getContent();
            content = CommonUtil.removeHtmlTagsAndNewlines(content);
            
            if (chatHistory.getSender().equals("manual")) {
                dialogueBuilder.append("客服：").append(content).append("\n");
            } else if (chatHistory.getSender().equals("user")) {
                dialogueBuilder.append("用户：").append(content).append("\n");
            }
        }
        
        submitData.put("dialogue", dialogueBuilder.toString());
        return submitData;
    }

    /**
     * 更新质量检查状态
     */
    @Transactional
    public void updateQualityInspectionStatus(Integer sessionId, Integer status, String taskId) {
        try {
            sessionListRepo.updateQualityInspectionStatus(sessionId, status, taskId);
        } catch (Exception e) {
            LOGGER.error("更新质量检查状态失败, sessionId:{}, status:{}, taskId:{}, error:{}", 
                sessionId, status, taskId, e.getMessage(), e);
        }
    }

    /**
     * 更新质量检查结果
     */
    @Transactional
    public void updateQualityInspectionResult(Integer sessionId, Integer status, Integer score,
                                            Date inspectionTime) {
        try {
            sessionListRepo.updateQualityInspectionResult(sessionId, status, score, inspectionTime);
        } catch (Exception e) {
            LOGGER.error("更新质量检查结果失败, sessionId:{}, status:{}, score:{}, error:{}",
                sessionId, status, score, e.getMessage(), e);
        }
    }
}
