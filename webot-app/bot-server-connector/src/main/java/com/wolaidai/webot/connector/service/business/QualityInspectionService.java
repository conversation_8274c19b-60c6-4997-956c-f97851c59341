package com.wolaidai.webot.connector.service.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.utils.CommonUtil;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.repo.ChatHistoryRepo;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class QualityInspectionService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final AppPropertyConfig appPropertyConfig;
    private final SessionListRepo sessionListRepo;
    private final ChatHistoryRepo chatHistoryRepo;

    /**
     * 异步提交质量检查
     */
    @Async("threadPool")
    @Transactional
    public void submitQualityInspectionAsync(SessionListEntity sessionList) {
        try {
            if (!appPropertyConfig.isQualityInspectionEnabled()) {
                LOGGER.debug("质量检查功能未启用, sessionId:{}", sessionList.getId());
                return;
            }

            if (StringUtils.isBlank(appPropertyConfig.getQualityInspectionSubmitUrl())) {
                LOGGER.error("质量检查提交URL未配置, sessionId:{}", sessionList.getId());
                return;
            }

            // 获取对话历史
            List<ChatHistoryEntity> chatHistoryList = getChatHistoryForInspection(sessionList.getSessionKey());
            if (CollectionUtils.isEmpty(chatHistoryList)) {
                LOGGER.info("质量检查对话历史为空, sessionId:{}", sessionList.getId());
                return;
            }

            // 检查是否有有效的用户和客服对话
            long userMsgCount = chatHistoryList.stream().filter(v -> v.getSender().equals("user")).count();
            long manualMsgCount = chatHistoryList.stream().filter(v -> v.getSender().equals("manual")).count();
            
            if (userMsgCount == 0 || manualMsgCount == 0) {
                LOGGER.info("质量检查对话内容不足, sessionId:{}, userMsgCount:{}, manualMsgCount:{}", 
                    sessionList.getId(), userMsgCount, manualMsgCount);
                return;
            }

            // 构建提交数据（符合阿里云UploadDataV4 API规范）
            JSONObject submitData = buildSubmitData(sessionList, chatHistoryList);

            // 调用阿里云智能对话分析服务提交质量检查
            String response = HttpClientUtil.post(appPropertyConfig.getQualityInspectionSubmitUrl(),
                submitData.toJSONString(), 10000);

            LOGGER.info("阿里云质量检查提交接口返回, sessionId:{}, fileName:{}, channelsCount:{}, response:{}",
                sessionList.getId(), submitData.getString("FileName"),
                submitData.getString("Channels") != null ? JSON.parseArray(submitData.getString("Channels")).size() : 0,
                response);
            
            if (StringUtils.isBlank(response)) {
                LOGGER.error("阿里云质量检查提交接口返回为空, sessionId:{}, fileName:{}",
                    sessionList.getId(), submitData.getString("FileName"));
                updateQualityInspectionStatus(sessionList.getId(), 3, null); // 3-检查失败
                return;
            }

            JSONObject responseJson = JSON.parseObject(response);
            // 阿里云API返回格式：{"RequestId":"xxx","Code":"200","Message":"success","Data":"taskId"}
            if ("200".equals(responseJson.getString("Code")) && responseJson.containsKey("Data")) {
                String taskId = responseJson.getString("Data");
                if (StringUtils.isNotBlank(taskId)) {
                    // 更新状态为检查中
                    updateQualityInspectionStatus(sessionList.getId(), 1, taskId); // 1-检查中
                    LOGGER.info("阿里云质量检查提交成功, sessionId:{}, fileName:{}, taskId:{}",
                        sessionList.getId(), submitData.getString("FileName"), taskId);
                } else {
                    LOGGER.error("阿里云质量检查提交返回taskId为空, sessionId:{}, fileName:{}",
                        sessionList.getId(), submitData.getString("FileName"));
                    updateQualityInspectionStatus(sessionList.getId(), 3, null); // 3-检查失败
                }
            } else {
                LOGGER.error("阿里云质量检查提交失败, sessionId:{}, fileName:{}, code:{}, message:{}",
                    sessionList.getId(), submitData.getString("FileName"),
                    responseJson.getString("Code"), responseJson.getString("Message"));
                updateQualityInspectionStatus(sessionList.getId(), 3, null); // 3-检查失败
            }

        } catch (Exception e) {
            LOGGER.error("提交质量检查发生错误, sessionId:{}, error:{}", sessionList.getId(), e.getMessage(), e);
            updateQualityInspectionStatus(sessionList.getId(), 3, null); // 3-检查失败
        }
    }

    /**
     * 查询质量检查结果
     */
    @Transactional
    public void queryQualityInspectionResult(String taskId, String sessionKey) {
        try {
            if (StringUtils.isBlank(appPropertyConfig.getQualityInspectionQueryUrl())) {
                LOGGER.error("质量检查查询URL未配置, taskId:{}", taskId);
                return;
            }

            SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
            if (sessionList == null) {
                LOGGER.error("会话不存在, sessionKey:{}, taskId:{}", sessionKey, taskId);
                return;
            }

            // 构建查询参数
            JSONObject queryData = new JSONObject();
            queryData.put("taskId", taskId);

            // 调用接口B查询结果
            String response = HttpClientUtil.post(appPropertyConfig.getQualityInspectionQueryUrl(), 
                queryData.toJSONString(), 10000);
            
            LOGGER.info("阿里云质量检查查询接口返回, sessionId:{}, taskId:{}, response:{}",
                sessionList.getId(), taskId, response);

            if (StringUtils.isBlank(response)) {
                LOGGER.error("阿里云质量检查查询接口返回为空, sessionId:{}, taskId:{}", sessionList.getId(), taskId);
                return;
            }

            JSONObject responseJson = JSON.parseObject(response);
            // 阿里云API返回格式：{"RequestId":"xxx","Code":"200","Message":"success","Data":{...}}
            if ("200".equals(responseJson.getString("Code")) && responseJson.containsKey("Data")) {
                JSONObject data = responseJson.getJSONObject("Data");

                // 解析阿里云质量检查结果
                Integer score = extractQualityScore(data);
                String result = data.toJSONString(); // 保存完整的检查结果

                // 更新质量检查结果
                updateQualityInspectionResult(sessionList.getId(), 2, score, result, new Date()); // 2-已完成
                LOGGER.info("阿里云质量检查结果更新成功, sessionId:{}, taskId:{}, score:{}",
                    sessionList.getId(), taskId, score);
            } else {
                LOGGER.error("阿里云质量检查查询失败, sessionId:{}, taskId:{}, code:{}, message:{}",
                    sessionList.getId(), taskId, responseJson.getString("Code"), responseJson.getString("Message"));
            }

        } catch (Exception e) {
            LOGGER.error("查询质量检查结果发生错误, taskId:{}, sessionKey:{}, error:{}", 
                taskId, sessionKey, e.getMessage(), e);
        }
    }

    /**
     * 获取用于质量检查的对话历史
     */
    private List<ChatHistoryEntity> getChatHistoryForInspection(String sessionKey) {
        List<ChatHistoryEntity> chatHistoryList = chatHistoryRepo.findHistoryListBySessionKey(sessionKey);
        if (CollectionUtils.isEmpty(chatHistoryList)) {
            return null;
        }

        // 过滤掉撤回消息和系统消息，只保留用户和客服的消息
        return chatHistoryList.stream()
                .filter(v -> v.getRecall() == null)
                .filter(v -> v.getSender().equals("user") || v.getSender().equals("manual"))
                .collect(Collectors.toList());
    }

    /**
     * 构建提交给阿里云智能对话分析服务的数据（符合UploadDataV4 API规范）
     */
    private JSONObject buildSubmitData(SessionListEntity sessionList, List<ChatHistoryEntity> chatHistoryList) {
        JSONObject submitData = new JSONObject();

        // 必需参数
        submitData.put("DataSetName", "customer_service_quality"); // 数据集名称
        submitData.put("FileName", generateFileName(sessionList)); // 文件名

        // 可选参数 - 业务相关信息
        JSONObject metaInfo = new JSONObject();
        metaInfo.put("sessionKey", sessionList.getSessionKey());
        metaInfo.put("orgId", sessionList.getOrgId());
        metaInfo.put("clientId", sessionList.getClientId());
        metaInfo.put("customerName", sessionList.getCustomerName());
        metaInfo.put("serviceUser", sessionList.getLastServiceUser());
        metaInfo.put("createTime", sessionList.getCreateTime());
        metaInfo.put("offlineTime", sessionList.getOfflineTime());
        metaInfo.put("durationSecond", sessionList.getDurationSecond());
        submitData.put("MetaInfo", metaInfo.toJSONString());

        // 构建对话通道信息（Channels）
        JSONArray channels = buildChannelsData(chatHistoryList);
        submitData.put("Channels", channels.toJSONString());

        return submitData;
    }

    /**
     * 生成文件名
     */
    private String generateFileName(SessionListEntity sessionList) {
        return String.format("session_%s_%d_%d.json",
            sessionList.getSessionKey(),
            sessionList.getOrgId(),
            System.currentTimeMillis());
    }

    /**
     * 构建对话通道数据（符合阿里云Channels格式）
     */
    private JSONArray buildChannelsData(List<ChatHistoryEntity> chatHistoryList) {
        JSONArray channels = new JSONArray();

        for (ChatHistoryEntity chatHistory : chatHistoryList) {
            JSONObject channel = new JSONObject();

            // 设置通道基本信息
            if (chatHistory.getSender().equals("user")) {
                channel.put("ChannelId", 0); // 0表示客户通道
                channel.put("Identity", "customer");
            } else if (chatHistory.getSender().equals("manual")) {
                channel.put("ChannelId", 1); // 1表示客服通道
                channel.put("Identity", "agent");
            } else {
                continue; // 跳过系统消息
            }

            // 设置消息内容
            String content = chatHistory.getContent();
            content = CommonUtil.removeHtmlTagsAndNewlines(content);
            channel.put("Words", content);

            // 设置时间信息（转换为毫秒时间戳）
            if (chatHistory.getDate() != null) {
                channel.put("Begin", chatHistory.getDate().getTime());
                channel.put("End", chatHistory.getDate().getTime() + 1000); // 假设每条消息持续1秒
            }

            // 设置静音标识（对话分析中通常设为0）
            channel.put("SilenceDuration", 0);

            // 设置情绪值（可选，默认为0）
            channel.put("EmotionValue", 0);

            // 设置语速（可选，默认为0）
            channel.put("SpeechRate", 0);

            channels.add(channel);
        }

        return channels;
    }

    /**
     * 从阿里云质量检查结果中提取质量分数
     */
    private Integer extractQualityScore(JSONObject data) {
        try {
            // 阿里云智能对话分析返回的数据结构可能包含多个维度的分数
            // 这里根据实际API返回结构进行调整
            if (data.containsKey("Score")) {
                return data.getInteger("Score");
            }

            // 如果有详细的评分结果，可以计算平均分或总分
            if (data.containsKey("ResultInfo")) {
                JSONObject resultInfo = data.getJSONObject("ResultInfo");
                if (resultInfo.containsKey("Score")) {
                    return resultInfo.getInteger("Score");
                }
            }

            // 如果有规则命中结果，可以根据命中情况计算分数
            if (data.containsKey("Rules")) {
                // 这里可以根据规则命中情况计算质量分数
                // 具体逻辑需要根据业务需求定制
                return calculateScoreFromRules(data.getJSONArray("Rules"));
            }

            // 默认返回null，表示无法获取分数
            return null;

        } catch (Exception e) {
            LOGGER.error("解析阿里云质量检查分数失败, data:{}, error:{}", data.toJSONString(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据规则命中情况计算质量分数
     */
    private Integer calculateScoreFromRules(JSONArray rules) {
        if (rules == null || rules.isEmpty()) {
            return 100; // 无规则命中，默认满分
        }

        int totalScore = 100;
        int deductionPerRule = 10; // 每个规则命中扣10分

        for (int i = 0; i < rules.size(); i++) {
            JSONObject rule = rules.getJSONObject(i);
            // 可以根据规则的严重程度进行不同的扣分
            if (rule.containsKey("Hit") && rule.getBoolean("Hit")) {
                totalScore -= deductionPerRule;
            }
        }

        return Math.max(0, totalScore); // 确保分数不低于0
    }

    /**
     * 更新质量检查状态
     */
    @Transactional
    public void updateQualityInspectionStatus(Integer sessionId, Integer status, String taskId) {
        try {
            sessionListRepo.updateQualityInspectionStatus(sessionId, status, taskId);
        } catch (Exception e) {
            LOGGER.error("更新质量检查状态失败, sessionId:{}, status:{}, taskId:{}, error:{}", 
                sessionId, status, taskId, e.getMessage(), e);
        }
    }

    /**
     * 更新质量检查结果
     */
    @Transactional
    public void updateQualityInspectionResult(Integer sessionId, Integer status, Integer score,
                                            String result, Date inspectionTime) {
        try {
            sessionListRepo.updateQualityInspectionResult(sessionId, status, score, result, inspectionTime);
        } catch (Exception e) {
            LOGGER.error("更新质量检查结果失败, sessionId:{}, status:{}, score:{}, error:{}",
                sessionId, status, score, e.getMessage(), e);
        }
    }
}
