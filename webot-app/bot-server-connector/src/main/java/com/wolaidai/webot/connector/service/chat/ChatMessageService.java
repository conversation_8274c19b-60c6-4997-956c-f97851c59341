package com.wolaidai.webot.connector.service.chat;

import com.wolaidai.webot.connector.model.MessageModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.DataProcessService;
import com.wolaidai.webot.connector.service.event.chat.MessageEventService;
import io.socket.socketio.server.SocketIoSocket;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChatMessageService extends DataProcessService {

    @Override
    protected List<SocketPacketModel> processData(MessageModel messageModel) {
        return applicationContext.getBean(MessageEventService.class).process(messageModel);
    }
}
