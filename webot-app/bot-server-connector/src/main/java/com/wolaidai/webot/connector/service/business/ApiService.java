package com.wolaidai.webot.connector.service.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.nls.client.AccessToken;
import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import lombok.AllArgsConstructor;
import okhttp3.*;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@AllArgsConstructor
public class ApiService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final AppPropertyConfig appPropertyConfig;

    private static AccessToken accessToken;

    public byte[] getFile(String mediaId){
        try {
            return HttpClientUtil.getFile(appPropertyConfig.getFileServerUrl()+"/1/"+mediaId);
        } catch (Exception e) {
            LOGGER.error("获取文件失败", e);
        }
        return null;
    }

    private boolean checkAccessToken() {
        if (accessToken == null || accessToken.getExpireTime() - System.currentTimeMillis()/1000 < 30) {
            accessToken = new AccessToken(appPropertyConfig.getAliAccessKeyId(), appPropertyConfig.getAliAccessKeySecret());
            try {
                accessToken.apply();
                LOGGER.info("获取accessToken成功,token:{},expireTime:{}", accessToken.getToken(), accessToken.getExpireTime());
            } catch (IOException e) {
                LOGGER.error("获取accessToken失败", e);
                return false;
            }
            if(accessToken.getToken()==null){
                LOGGER.error("获取accessToken失败,token返回空");
                return false;
            }
        }
        return true;
    }

    public byte[] speechSynthesizer(String text, String voice, String volume, String speechRate, String pitchRate, String format, String sampleRate) {
        /**
         * 设置HTTPS GET请求
         * 1.使用HTTPS协议
         * 2.语音识别服务域名：nls-gateway.cn-shanghai.aliyuncs.com
         * 3.语音识别接口请求路径：/stream/v1/tts
         * 4.设置必须请求参数：appkey、token、text、format、sample_rate
         * 5.设置可选请求参数：voice、volume、speech_rate、pitch_rate
         */
        if (StringUtils.isBlank(text) || StringUtils.isBlank(voice)) {
            return null;
        }

        String textUrlEncode = text;
        try {
            textUrlEncode = URLEncoder.encode(textUrlEncode, "UTF-8")
                    .replace("+", "%20")
                    .replace("*", "%2A")
                    .replace("%7E", "~");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        if (!checkAccessToken()) return null;

        byte[] data =null;
        String url = "https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/tts";
        url = url + "?appkey=" + appPropertyConfig.getAliAppKey();
        url = url + "&token=" + accessToken.getToken();
        url = url + "&text=" + textUrlEncode;
        if (StringUtils.isNotBlank(format)) {
            url = url + "&format=" + format;
        }else{
            url = url + "&format=wav";
        }

        url = url + "&voice=" + voice;
        if (StringUtils.isNotBlank(sampleRate)) {
            url = url + "&sample_rate=" + sampleRate;
        }
        // voice 发音人，可选，默认是xiaoyun
        url = url + "&voice=" + voice;
        // volume 音量，范围是0~100，可选，默认50
        if (StringUtils.isNotBlank(volume)) {
            url = url + "&volume=" + volume;
        }
        // speech_rate 语速，范围是-500~500，可选，默认是0
        if (StringUtils.isNotBlank(speechRate)) {
            url = url + "&speech_rate=" + speechRate;
        }
        // pitch_rate 语调，范围是-500~500，可选，默认是0
        if (StringUtils.isNotBlank(pitchRate)) {
            url = url + "&pitch_rate=" + pitchRate;
        }
        LOGGER.info("语音合成URL: {}", url);
        /**
         * 发送HTTPS GET请求，处理服务端的响应
         */
        Request request = new Request.Builder().url(url).get().build();
        try {
            long start = System.currentTimeMillis();
            OkHttpClient client = new OkHttpClient();
            Response response = client.newCall(request).execute();
            LOGGER.info("语音合成耗时{}ms" ,System.currentTimeMillis() - start);
            String contentType = response.header("Content-Type");
            if ("audio/mpeg".equals(contentType)) {
                LOGGER.info("语音合成调用成功");
                data = response.body().bytes();
            } else {
                // ContentType 为 null 或者为 "application/json"
                String errorMessage = response.body().string();
                LOGGER.error("语音合成调用失败,返回信息:{}:", errorMessage);
            }
            response.close();
        } catch (Exception e) {
            LOGGER.error("语音合成失败", e);
        }
        return data;
    }

    public String speechRecognition(InputStream inputStream, String format, String sampleRate,
                                    boolean enablePunctuationPrediction,
                                    boolean enableInverseTextNormalization,
                                    boolean enableVoiceDetection) {
        /**
         * 设置HTTP REST POST请求
         * 1.使用http协议
         * 2.语音识别服务域名：nls-gateway.cn-shanghai.aliyuncs.com
         * 3.语音识别接口请求路径：/stream/v1/asr
         * 4.设置必须请求参数：appkey、format、sample_rate，
         * 5.设置可选请求参数：enable_punctuation_prediction、enable_inverse_text_normalization、enable_voice_detection
         */

        if (!checkAccessToken()) return null;

        String url = "http://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/asr";
        String requestUrl = url;
        requestUrl = requestUrl + "?appkey=" + appPropertyConfig.getAliAppKey();
        if (StringUtils.isNotBlank(format)) {
            requestUrl = requestUrl + "&format=" + format;
        }
        if(StringUtils.isNotBlank(sampleRate)) {
            requestUrl = requestUrl + "&sample_rate=" + sampleRate;
        }
        if (enablePunctuationPrediction) {
            requestUrl = requestUrl + "&enable_punctuation_prediction=" + true;
        }
        if (enableInverseTextNormalization) {
            requestUrl = requestUrl + "&enable_inverse_text_normalization=" + true;
        }
        if (enableVoiceDetection) {
            requestUrl = requestUrl + "&enable_voice_detection=" + true;
        }
        LOGGER.info("语音识别URL: {}", requestUrl);
        /**
         * 设置HTTP 头部字段
         * 1.鉴权参数
         * 2.Content-Type：application/octet-stream
         */
        HashMap<String, String> headers = new HashMap<String, String>();
        headers.put("X-NLS-Token", accessToken.getToken());
        headers.put("Content-Type", "application/octet-stream");
        /**
         * 发送HTTP POST请求，返回服务端的响应
         */
        long start = System.currentTimeMillis();
        RequestBody body;
        if (inputStream==null) {
            LOGGER.error("语音识别文件路径错误");
            return null;
        } else {
            try {
                body = RequestBody.create(MediaType.parse("application/octet-stream"), IOUtils.toByteArray(inputStream));
            } catch (IOException e) {
                LOGGER.error("语音识别读取文件流错误",e);
                return null;
            }
        }
        Headers.Builder hb = new Headers.Builder();
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                hb.add(entry.getKey(), entry.getValue());
            }
        }
        Request request = new Request.Builder()
                .url(requestUrl)
                .headers(hb.build())
                .post(body)
                .build();
        String response = null;
        OkHttpClient.Builder httpBuilder = new OkHttpClient.Builder();
        OkHttpClient client = httpBuilder.connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
        try {
            Response s = client.newCall(request).execute();
            response = s.body().string();
            s.close();
        } catch (SocketTimeoutException e) {
            LOGGER.error("语音识别超时",e);
        } catch (IOException e) {
            LOGGER.error("语音识别调用失败",e);
        }
        LOGGER.info("语音识别耗时{}ms" ,System.currentTimeMillis() - start);
        if (response != null) {
            LOGGER.info("识别结果:{} " ,response);
            return JSONPath.read(response, "result").toString();
        }
        return null;
    }

    public JSONObject getReply(Integer orgId,String chatId,Integer botId,Integer skillGroupId,Integer skillId,String clientId,Integer clientType,String type,String content,String recognition,String cid,boolean debug){
        JSONObject data = new JSONObject().put("chatId",chatId).put("clientId",clientId).put("clientType",clientType).put("type",type);
        if(content!=null){
            data.put("content",content.replace("(","").replace(")","").replace("^",""));
        }
        if(orgId!=null){
            data.put("orgId",orgId);
        }
        if(botId!=null){
            data.put("botId",botId);
        }else{
            data.put("botId",-1);
        }
        if(skillGroupId!=null){
            data.put("skillGroupId",skillGroupId);
        }
        if(skillId!=null){
            data.put("skillId",skillId);
        }
        if(StringUtils.isNotBlank(recognition)){
            data.put("recognition",recognition);
        }
        if(StringUtils.isNotBlank(cid)){
            data.put("cid",cid);
        }
        if(debug){
            data.put("debug",true);
        }
        try {
            String response = HttpClientUtil.post(appPropertyConfig.getBotEngineUrl()+"/getReply", data.toString(), 10000);
            return new JSONObject(response);
        } catch (Exception e) {
            LOGGER.error("call getReply error",e);
            return null;
        }
    }

    public JSONObject resetBot(Integer botId,String clientId){
        if(botId==null){
            botId = -1;
        }
        JSONObject data = new JSONObject().put("botId",botId).put("clientId",clientId);
        try {
            String response = HttpClientUtil.post(appPropertyConfig.getBotEngineUrl()+"/reset",data.toString(), 10000);
            return new JSONObject(response);
        } catch (Exception e) {
            LOGGER.error("call resetBot error",e);
            return null;
        }
    }

    public JSONObject getOpReply(Integer orgId,String chatId,Integer botId,String clientId,Integer clientType,String type,String content){
        JSONObject data = new JSONObject().put("chatId",chatId).put("clientId",clientId).put("clientType",clientType).put("type",type);
        if(content!=null){
            data.put("content",content.replace("(","").replace(")","").replace("^",""));
        }
        if(orgId!=null){
            data.put("orgId",orgId);
        }
        if(botId!=null){
            data.put("botId",botId);
        }else{
            data.put("botId",-1);
        }
        try {
            String response = HttpClientUtil.post(appPropertyConfig.getBotEngineUrl()+"/getOperationReply", data.toString(), 10000);
            return new JSONObject(response);
        } catch (Exception e) {
            LOGGER.error("call getReply error",e);
            return null;
        }
    }

    public String getFaceDocument(String mobile) {
        if (StringUtils.isBlank(appPropertyConfig.getFaceDocumentsUrl())) {
            return null;
        }
        try {
            String response = HttpClientUtil.get(appPropertyConfig.getFaceDocumentsUrl() + "?mobile=" + mobile);
            if (StringUtils.isBlank(response)) {
                return null;
            }
            com.alibaba.fastjson.JSONObject responseJson = JSON.parseObject(response);
            com.alibaba.fastjson.JSONArray data = responseJson.getJSONArray("data");
            if (!CollectionUtils.isEmpty(data)) {
                com.alibaba.fastjson.JSONObject documentData = data.getJSONObject(0);
                return documentData.getString("url");
            }
        } catch (Exception e) {
            LOGGER.error("call getFaceDocument error", e);
        }
        return null;
    }

    public String getPublicAccountMobile(String appid,String openid) {
        if(StringUtils.isAnyBlank(appPropertyConfig.getPublicAccountUrl(),appid,openid)){
            return null;
        }
        try {
            String response = HttpClientUtil.get(appPropertyConfig.getPublicAccountUrl() + "?appid=" + appid + "&openid=" + openid);
            if(StringUtils.isBlank(response)){
                return null;
            }
            com.alibaba.fastjson.JSONObject responseJson = JSON.parseObject(response);
            com.alibaba.fastjson.JSONObject data = responseJson.getJSONObject("data");
            if(data!=null){
                return data.getString("mobile");
            }
        } catch (Exception e) {
            LOGGER.error("call getPublicAccountMobile error", e);
        }
        return null;
    }

    public String getFaceDetectionVendorReq(String mobile, String vendor, String callbackUrl) {
        if (StringUtils.isBlank(appPropertyConfig.getFaceDetectionVendorUrl())) {
            return null;
        }
        try {
            String reqUrl = appPropertyConfig.getFaceDetectionVendorUrl() + "/" + mobile + "?source=WELAB-AI&vendor=" + vendor + "&url=" + callbackUrl;
            String response = HttpClientUtil.get(reqUrl);
            if (StringUtils.isBlank(response)) {
                return null;
            }
            com.alibaba.fastjson.JSONObject responseJson = JSON.parseObject(response);
            if(responseJson.getInteger("status") == 200) {
                return responseJson.getString("data");
            }
        } catch (Exception e) {
            LOGGER.error("获取第三方人脸验证链接失败，mobile:" + mobile + ", vendor:" + vendor, e);
        }
        return null;
    }


    public com.alibaba.fastjson.JSONObject getFaceDetectionVendorResult(String mobile, String vendor, String orderNo) {
        if (StringUtils.isBlank(appPropertyConfig.getFaceDetectionVendorResultUrl())) {
            return null;
        }
        try {
            String reqUrl = appPropertyConfig.getFaceDetectionVendorResultUrl() + "/" + mobile + "?vendor=" + vendor + "&orderNo=" + orderNo;
            String response = HttpClientUtil.get(reqUrl);
            if (StringUtils.isBlank(response)) {
                return null;
            }
            com.alibaba.fastjson.JSONObject responseJson = JSON.parseObject(response);
            if(responseJson.getInteger("status") == 200) {
                com.alibaba.fastjson.JSONObject data = responseJson.getJSONObject("data");
                String message = responseJson.getString("message");
                data.put("message", message);
                return data;
            }
        } catch (Exception e) {
            LOGGER.error("获取第三方人脸验证结果失败，mobile:" + mobile + ", vendor:" + vendor + ", orderNo:" + orderNo , e);
        }
        return null;
    }


    public com.alibaba.fastjson.JSONObject queryDocQa(com.alibaba.fastjson.JSONObject data) {
        try {
            String responseStr = HttpClientUtil.post(appPropertyConfig.getDocQaUrl() + "/query", data.toJSONString(), 30000);
            if (StringUtils.isNotBlank(responseStr)) {
                com.alibaba.fastjson.JSONObject resData = com.alibaba.fastjson.JSONObject.parseObject(responseStr);
                if(resData != null && (Integer)resData.getOrDefault("ret", 1)==0 && resData.getJSONObject("data") != null) {
                    return resData.getJSONObject("data");
                }
            }
        } catch (Exception e) {
            LOGGER.error("调用文档问答失败,cid:{}", data.get("cid"), e);
        }
        return null;
    }



}
