package com.wolaidai.webot.connector.service.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.bson.json.JsonObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@AllArgsConstructor
public class TemplateService {
	private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

	private final AppPropertyConfig appPropertyConfig;

	public static final Map<String, String> VARIABLE_TO_URL;
	public static final Map<String, String> VARIABLE_TO_CONTENT;

	static {
		Map<String, String> map = new HashMap<>();
		map.put("loan", "v1/qa-bot/loan");
		map.put("repaymentScheme", "v1/qa-bot/repayment-scheme");
		map.put("collectionStaff", "v1/qa-bot/collection-staff");
		map.put("transferCompany", "v1/qa-bot/transfer-company");
		map.put("vipOrder", "v1/qa-bot/vip-order");
		VARIABLE_TO_URL = Collections.unmodifiableMap(map);

		Map<String, String> map2 = new HashMap<>();
		map2.put("loan", "<p>%d. 借款金额：%s，借款状态：还款中，还款日：%s</p>");
		map2.put("repaymentScheme", "<p>%d. 贷款号：%s，方案类型：%s，需还款金额：%s，减免金额：%s，有效期：%s</p>");
		map2.put("collectionStaff", "<p>%d. 借款金额：%s，专员id：%s</p>");
		map2.put("transferCompany", "<p>%d. 借款金额：%s，机构名称：%s，联系方式：%s</p>");
		map2.put("vipOrder", "<p>%d. 开通金额：%s，开通时间：%s</p>");
		VARIABLE_TO_CONTENT = Collections.unmodifiableMap(map2);
	}

	private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{([a-zA-Z0-9_]+)\\}");

	public String replacePlaceholders(String template, String mobile) {
		if (StringUtils.isBlank(template) || !(template.contains("{") && template.contains("}"))) {
			return template;
		}
		Matcher matcher = PLACEHOLDER_PATTERN.matcher(template);
		StringBuffer result = new StringBuffer();
		while (matcher.find()) {
			String varName = matcher.group(1);
			String uri = getUriByVarName(varName);
			if (StringUtils.isBlank(uri)) {
				continue;
			}

			String value = "";
			try {

				String res = "";
				if (StringUtils.isNotBlank(mobile)) {
					res = HttpClientUtil.get(appPropertyConfig.getKefuUrlPre() + uri + "?mobile=" + mobile, 30000);
				}
				Map<String, Object> map = parseRes(res, varName);
				if ((Boolean) map.get("isSuccess")) {
					value = map.get("content").toString();
				} else {
					if (template.contains("也许您是要问：")){
						return map.get("content").toString() + "<br/><p>也许您是要问：</p>";
					}
					return map.get("content").toString();
				}
			} catch (Exception e) {
				LOGGER.warn("replacePlaceholders, api:{} error, mobile: {}", uri, mobile, e);
			}

			matcher.appendReplacement(result, Matcher.quoteReplacement(value));
		}
		matcher.appendTail(result);
		return result.toString();
	}

	public org.json.JSONObject extractMenu(String text) {
		List<String> paragraphs = extractParagraphs(text);
		int headIndex = -1;
		for (int i = 0; i < paragraphs.size(); i++) {
			if (paragraphs.get(i).contains("热点问题：")) {
				headIndex = i;
				break;
			}
		}
		org.json.JSONObject result = new org.json.JSONObject();
		if (headIndex == -1) {
			result.put("head_content", "");
			result.put("list", new ArrayList<org.json.JSONObject>());
			String tail_content = String.join("\n", paragraphs);
			result.put("tail_content", tail_content);
			return result;
		}
		String head_content = paragraphs.get(headIndex);
		result.put("head_content", head_content);
		List<org.json.JSONObject> questions = new ArrayList<>();
		Pattern questionPattern = Pattern.compile("^\\s*\\d+\\.\\s*(.*)");
		int i = headIndex + 1;
		while (i < paragraphs.size()) {
			String para = paragraphs.get(i);
			Matcher matcher = questionPattern.matcher(para);
			if (matcher.matches()) {
				org.json.JSONObject content = new org.json.JSONObject();
				String question = matcher.group(1).trim();
				content.put("id", i);
				content.put("content", i + "." + question);
				questions.add(content);
				i++;
			} else {
				break;
			}
		}
		String tail_content = String.join("\n", paragraphs.subList(i, paragraphs.size()));
		result.put("list", questions);
		result.put("tail_content", tail_content);
		return result;
	}


	private static List<String> extractParagraphs(String text) {
		List<String> paragraphs = new ArrayList<>();
		Pattern pPattern = Pattern.compile("<p[^>]*>(.*?)</p>", Pattern.DOTALL);
		Matcher matcher = pPattern.matcher(text);
		while (matcher.find()) {
			String content = matcher.group(1);
			String plainText = content.replaceAll("<[^>]+>", "").trim();
			paragraphs.add(plainText);
		}
		return paragraphs;
	}


	private static Map<String, Object> parseRes(String res, String varName) {

		boolean isSuccess = false;
		JSONArray resultList;
		if (StringUtils.isNotBlank(res)) {
			JSONObject resJson = JSON.parseObject(res);
			if ("0".equals(resJson.getString("code"))) {
				resultList = resJson.getJSONArray("result");
				if (resultList != null && !resultList.isEmpty()) {
					isSuccess = true;
				}
			} else {
				resultList = null;
			}
		} else {
			resultList = null;
		}

		Map<String, Object> map = new HashMap<>();
		map.put("isSuccess", isSuccess);

		switch (varName) {
			case "loan":
				if (!isSuccess) {
					map.put("content", "暂未查询到对应记录，如有疑问您可以点击【转人工】咨询。");
				} else {
					map.put("content", IntStream.range(0, resultList.size()).mapToObj(
							i -> {
								JSONObject jsonObject = resultList.getJSONObject(i);
								return String.format(VARIABLE_TO_CONTENT.get(varName), i + 1,
										jsonObject.getBigDecimal("amount"),
										jsonObject.getString("deadDate"));
							}).collect(Collectors.joining("")));
				}
				break;

			case "repaymentScheme":
				if (!isSuccess) {
					map.put("content", "未查询到对应方案。如有疑问您可以点击【转人工】咨询。");
				} else {
					map.put("content", IntStream.range(0, resultList.size()).mapToObj(
							i -> {
								JSONObject jsonObject = resultList.getJSONObject(i);
								return String.format(VARIABLE_TO_CONTENT.get(varName), i + 1,
										jsonObject.getString("appNo"),
										jsonObject.getString("schemeType"),
										jsonObject.getBigDecimal("needRepayAmount"),
										jsonObject.getBigDecimal("reduceAmount"),
										jsonObject.getString("validTime")
								);
							}).collect(Collectors.joining("")));
				}
				break;

			case "collectionStaff":
				if (!isSuccess) {
					map.put("content", "暂未查询到专员信息，如有疑问您可以点击【转人工】咨询。");
				} else {
					map.put("content", IntStream.range(0, resultList.size()).mapToObj(
							i -> {
								JSONObject jsonObject = resultList.getJSONObject(i);
								return String.format(VARIABLE_TO_CONTENT.get(varName), i + 1,
										jsonObject.getBigDecimal("amount").toString(),
										jsonObject.getString("staffId"));
							}).collect(Collectors.joining("")));
				}
				break;

			case "transferCompany":
				if (!isSuccess) {
					map.put("content", "暂未查询到机构信息，如有疑问您可以点击【转人工】咨询。");
				} else {

					map.put("content", IntStream.range(0, resultList.size()).mapToObj(
							i -> {
								JSONObject jsonObject = resultList.getJSONObject(i);
								return String.format(VARIABLE_TO_CONTENT.get(varName), i + 1,
										jsonObject.getBigDecimal("amount").toString(),
										jsonObject.getString("companyName"),
										jsonObject.getString("telNo"));
							}).collect(Collectors.joining("")));
				}
				break;

			case "vipOrder":
				if (!isSuccess) {
					map.put("content", "暂未查询到对应记录，如有疑问您可以点击【转人工】咨询。");
				} else {

					map.put("content", IntStream.range(0, resultList.size()).mapToObj(
							i -> {
								JSONObject jsonObject = resultList.getJSONObject(i);
								return String.format(VARIABLE_TO_CONTENT.get(varName), i + 1,
										jsonObject.getBigDecimal("paymentAmount"),
										DateUtil.formatDate(jsonObject.getDate("paymentAt"), "yyyy-MM-dd HH:mm:ss"));
							}).collect(Collectors.joining("")));
				}
		}


		return map;
	}

	private String getUriByVarName(String varName) {
		return VARIABLE_TO_URL.getOrDefault(varName, "");
	}


	public static void main(String[] args) {
		String res = "{\"code\":\"0\",\"message\":\"请求成功\",\"result\":[{\"deadDate\":\"2025-05-11\",\"amount\":20000.00,\"appNo\":\"25041114134518510621398\"},{\"deadDate\":\"2025-05-10\",\"amount\":20000.00,\"appNo\":\"25041017362164831155133\"},{\"deadDate\":\"2025-05-18\",\"amount\":9000.00,\"appNo\":\"25012410533025634965534\"}]}";

		Map<String, Object> map = parseRes(res, "loan");
		System.out.println("map = " + map);
	}

}
