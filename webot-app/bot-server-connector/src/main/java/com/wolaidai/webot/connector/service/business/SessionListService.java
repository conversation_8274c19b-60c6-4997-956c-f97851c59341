package com.wolaidai.webot.connector.service.business;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.welab.privacy.crypto.SpringCryptoHelper;
import com.welab.privacy.manager.PrivacyBean;
import com.wolaidai.webot.connector.service.BaseService;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;

import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class SessionListService extends BaseService{

    private SpringCryptoHelper springCryptoHelper;

    /**
     * 处理用户敏感数据，将数据库中的数据进行解密处理
     * @param sessionListEntity
     */
    public void decryptCustomerInfo(SessionListEntity sessionListEntity) {
        if(sessionListEntity.getCustomerDetail()==null || !sessionListEntity.getCustomerDetail().containsKey("customers") 
            || sessionListEntity.getCustomerDetail().getJSONArray("customers").size()==0){
          return;
        }

        PrivacyBean mobilePrivacyBean = springCryptoHelper.getConfig("cs_bot", "session_list", "mobile");
        PrivacyBean cnidPrivacyBean = springCryptoHelper.getConfig("cs_bot", "session_list", "cnid");
        JSONArray customers = sessionListEntity.getCustomerDetail().getJSONArray("customers");
        for(int i=0;i<customers.size();i++){
            JSONObject customer = customers.getJSONObject(i);
            String mobile = customer.getString("mobile");
            String cnid = customer.getString("cnid");
            if(StringUtils.isNotBlank(mobile) && mobile.length()>11 && mobilePrivacyBean != null){
                String decMobile = springCryptoHelper.decrypt(mobilePrivacyBean.getDatabase(), mobilePrivacyBean.getTable(), mobilePrivacyBean.getOrigin(), mobile);
                customer.put("mobile",decMobile);
            }
            if(StringUtils.isNotBlank(cnid) && cnid.length()>18 && cnidPrivacyBean != null) {
                String decCnid = springCryptoHelper.decrypt(cnidPrivacyBean.getDatabase(), cnidPrivacyBean.getTable(), cnidPrivacyBean.getOrigin(), cnid);
                customer.put("cnid", decCnid);
            }
        }
        sessionListEntity.getCustomerDetail().put("customers", customers);
    }

    /**
     * 处理用户敏感数据，加密处理
     * @param customers
     */
    public void encryptCustomerInfo(JSONArray customers) {
        if(customers == null || customers.size()==0) {
            return;
        }
        PrivacyBean mobilePrivacyBean = springCryptoHelper.getConfig("cs_bot", "session_list", "mobile");
        PrivacyBean cnidPrivacyBean = springCryptoHelper.getConfig("cs_bot", "session_list", "cnid");
        for(int i=0;i<customers.size();i++){
            JSONObject customer = customers.getJSONObject(i);
            String mobile = customer.getString("mobile");
            String cnid = customer.getString("cnid");
            if(StringUtils.isNotBlank(mobile) && mobile.length()==11 && mobilePrivacyBean != null){
                String encMobile = springCryptoHelper.encrypt(mobilePrivacyBean.getDatabase(), mobilePrivacyBean.getTable(), mobilePrivacyBean.getOrigin(), mobile);
                customer.put("mobile",encMobile);
            }
            if(StringUtils.isNotBlank(cnid) && cnid.length()<=18 && cnidPrivacyBean != null) {
                String encCnid = springCryptoHelper.encrypt(cnidPrivacyBean.getDatabase(), cnidPrivacyBean.getTable(), cnidPrivacyBean.getOrigin(), cnid);
                customer.put("cnid", encCnid);
            }
        }
    }


  
}
