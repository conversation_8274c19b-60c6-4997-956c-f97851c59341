package com.wolaidai.webot.connector.utils;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wolaidai.webot.connector.callback.SocketCallback;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

public class SocketContextUtils {
    private static Cache<String, SocketCallback> callbackCache = CacheBuilder.newBuilder().expireAfterWrite(30, TimeUnit.SECONDS).build();

    public static String addCallback(SocketCallback callback){
        String callbackId = UUID.randomUUID().toString();
        callbackCache.put(callbackId,callback);
        return callbackId;
    }

    public static SocketCallback getCallback(String callbackId){
        return callbackCache.getIfPresent(callbackId);
    }

}
