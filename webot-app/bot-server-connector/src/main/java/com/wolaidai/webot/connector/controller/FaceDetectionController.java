package com.wolaidai.webot.connector.controller;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.fluent.Request;
import org.apache.http.client.utils.URLEncodedUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.common.utils.IOUtils;
import com.aliyun.oss.model.OSSObject;
import com.wolaidai.webot.common.util.OssFileClient;
import com.wolaidai.webot.connector.config.AppPropertyConfig;
import com.wolaidai.webot.connector.constant.RedisConstants;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.constant.SocketNamespace;
import com.wolaidai.webot.connector.constant.WebStatusConstants;
import com.wolaidai.webot.connector.model.ResponseModel;
import com.wolaidai.webot.connector.model.SocketPacketModel;
import com.wolaidai.webot.connector.service.business.ApiService;
import com.wolaidai.webot.connector.service.business.ChatHistoryService;
import com.wolaidai.webot.connector.utils.CryptUtils;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mysql.entity.bot.BotEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.entity.face.FaceDetectionEntity;
import com.wolaidai.webot.data.mysql.entity.face.FaceDetectionHistoryEntity;
import com.wolaidai.webot.data.mysql.repo.BotRepo;
import com.wolaidai.webot.data.mysql.repo.FaceDetectionRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;

@AllArgsConstructor
@RestController
@Api(tags = "人脸验证")
@RequestMapping("/faceid")
public class FaceDetectionController extends BaseController {
    protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    private final StringRedisTemplate csStringRedisTemplate;
    private final RedisTemplate<String, Object> csRedisTemplate;
    private final FaceDetectionRepo faceDetectionRepo;
    private final BotRepo botRepo;
    private final ApiService apiService;

    @Autowired
    private AppPropertyConfig appPropertyConfig;

    @Autowired
    private ChatHistoryService chatHistoryService;

    @Autowired
    private UserStateRepo userStateRepo;
    

    private JSONObject genData(String token, FaceDetectionEntity fd) {
        JSONObject data = new JSONObject();
        //在线客服系统获取入口信息，用于后续跳转
        if("online".equals(fd.getSource())) {
            SessionListEntity session = fd.getSession(); 
            if (null != session) {
                data.put("clientTypeId", session.getClientTypeId());
                BotEntity bot = botRepo.findById(session.getBotId()).orElse(null);
                if (null != bot) {
                    data.put("accessKey", bot.getCode());
                }
            } else {
                String key = String.format(RedisKey.CS_FACE_DETECTION, token);
                if (csStringRedisTemplate.hasKey(key)) {
                    data.put("clientTypeId", csStringRedisTemplate.opsForHash().get(key, "clientTypeId"));
                }
            }
        }
        return data;
    }

    private ResponseModel checkStatusByToken(String token, FaceDetectionEntity fd, JSONObject data) {
        String key = String.format(RedisKey.CS_FACE_DETECTION, token);
        if (!csStringRedisTemplate.hasKey(key) || null == fd) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FACE_TOKEN_EXPIRED, "token不存在或者已过期", data);
        }
        HashOperations<String, String, String> hash = csStringRedisTemplate.opsForHash();
        if (Integer.parseInt(hash.get(key, "failureTimes")) >= appPropertyConfig.getMaxFaceDetectFailureTimes()) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FACE_DETECTION_FAILURE_TIMES_LIMIT, "失败次数达到最大值", data);
        }
        if (hash.hasKey(key, "code") && "0".equals(hash.get(key, "code"))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FACE_DETECTION_UNNECESSARY, "已经通过人脸验证", data);
        }
        //在线客服人脸验证需验证会话有效
        if("online".equals(fd.getSource())) {
            if (null == fd.getSession() || Objects.equals(fd.getSession().getStatus(), SessionListEntity.STATUS_OFFLINE)) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FACE_DETECTION_MISSING, "会话不存在或已结束", data);
            }
        }
        return null;
    }

    @GetMapping("/status")
    @ApiOperation(value = "判断是否可以进行人脸验证")
    public ResponseModel checkStatus(@RequestHeader("FaceID-Token") String token) {
        FaceDetectionEntity fd = faceDetectionRepo.findByToken(token);
        JSONObject data = genData(token, fd);
        ResponseModel res = checkStatusByToken(token, fd, data);
        if (null != res) {
            return res;
        }

        //生成第三方人脸验证链接
        //如果自研报错L1一次(L1:300,600-699错误码 L2:其它错误码)或第三方报错，下一个使用顺序中(顺序：自研，微众，旷视)的其它供应商
        //如果自研报错L2连续3次，下一个使用顺序中的其它供应商
        if (null != fd && appPropertyConfig.isFaceDetectionVendorFlag()) {
            Map<String, String> vendorMap = new HashMap<>();
            vendorMap.put("WELAB-AI", "tencent");
            vendorMap.put("tencent", "faceID");
            vendorMap.put("faceID", "WELAB-AI");
            List<FaceDetectionHistoryEntity> faceHistoryList = fd.getHistories();
            if(CollectionUtils.isNotEmpty(faceHistoryList)) {
                faceHistoryList.sort((v1, v2) -> v1.getCreateTime().compareTo(v2.getCreateTime()));
                int histSize = faceHistoryList.size();
                FaceDetectionHistoryEntity lastHist = faceHistoryList.get(histSize-1);
                int lastCode =lastHist.getCode();
                boolean callVendor = false;
                if((lastHist.getVendor().equals("WELAB-AI") && (lastCode == 300 || (lastCode>=600 && lastCode < 700)))
                    || (Arrays.asList("faceID", "tencent").contains(lastHist.getVendor()) && lastCode!=0)) {
                    String nextVendor = vendorMap.get(lastHist.getVendor());
                    if(!"WELAB-AI".equals(nextVendor)) {
                        callVendor = true;
                    } 
                } else if((lastHist.getVendor().equals("WELAB-AI") && !(lastCode == 300 || (lastCode>=600 && lastCode < 700)) && histSize >= appPropertyConfig.getFaceDetectionErrorLimit())) {
                    int limit = appPropertyConfig.getFaceDetectionErrorLimit();
                    if(limit==1){
                        callVendor = true;
                    } else if(limit>1){
                        int previous = histSize-2;
                        while(previous >= 0) {
                            FaceDetectionHistoryEntity previousHistory = faceHistoryList.get(previous);
                            if(previousHistory.getVendor().equals("WELAB-AI") && !(previousHistory.getCode() == 300 || (previousHistory.getCode()>=600 && previousHistory.getCode() < 700))) {
                                if((previous + limit) == histSize) {
                                    callVendor = true;
                                    break;
                                }
                            } else {
                                break;
                            }
                            previous--;
                        }
                    }
                }
                if(callVendor) {
                    String nextVendor = vendorMap.get(lastHist.getVendor());
                    String callbackUrl = appPropertyConfig.getFaceDetectionVendorCallbackUrl() + "?vendor=" + nextVendor + "&welabToken=" + token;
                    callbackUrl = UriUtils.encode(callbackUrl, "UTF-8");
                    String faceUrl = apiService.getFaceDetectionVendorReq(fd.getMobile(), nextVendor, callbackUrl);
                    if(StringUtils.isNotEmpty(faceUrl)) {
                        String orderNo = URLEncodedUtils.parse(URI.create(faceUrl), StandardCharsets.UTF_8).stream().filter(v -> v.getName().equals("orderNo")).findFirst().get().getValue();
                        data.put("faceUrl", faceUrl);
                        data.put("vendor", nextVendor);
                        data.put("orderNo", orderNo);
                    }
                }
            }
        }

        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "可以进行人脸验证", data);
    }

    @GetMapping("/photo")
    @ApiOperation(value = "获取用户底照")
    public ResponseModel getPhoto(@RequestHeader("FaceID-Token") String token, HttpServletResponse response) {
        FaceDetectionEntity fd = faceDetectionRepo.findByToken(token);
        JSONObject data = genData(token, fd);
        ResponseModel res = checkStatusByToken(token, fd, data);
        if (null != res) {
            return res;
        }
        String key = String.format(RedisKey.CS_FACE_DETECTION, token);
        String photo = null;
        if (!csStringRedisTemplate.opsForHash().hasKey(key, "photo") || StringUtils.isBlank(photo = (String) csStringRedisTemplate.opsForHash().get(key, "photo"))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "获取底照失败", data);
        }
        try (OSSObject ossObject = OssFileClient.getObject(appPropertyConfig.getOssBucketName(), photo)) {
            if (ossObject != null) {
                InputStream is = ossObject.getObjectContent();
                response.addHeader("Cache-Control", "max-age=1800");
                response.getOutputStream().write(CryptUtils.encryptAes(IOUtils.readStreamAsByteArray(is)));
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("oss get file error,token:{}", token, e);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "获取底照失败", data);
    }

    @PostMapping("/result")
    @ApiOperation(value = "保存人脸验证结果")
    public ResponseModel saveResult(@RequestHeader("FaceID-Token") String token, @RequestBody byte[] data) {
        FaceDetectionEntity fd = faceDetectionRepo.findByToken(token);
        if (null == fd) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到人脸验证的记录");
        }
        if (Objects.equals(fd.getCode(), 0)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "已经通过人脸验证，无需再次保存结果");
        }
        if (null == fd.getFailureTimes()) {
            fd.setFailureTimes(0);
        }
        if (fd.getFailureTimes() >= appPropertyConfig.getMaxFaceDetectFailureTimes()) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "失败次数达到最大值");
        }
        String str = new String(CryptUtils.decryptAes(data));
        JSONObject json = JSON.parseObject(str);
        Integer code = json.getInteger("code");
        String msg = json.getString("msg");
        String photo = json.getString("photo");

        LOGGER.info("保存人脸验证结果, token:{}, code:{}, msg:{}", token, code, msg);

        FaceDetectionHistoryEntity history = new FaceDetectionHistoryEntity();
        history.setVendor("WELAB-AI");
        //处理第三方结果
        String vendor = json.getString("vendor");
        String orderNo = json.getString("orderNo");
        if(null != vendor && null != orderNo) {
            JSONObject vendorData = apiService.getFaceDetectionVendorResult(fd.getMobile(), vendor, orderNo);
            if(null != vendorData) {
                String vendorCode = vendorData.getString("code");
                String imgBase64 = vendorData.getString("base64");
                if(StringUtils.isNotEmpty(imgBase64)) {
                    photo = imgBase64.replaceFirst("data:image/\\w{3,4};base64,", "");
                }
                msg = vendorData.getString("message");
                //第3方验证失败统一使用1001作为错误码
                code = vendorCode.equals("0") ? 0 : 1001;
                history.setVendorCode(vendorCode);
                history.setVendor(vendor);
                history.setOrderNo(orderNo);
            } else {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "获取第三方人脸验证结果失败");
            }
        }

        if (null != photo) {
            byte[] photoData = Base64.getDecoder().decode(photo);
            String fileDir = "cs/facedection/" + DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now()) + "/" + token + "/";
            String file = fileDir + "snapshot_" + code + "_" + UUID.randomUUID() + ".jpg";
            OssFileClient.putObject(appPropertyConfig.getOssBucketName(), file, new ByteArrayInputStream(photoData));
            history.setCompareFileId(Base64.getEncoder().encodeToString(file.getBytes()));
        }
        String key = String.format(RedisKey.CS_FACE_DETECTION, token);
        if (!Objects.equals(code, 0)) {
            fd.setFailureTimes(fd.getFailureTimes() + 1);
            if (csStringRedisTemplate.hasKey(key)) {
                csStringRedisTemplate.opsForHash().increment(key, "failureTimes", 1);
            }
        } else if (csStringRedisTemplate.hasKey(key)) {
            csStringRedisTemplate.opsForHash().put(key, "code", "0");
        }
        history.setCode(code);
        history.setCreateTime(new Date());
        history.setFaceDetection(fd);
        history.setMsg(msg);
        history.setOrgId(fd.getOrgId());
        history.setToken(token);

        fd.setCode(code);
        fd.setMsg(msg);
        fd.setVendor(history.getVendor());
        fd.setUpdateTime(history.getCreateTime());
        fd.getHistories().add(history);
        faceDetectionRepo.save(fd);

        processOnlineResult(fd);
        
        JSONObject infoData = genData(token, fd);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "保存结果成功", infoData);
    }


    private void processOnlineResult(FaceDetectionEntity fd) {
        if(null!=fd && "online".equals(fd.getSource())) {
            HashOperations<String, String, String> hashOperations = csStringRedisTemplate.opsForHash();
            String socketId = hashOperations.get(String.format(RedisKey.CS_USER_ONLINE_EMAIL_KEY, fd.getOrgId(), fd.getServiceUser()), "socketId");
            if (StringUtils.isNotBlank(socketId)) {
                String channel = String.format(RedisConstants.SOCKET_REQUEST_CHANNEL_PREFIX, SocketNamespace.CHAT);
                List<SocketPacketModel<String>> infoList = new ArrayList<>();
                
                Integer failTimes = faceDetectionRepo.getFailTimesBySessionId(fd.getSession().getId());
                JSONObject manual = new JSONObject().fluentPut("sessionKey", fd.getSession().getSessionKey()).fluentPut("faceDetectionResult", Objects.equals(fd.getCode(), 0) ? 1 : 2).fluentPut("failureTimes", failTimes);
                SocketPacketModel<String> notifyModel = new SocketPacketModel<String>(fd.getSession().getSessionKey(), null, SocketEvent.EVENT, 
                    new JSONObject().fluentPut("type", "facedetection:notify").fluentPut("time", System.currentTimeMillis()).fluentPut("manual", manual).toJSONString());
                infoList.add(notifyModel);
            
                ChatHistoryEntity chatHistory = saveChatHistory(fd.getSession(), fd.getCode(), fd.getMsg());
                infoList.add(new SocketPacketModel<String>(fd.getSession().getSessionKey(), null, SocketEvent.MESSAGE, MessageUtil.toJsonString(chatHistory)));
    
                csRedisTemplate.convertAndSend(channel, infoList);
            }
        }
    }

    private ChatHistoryEntity saveChatHistory(SessionListEntity sessionList, Integer code, String msg) {
        String content = code == 0 ? "人脸验证成功" : "人脸验证失败-"+msg;
        if (code >= 600 && code < 700) {
            content = "人脸验证失败-系统核验不通过";
        }
        Map<String, Object> manual = new HashMap<>();
        manual.put("code", code);
        manual.put("msg", msg);
        manual.put("sessionKey", sessionList.getSessionKey());
        ChatHistoryEntity chatHistory = chatHistoryService.createChatHistory(sessionList.getClientId(), sessionList.getClientTypeId(), "cs", "system", "event", "facedetection", null, 
                                            sessionList.getOrgId(), sessionList.getBotId(), sessionList.getBusinessId(), sessionList.getGcid(), content, null, null, manual, true);
        return chatHistory;
    }


    @GetMapping("/link")
    @ApiOperation(value = "获取人脸验证链接")
    public ResponseModel getLink(String mobile,String source) {
        if(StringUtils.isEmpty(mobile) || StringUtils.isEmpty(source)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "mobile, source不能为空");
        }
        if(!Arrays.asList("telephone").contains(source)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "source非法，暂不支持该种类型");
        }
       
        Optional<FaceDetectionEntity> faceOption = faceDetectionRepo.findFirstBySourceAndMobileOrderByCreateTimeDesc(source, mobile);
        String errMsg = null;
        if(faceOption.isPresent()) {
            FaceDetectionEntity face = faceOption.get();
            Date now = new Date();
            long intervalMinutes = (now.getTime() - face.getCreateTime().getTime())/(1000 * 60);
            if (Objects.equals(face.getCode(), 0) && intervalMinutes < appPropertyConfig.getFaceLinkIntervalMinutes()) {
                errMsg = "已发起人脸验证操作，验证成功，请勿重复操作";
            } else if ((face.getCode()==null || (face.getCode()!=0 && face.getFailureTimes()<appPropertyConfig.getMaxFaceDetectFailureTimes()))
                && intervalMinutes < appPropertyConfig.getFaceTokenTimeoutMinutes()){
                errMsg = "已发起人脸验证操作，请勿重复操作";
            }
        }
        if(null != errMsg){
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, errMsg);
        }
        
        try {
            String url = apiService.getFaceDocument(mobile);
            if(null == url) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "用户底照不存在");
            }
            InputStream is = Request.Get(url).execute().returnContent().asStream();
            String token = UUID.randomUUID().toString();
            String fileDir = "cs/facedection/" + DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now()) + "/" + token + "/";
            String file = fileDir + "origin_" + token + ".jpg";
            OssFileClient.putObject(appPropertyConfig.getOssBucketName(), file, is);
            String key = String.format(RedisKey.CS_FACE_DETECTION, token);
            csStringRedisTemplate.opsForHash().put(key, "failureTimes", "0");
            csStringRedisTemplate.opsForHash().put(key, "photo", file);
            csStringRedisTemplate.expire(key, Duration.ofMinutes(appPropertyConfig.getFaceTokenTimeoutMinutes()));
            FaceDetectionEntity fd = new FaceDetectionEntity();
            fd.setSource(source);
            fd.setMobile(mobile);
            fd.setToken(token);
            fd.setFileId(Base64.getEncoder().encodeToString(file.getBytes()));
            fd.setCreateTime(new Date());
            fd.setUpdateTime(fd.getCreateTime());
            faceDetectionRepo.save(fd);
            String faceLink = appPropertyConfig.getFaceDetectionUrl() + "?token=" + token;
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "获取人脸验证链接成功", faceLink);
        } catch (Exception e) {
            LOGGER.error("获取人脸验证链接失败, mobile:" + mobile + ", source:" + source, e);
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "获取人脸验证链接失败，请联系管理员");
        }
    }


    @GetMapping("/list")
    @ApiOperation(value = "获取人脸验证记录列表")
    public ResponseModel getResultList(String mobile,String source) {
        if(StringUtils.isEmpty(mobile) || StringUtils.isEmpty(source)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "mobile, source不能为空");
        }
        if(!Arrays.asList("telephone").contains(source)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "source非法，暂不支持该种类型");
        }

        List<JSONObject> resultList = new ArrayList<>();
        List<FaceDetectionEntity> faceList = faceDetectionRepo.findByMobileOrderByCreateTimeDesc(mobile);
        if(CollectionUtils.isNotEmpty(faceList)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            faceList.stream().forEach(v -> {
                JSONObject faceInfo = new JSONObject();
                faceInfo.put("token", v.getToken());
                faceInfo.put("source", v.getSource());
                faceInfo.put("group", "");
                faceInfo.put("service_user", "");
                if(null != v.getServiceUser()) {
                    UserStateEntity user = userStateRepo.findByOrgIdAndEmail(v.getOrgId(), v.getServiceUser());
                    faceInfo.put("group", "online");
                    faceInfo.put("service_user", user.getNickName());
                }
                faceInfo.put("create_time", sdf.format(v.getCreateTime()));
                int count = Objects.equals(v.getCode(), 0) ? v.getFailureTimes()+1 : v.getFailureTimes();
                faceInfo.put("count", count);
                int code = 0;
                if(null == v.getCode()) {
                    code = 2;
                } else if (v.getCode()!=0){
                    code = 1;
                }
                faceInfo.put("code", code);
                List<JSONObject> histList = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(v.getHistories())) {
                    v.getHistories().stream().forEach(h -> {
                        JSONObject history = new JSONObject();
                        history.put("verify_time",  sdf.format(h.getCreateTime()));
                        history.put("vendor", h.getVendor());
                        history.put("code", Objects.equals(h.getCode(), 0) ? 0 : 1);
                        history.put("msg",  Objects.equals(h.getCode(), 0) ? "" : h.getMsg());
                        histList.add(history);
                    });
                    histList.sort((v1, v2) -> v2.getString("verify_time").compareTo(v1.getString("verify_time")));
                }
                faceInfo.put("detail", histList);
                resultList.add(faceInfo);
            });
            resultList.sort((v1, v2) -> v2.getString("create_time").compareTo(v1.getString("create_time")));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "获取人脸验证记录成功", resultList);
    }

}
