#spring.jpa.hibernate.ddl-auto=create
server.port=9595
server.servlet.context-path=/api
spring.mvc.date-format=yyyyMMddHHmmss
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.main.allow-circular-references=true
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.serialization.write-date-keys-as-timestamps=false
spring.jackson.default-property-inclusion=NON_NULL
log.path=./logs
log.file=bot-server-connector.log
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.datasource.url=***************************************************************************
spring.datasource.username=root
spring.datasource.password=WvQQpZCEbRChwZ2BeUisoMRt
spring.jpa.open-in-view=true
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

spring.jpa.properties.hibernate.generate_statistics=false
spring.jpa.properties.hibernate.jdbc.batch_size=1000
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates =true

spring.data.mongodb.uri=*****************************************

smart.redis.bot.database=0
smart.redis.cs.database=1

spring.redis.url=redis://:Welabx20170808X@**********:6379

elastic.esClusterName=elasticsearch
elastic.esClusterNodes=**********:9301
elastic.esSecurityUser=
elastic.esXpackKeyPath=
elastic.esXpackKeyPassword=
spring.servlet.multipart.enabled=false

app.notifyFeedback=true
app.feedbackDetailUrl=https://xbot-dev.wld.net/feedbackDetail
app.webhookUrl=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1e06a69b-4e86-447b-b02d-a42957f858b1

app.envTest=true
app.fileServerUrl=https://webot-dev.wld.net/file
app.botEngineUrl=http://**********:9438/engine
app.faceDocumentsUrl=https://japi-fat.wld.net/application/api/v1/internal/face_documents
app.publicAccountUrl=https://japi-fat.wld.net/application/api/v1/internal/public_account_user
app.faceDetectionUrl=https://webot-dev.wld.net/faceid
app.satisfactionUrl=https://webot-dev.wld.net/chat/satisfaction

app.aliAccessKeyId=LTAI4Fk6qFgSWSyVZFFzH3Mo
app.aliAccessKeySecret=******************************
app.aliAppKey=B1NbmuHMGdpcSjXc

app.faceDetectionVendorFlag=true
app.faceDetectionVendorUrl=https://japi-fat.wolaidai.com/welab-ocr/api/v3/liveness/h5face
app.faceDetectionVendorResultUrl=https://japi-fat.wolaidai.com/welab-ocr/api/v3/liveness/result
app.faceDetectionVendorCallbackUrl=https://webot-dev.wld.net/faceid/vendor
app.faceDetectionErrorLimit=3

app.faceLinkIntervalMinutes=3
app.customerInfoUrl=https://crm-fat.welab-inc.com/welab-crm-interview/v1/user/queryByMobile
app.verifyUrl=https://webot-dev.wld.net/chat/verify

wx.open.authCallbackUrl=https://webot-dev.wld.net/connector/api/wx/auth/result
wx.open.authReturnUrl=https://xbot-dev.wld.net/robot/wechat
wx.open.componentAppId=wx44772f3cd26f6596
wx.open.componentSecret=257209196c55c88f106f634cd583a1b5
wx.open.componentToken=keyboardcat123
wx.open.componentAesKey=zYTTvzcRwzftFRA3e6dVcER8Bp1GbXXklUZkpwlSnTx

wx.mp.configs[0].appId=wx2a049409c1a0eeca
wx.mp.configs[0].secret=6431c8977e77a1ca7921552e66d63327
wx.mp.configs[0].token=a4dddf55-915b-4f23-90ec-fc1d142272fd
wx.mp.configs[0].aesKey=Welab@123

#old
app.docQaUrl=http://172.31.31.21:8032
#new
#app.docQaUrl=http://172.31.131.32:8032
app.docQaSearchDocId=52

welab.privacy.root.url=https://japi-fat.wolaidai.com/privacy/api/v2/config-info
welab.privacy.secret.key=N2qevU18ctVFJjJV
appId=bot-server-connector

app.mongoEncUrl=https://japi-fat.wolaidai.com/privacy/api/v2/config-info
app.mongoEncAppId=bot-server-connector
app.mongoEncSecret=N2qevU18ctVFJjJV
app.mongoEncDb=webot-v2

app.aiSummaryUrl=http://10.90.0.5:8888/llm/v1/chat/summarize
app.csProductId=3
app.summaryWebhookUrl=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4c65ee4f-916d-49dc-9e17-f35c11d6bf2c
app.kefuUrlPre = https://crm-fat.welab-inc.com/welab-crm-interview/
app.testUserMobile=13077973938

# 阿里云智能对话分析服务质量检查配置
app.qualityInspectionEnabled=true
# 阿里云UploadDataV4 API接口地址
app.qualityInspectionSubmitUrl=https://qualitycheck.cn-shanghai.aliyuncs.com
# 阿里云质量检查结果查询API接口地址（根据实际API调整）
app.qualityInspectionQueryUrl=https://qualitycheck.cn-shanghai.aliyuncs.com