#spring.jpa.hibernate.ddl-auto=create
server.port=9595
server.servlet.context-path=/api
spring.mvc.date-format=yyyyMMddHHmmss
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.main.allow-circular-references=true
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.serialization.write-date-keys-as-timestamps=false
log.path=./logs
log.file=webot-socket-server.log
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.datasource.url=***************************************************************************
spring.datasource.username=root
spring.datasource.password=WvQQpZCEbRChwZ2BeUisoMRt
spring.jpa.open-in-view=true
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

spring.jpa.properties.hibernate.generate_statistics=false
spring.jpa.properties.hibernate.jdbc.batch_size=1000
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates =true

spring.data.mongodb.uri=*****************************************

smart.redis.bot.database=0
smart.redis.cs.database=1

spring.redis.url=redis://:Welabx20170808X@**********:6379

elastic.esClusterName=elasticsearch
elastic.esClusterNodes=**********:9301
elastic.esSecurityUser=
elastic.esXpackKeyPath=
elastic.esXpackKeyPassword=
spring.servlet.multipart.enabled=false

app.envTest=true
app.fileServerUrl=https://webot-dev.wld.net/file
app.replyUrl=http://**********:9438/engine/getReply

app.aliAccessKeyId=LTAI4Fk6qFgSWSyVZFFzH3Mo
app.aliAccessKeySecret=******************************
app.aliAppKey=B1NbmuHMGdpcSjXc