package socket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.connector.WebotConnectorApplication;
import com.wolaidai.webot.connector.constant.SocketEvent;
import com.wolaidai.webot.connector.model.inner.SessionBaseModel;
import com.wolaidai.webot.connector.model.inner.SessionExitModel;
import com.wolaidai.webot.connector.service.business.ChatRecordService;
import com.wolaidai.webot.connector.service.event.chat.inner.SessionJoinEventService;
import com.wolaidai.webot.connector.utils.MessageUtil;
import com.wolaidai.webot.data.mysql.entity.chat.*;
import com.wolaidai.webot.data.mysql.repo.ChatRecordRepo;
import com.wolaidai.webot.data.mysql.repo.ChatRoomRepo;
import com.wolaidai.webot.data.mysql.repo.ChatSessionRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = WebotConnectorApplication.class)
public class SocketInterfaceTest {

    @Autowired
    private ChatRoomRepo chatRoomRepo;
    @Autowired
    private ChatSessionRepo chatSessionRepo;
    @Autowired
    private UserStateRepo userStateRepo;
    @Autowired
    private ChatRecordService chatRecordService;

    @Test
    public void socketEventTest() {
        ChatRoomEntity chatRoomEntity = chatRoomRepo.findById(9).orElse(null);
        UserStateEntity byOrgIdAndEmail = userStateRepo.findByOrgIdAndEmail(1, "<EMAIL>");
        chatRoomEntity.getRoomUsers().add(new ChatRoomUsersEntity(chatRoomEntity, byOrgIdAndEmail));
        chatRoomEntity.setUpdateTime(new Date());
        chatRoomRepo.save(chatRoomEntity);
        System.out.println(JSONObject.toJSONString(chatRoomEntity));
    }

    @Test
    public void serviceTest() {
        String data = "{\"type\":\"inner:session:cut\",\"manual\":{\"sessionKey\":26,\"email\":\"<EMAIL>\"}}";
        org.json.JSONObject data1 = new org.json.JSONObject(data);
        org.json.JSONObject manual = data1.getJSONObject("manual");
        SessionBaseModel baseModel = JSON.parseObject(manual.toString(), SessionExitModel.class);
        ChatSessionEntity sessionEntity = chatSessionRepo.findById(baseModel.getSessionKey()).orElse(null);
        ChatRecordEntity chatRecord = chatRecordService.createChatRecord("1111111111",
                "<EMAIL>", "<EMAIL>", "测试蹦跶",
                "text", sessionEntity, null, null, new JSONObject().fluentPut("sessionKey", 22)
                        .fluentPut("email", "<EMAIL>").fluentPut("nickName", "南波湾").fluentPut("workNumber", "9527"));
        chatRecord.setEventKey(SocketEvent.INNER_SESSION_RELOAD_TYPE);
        System.out.println(MessageUtil.toJsonString(chatRecord));
    }
}
