<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>webot-app</artifactId>
        <groupId>com.wolaidai</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bot-server-connector</artifactId>
    <description>webot connector server</description>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.nls</groupId>
            <artifactId>nls-sdk-common</artifactId>
            <version>2.1.8</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.6.6</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jetty</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.5</version>
        </dependency>
        <dependency>
            <groupId>com.wolaidai</groupId>
            <artifactId>webot-data-mysql</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wolaidai</groupId>
            <artifactId>webot-data-redis</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wolaidai</groupId>
            <artifactId>webot-data-mongodb</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.wolaidai</groupId>-->
<!--            <artifactId>webot-data-elasticsearch</artifactId>-->
<!--            <version>${project.parent.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.wolaidai</groupId>
            <artifactId>webot-common</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.17.3</version>
        </dependency>
        <dependency>
            <groupId>io.socket</groupId>
            <artifactId>socket.io-server</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>io.socket</groupId>
            <artifactId>engine.io-server</artifactId>
            <version>6.2.1</version>
        </dependency>
        <dependency>
            <groupId>io.socket</groupId>
            <artifactId>engine.io-server-jetty</artifactId>
            <version>6.2.1</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20220320</version>
        </dependency>
        <dependency>
            <groupId>ws.schild</groupId>
            <artifactId>jave-core</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>ws.schild</groupId>
            <artifactId>jave-nativebin-linux64</artifactId>
            <version>3.2.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>ws.schild</groupId>-->
<!--            <artifactId>jave-nativebin-osx64</artifactId>-->
<!--            <version>3.2.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-open</artifactId>
            <version>4.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
            <version>4.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.welab</groupId>
            <artifactId>welab-privacy-api</artifactId>
            <version>1.1.0-RELEASE</version>
        </dependency>
    </dependencies>



    <build>
        <finalName>bot-server-connector</finalName>
    </build>
</project>