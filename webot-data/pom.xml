<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wolaidai</groupId>
        <artifactId>webot</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>webot-data</artifactId>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>

    <modules>
        <module>webot-data-mysql</module>
        <module>webot-data-redis</module>
        <module>webot-data-elasticsearch</module>
        <module>webot-data-mongodb</module>
    </modules>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
