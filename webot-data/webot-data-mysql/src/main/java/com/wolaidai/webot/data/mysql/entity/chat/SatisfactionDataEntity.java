package com.wolaidai.webot.data.mysql.entity.chat;

import com.alibaba.fastjson.JSONArray;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "satisfaction_data")
//用户满意度表
public class SatisfactionDataEntity extends BaseEntity {
    //未评价
    public static final Integer STATUS_UNTREATED = 0;
    //已评价
    public static final Integer STATUS_TREATED = 1;

    //系统发起类型
    public static final Integer TYPE_SYSTEM = 1;
    //客服发起类型
    public static final Integer TYPE_SERVICEUSER = 2;
    //用户发起类型
    public static final Integer TYPE_CUSTOMER = 3;

    private String clientId;
    private Integer clientTypeId;
    private String origin;
    @ManyToOne(fetch = FetchType.LAZY)
    private SessionListEntity session;
    //评级
    private Integer level;
    //标签
    @Type( type = "json" )
    @Column( columnDefinition = "json" )
    private JSONArray labels;
    //评价内容
    private String content;
    private Integer orgId;
    //用户评价状态，0：未评价；1：已评价
    private Integer status;
    //发起类型，1：系统发起；2：客服发起
    private Integer type;
    //客服主动发起email
    private String serviceUser;
    private Date createTime;
    private Date updateTime;

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Integer getClientTypeId() {
        return clientTypeId;
    }

    public void setClientTypeId(Integer clientTypeId) {
        this.clientTypeId = clientTypeId;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public SessionListEntity getSession() {
        return session;
    }

    public void setSession(SessionListEntity session) {
        this.session = session;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public JSONArray getLabels() {
        return labels;
    }

    public void setLabels(JSONArray labels) {
        this.labels = labels;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getServiceUser() {
        return serviceUser;
    }

    public void setServiceUser(String serviceUser) {
        this.serviceUser = serviceUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
