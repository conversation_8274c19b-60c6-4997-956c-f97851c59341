package com.wolaidai.webot.data.mysql.entity.bot;

import com.wolaidai.webot.data.mysql.constant.Database;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import java.util.Date;
import java.util.Objects;
import java.util.Set;

@Entity
@Table(name="skill",catalog = Database.QA_BOT)
public class SkillEntity extends BaseEntity {
    public static final int ACTIVE_STATUS=1;
    public static final int DISABLE_STATUS=0;
    public static final int TASK_TYPE=1;
    public static final int QA_TYPE=2;
    public static final int OPERATION_TYPE=3;
    public static final int PRESET_TYPE=4;
    public static final int DOC_QA_TYPE=5;

    private String name;
    private Integer type;
    private Integer presetType;
    private Integer status;
    private Integer orgId;
    private Integer deptId;
    @OneToMany(mappedBy = "skill", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("createTime desc")
    private Set<SkillSkillGroupEntity> skillSkillGroups;

    private Integer position;
    private String creator;
    private String remark;
    private Date createTime;
    private Date updateTime;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getPresetType() {
        return presetType;
    }

    public void setPresetType(Integer presetType) {
        this.presetType = presetType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getDeptId() {
		return deptId;
	}

	public void setDeptId(Integer deptId) {
		this.deptId = deptId;
	}

	public Set<SkillSkillGroupEntity> getSkillSkillGroups() {
        return skillSkillGroups;
    }

    public void setSkillSkillGroups(Set<SkillSkillGroupEntity> skillSkillGroups) {
        this.skillSkillGroups = skillSkillGroups;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SkillEntity that = (SkillEntity) o;
        return getId().equals(that.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId());
    }
}
