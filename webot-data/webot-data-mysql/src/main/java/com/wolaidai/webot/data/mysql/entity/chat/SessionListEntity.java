package com.wolaidai.webot.data.mysql.entity.chat;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import org.hibernate.annotations.Type;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "session_list")
//会话表
public class SessionListEntity extends BaseEntity {
    
    //初始状态
    public final static Integer STATUS_INIT = 0;
    //在线状态
    public final static Integer STATUS_ONLINE = 1;
    //离线状态
    public final static Integer STATUS_OFFLINE = 2;

    //自动分配
    public final static Integer ASSIGN_TYPE_AUTO = 1;
    //邀请分配
    public final static Integer ASSIGN_TYPE_INVITE = 2;

    //会话客户超时关闭
    public final static Integer CLOSE_TYPE_CUSTOMER_TIMEOUT = 1;
    //会话客服手动关闭
    public final static Integer CLOSE_TYPE_MANUAL = 2;
    //会话客服超时关闭
    public final static Integer CLOSE_TYPE_CS_TIMEOUT = 3;
    //会话用户手动关闭
    public final static Integer CLOSE_TYPE_CUSTOMER = 4;

    public final static Integer USER_SENDER = 1;
    public final static Integer MANUAL_SENDER = 2;

    //客户id
    private String clientId;
    //客户类型
    private Integer clientTypeId;
    //客户渠道来源
    private String origin;
    //首次客服email
    private String serviceUser;
    //来源客服email，首次为空
    private String fromServiceUser;
    //最后接待客服email
    private String lastServiceUser;
    private Integer botId;
    //全局会话ID
    private String gcid;
    //全局会话启动时间
    private Date gcTime;
    @OneToOne(fetch = FetchType.LAZY)
    private QueueListEntity fromQueue;
    //会话唯一标识
    private String sessionKey;
    private Integer status;
    //业务ID
    private Integer businessId;
    //业务名
    private String businessName;
    //客户姓名
    private String customerName;
    //客户类型，0:普通用户；1：VIP用户
    private Integer customerType;
    @Type( type = "json" )
    @Column( columnDefinition = "json" )
    private JSONObject customerDetail;
    private Integer orgId;
    //是否被标星，0：未标记，1：被标记
    private Integer mark = 0;
    //客服未读消息数
    private Integer unreadMsgCount = 0;
    private Integer satisfactionLevel;
    private Date createTime;
    private Date updateTime;
    //最后一条消息
    private String lastMsg;
    //最后消息时间
    private Date lastMsgTime;
    //0:system;1:user;2:manual;首次欢迎语为null
    private Integer lastMsgSender;
    //首次响应超时,0:未超时；1:超时
    private Integer firstRespTimeout = 0;
    //客服首次响应毫秒数
    private Integer serviceFirstRespTime;
    //任一响应超时,0:未超时；1:超时
    private Integer respTimeout = 0;
    //客服平均响应毫秒数
    private Integer avgRespTime;
    //客服平均响应超时
    private Integer avgRespTimeout = 0;
    //客户最早回复消息时间，客服回复后清空
    private Date customerFirstReplyTime;
    //客户最新回复消息时间
    private Date customerLastReplyTime;
    //开始排队时间
    private Date queueTime;
    //离线时间
    private Date offlineTime;
    //会话等待时间
    private Long waitSecond;
    //会话持续时间
    private Long durationSecond;
    //分配类型：1：自动分配；2：邀请分配
    private Integer assignType;
    //结束方式：1：客户超时下线；2：客服主动结束会话
    private Integer closeType;
    @Type( type = "json" )
    @Column( columnDefinition = "json" )
    private JSONArray transferToUsers = new JSONArray();
    //服务总结状态:1-已总结, 0-未总结
    private Integer serviceSummaryStatus = 0;
    //质量检查状态:0-未检查, 1-检查中, 2-已完成, 3-检查失败
    private Integer qualityInspectionStatus = 0;
    //质量检查分数
    private Integer qualityInspectionScore;
    //质量检查结果详情(JSON格式)
    private String qualityInspectionResult;
    //质量检查完成时间
    private Date qualityInspectionTime;
    //质量检查任务ID
    private String qualityInspectionTaskId;

    //会话转接列表
    @OneToMany(mappedBy = "session", cascade = CascadeType.ALL)
    @OrderBy("id DESC")
    private List<SessionTransferListEntity> sessionTransferList = new ArrayList<>();

    //服务小结
    @OneToMany(mappedBy = "session", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ServiceSummaryEntity> serviceSummaryList = new ArrayList<>();

    //满意度
    @OneToMany(mappedBy = "session", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<SatisfactionDataEntity> satisfactionDataList = new ArrayList<>();

    @OneToMany(mappedBy = "session", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<UserVerification> userVerificationList = new ArrayList<>();

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Integer getClientTypeId() {
        return clientTypeId;
    }

    public void setClientTypeId(Integer clientTypeId) {
        this.clientTypeId = clientTypeId;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getServiceUser() {
        return serviceUser;
    }

    public void setServiceUser(String serviceUser) {
        this.serviceUser = serviceUser;
    }

    public String getFromServiceUser() {
        return fromServiceUser;
    }

    public void setFromServiceUser(String fromServiceUser) {
        this.fromServiceUser = fromServiceUser;
    }

    public String getLastServiceUser() {
        return lastServiceUser;
    }

    public void setLastServiceUser(String lastServiceUser) {
        this.lastServiceUser = lastServiceUser;
    }

    public Integer getBotId() {
        return botId;
    }

    public void setBotId(Integer botId) {
        this.botId = botId;
    }

    public String getGcid() {
        return gcid;
    }

    public void setGcid(String gcid) {
        this.gcid = gcid;
    }

    public Date getGcTime() {
        return gcTime;
    }

    public void setGcTime(Date gcTime) {
        this.gcTime = gcTime;
    }

    public QueueListEntity getFromQueue() {
        return fromQueue;
    }

    public void setFromQueue(QueueListEntity fromQueue) {
        this.fromQueue = fromQueue;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public JSONObject getCustomerDetail() {
        return customerDetail;
    }

    public void setCustomerDetail(JSONObject customerDetail) {
        this.customerDetail = customerDetail;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getMark() {
        return mark;
    }

    public void setMark(Integer mark) {
        this.mark = mark;
    }

    public Integer getUnreadMsgCount() {
        return unreadMsgCount;
    }

    public void setUnreadMsgCount(Integer unreadMsgCount) {
        this.unreadMsgCount = unreadMsgCount;
    }

    public Integer getSatisfactionLevel() {
        return satisfactionLevel;
    }

    public void setSatisfactionLevel(Integer satisfactionLevel) {
        this.satisfactionLevel = satisfactionLevel;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getLastMsg() {
        return lastMsg;
    }

    public void setLastMsg(String lastMsg) {
        this.lastMsg = lastMsg;
    }

    public Date getLastMsgTime() {
        return lastMsgTime;
    }

    public void setLastMsgTime(Date lastMsgTime) {
        this.lastMsgTime = lastMsgTime;
    }

    public Integer getLastMsgSender() {
        return lastMsgSender;
    }

    public void setLastMsgSender(Integer lastMsgSender) {
        this.lastMsgSender = lastMsgSender;
    }

    public Integer getFirstRespTimeout() {
        return firstRespTimeout;
    }

    public void setFirstRespTimeout(Integer firstRespTimeout) {
        this.firstRespTimeout = firstRespTimeout;
    }

    public Integer getServiceFirstRespTime() {
        return serviceFirstRespTime;
    }

    public void setServiceFirstRespTime(Integer serviceFirstRespTime) {
        this.serviceFirstRespTime = serviceFirstRespTime;
    }

    public Integer getRespTimeout() {
        return respTimeout;
    }

    public void setRespTimeout(Integer respTimeout) {
        this.respTimeout = respTimeout;
    }

    public Integer getAvgRespTime() {
        return avgRespTime;
    }

    public void setAvgRespTime(Integer respAvgReplyTime) {
        this.avgRespTime = respAvgReplyTime;
    }

    public Integer getAvgRespTimeout() {
        return avgRespTimeout;
    }

    public void setAvgRespTimeout(Integer avgRespTimeout) {
        this.avgRespTimeout = avgRespTimeout;
    }

    public Date getCustomerFirstReplyTime() {
        return customerFirstReplyTime;
    }

    public void setCustomerFirstReplyTime(Date customerFirstReplyTime) {
        this.customerFirstReplyTime = customerFirstReplyTime;
    }

    public Date getCustomerLastReplyTime() {
        return customerLastReplyTime;
    }

    public void setCustomerLastReplyTime(Date customerLastReplyTime) {
        this.customerLastReplyTime = customerLastReplyTime;
    }

    public Date getQueueTime() {
        return queueTime;
    }

    public void setQueueTime(Date queueTime) {
        this.queueTime = queueTime;
    }

    public Date getOfflineTime() {
        return offlineTime;
    }

    public void setOfflineTime(Date offlineTime) {
        this.offlineTime = offlineTime;
    }

    public Long getWaitSecond() {
        return waitSecond;
    }

    public void setWaitSecond(Long waitSecond) {
        this.waitSecond = waitSecond;
    }

    public Long getDurationSecond() {
        return durationSecond;
    }

    public void setDurationSecond(Long durationSecond) {
        this.durationSecond = durationSecond;
    }

    public JSONArray getTransferToUsers() {
        return transferToUsers;
    }

    public void setTransferToUsers(JSONArray transferToUsers) {
        this.transferToUsers = transferToUsers;
    }

    public List<SessionTransferListEntity> getSessionTransferList() {
        return sessionTransferList;
    }

    public void setSessionTransferList(List<SessionTransferListEntity> sessionTransferList) {
        this.sessionTransferList = sessionTransferList;
    }

    public ServiceSummaryEntity getServiceSummary() {
        if(!CollectionUtils.isEmpty(serviceSummaryList)) {
            return serviceSummaryList.get(0);
        }
        return null;
    }

    public void setServiceSummary(ServiceSummaryEntity serviceSummary) {
        if(serviceSummary!=null){
            serviceSummaryList.clear();
            serviceSummaryList.add(serviceSummary);
        }
    }

    public SatisfactionDataEntity getSatisfactionData() {
        if(!CollectionUtils.isEmpty(satisfactionDataList)) {
            return satisfactionDataList.get(0);
        }
        return null;
    }

    public void setSatisfactionData(SatisfactionDataEntity satisfactionData) {
        if(satisfactionData!=null){
            satisfactionDataList.clear();
            satisfactionDataList.add(satisfactionData);
        }
    }

    public List<ServiceSummaryEntity> getServiceSummaryList() {
        return serviceSummaryList;
    }

    public void setServiceSummaryList(List<ServiceSummaryEntity> serviceSummaryList) {
        this.serviceSummaryList = serviceSummaryList;
    }

    public List<SatisfactionDataEntity> getSatisfactionDataList() {
        return satisfactionDataList;
    }

    public void setSatisfactionDataList(List<SatisfactionDataEntity> satisfactionDataList) {
        this.satisfactionDataList = satisfactionDataList;
    }

    public Integer getAssignType() {
        return assignType;
    }

    public void setAssignType(Integer assignType) {
        this.assignType = assignType;
    }

    public Integer getCloseType() {
        return closeType;
    }

    public void setCloseType(Integer closeType) {
        this.closeType = closeType;
    }

    public Integer getServiceSummaryStatus() {
        return serviceSummaryStatus;
    }

    public void setServiceSummaryStatus(Integer serviceSummaryStatus) {
        this.serviceSummaryStatus = serviceSummaryStatus;
    }

    public List<UserVerification> getUserVerificationList() {
        return userVerificationList;
    }

    public void setUserVerificationList(List<UserVerification> userVerificationList) {
        this.userVerificationList = userVerificationList;
    }

    public Integer getQualityInspectionStatus() {
        return qualityInspectionStatus;
    }

    public void setQualityInspectionStatus(Integer qualityInspectionStatus) {
        this.qualityInspectionStatus = qualityInspectionStatus;
    }

    public Integer getQualityInspectionScore() {
        return qualityInspectionScore;
    }

    public void setQualityInspectionScore(Integer qualityInspectionScore) {
        this.qualityInspectionScore = qualityInspectionScore;
    }

    public String getQualityInspectionResult() {
        return qualityInspectionResult;
    }

    public void setQualityInspectionResult(String qualityInspectionResult) {
        this.qualityInspectionResult = qualityInspectionResult;
    }

    public Date getQualityInspectionTime() {
        return qualityInspectionTime;
    }

    public void setQualityInspectionTime(Date qualityInspectionTime) {
        this.qualityInspectionTime = qualityInspectionTime;
    }

    public String getQualityInspectionTaskId() {
        return qualityInspectionTaskId;
    }

    public void setQualityInspectionTaskId(String qualityInspectionTaskId) {
        this.qualityInspectionTaskId = qualityInspectionTaskId;
    }
}
