package com.wolaidai.webot.data.mysql.repo;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.wolaidai.webot.data.mysql.entity.config.QaCommonConfigEntity;

public interface BotCommonConfigRepo extends BaseRepo<QaCommonConfigEntity, Integer> {

    @Query(value = "select * from qa_bot.common_config where (org_id=?1 or org_id=-1) and type=?2 order by org_id desc limit 1", nativeQuery = true)
    QaCommonConfigEntity findOneByOrgIdAndType(Integer orgId, String type);
    @Query(value = "select * from qa_bot.common_config where org_id=?1 and type=?2 order by org_id desc limit 1", nativeQuery = true)
    QaCommonConfigEntity checkByOrgIdAndType(Integer orgId, String type);
    
    @Query(value = "select * from qa_bot.common_config where (org_id=?1 or org_id=-1) and type=?2 order by org_id asc", nativeQuery = true)
    List<QaCommonConfigEntity> findByOrgIdAndType(Integer orgId, String type);
    
    @Query(value = "select * from qa_bot.common_config where (org_id=?1 or org_id=-1) and type=?2 and sub_type1=?3 order by org_id desc limit 1", nativeQuery = true)
    QaCommonConfigEntity findOneByOrgIdAndTypeAndSubType1(Integer orgId, String type, String subType1);
    @Query(value = "select * from qa_bot.common_config where org_id=?1 and type=?2 and sub_type1=?3 order by org_id desc limit 1", nativeQuery = true)
    QaCommonConfigEntity checkByOrgIdAndTypeAndSubType1(Integer orgId, String type, String subType1);
    
    @Query(value = "select * from qa_bot.common_config where (org_id=?1 or org_id=-1) and type=?2 and sub_type1=?3 order by org_id asc", nativeQuery = true)
    List<QaCommonConfigEntity> findByOrgIdAndTypeAndSubType1(Integer orgId, String type, String subType1);
    
    @Query(value = "select * from qa_bot.common_config where (org_id=?1 or org_id=-1) and type=?2 and sub_type1=?3 and sub_type2=?4 order by org_id desc limit 1", nativeQuery = true)
    QaCommonConfigEntity findOneByOrgIdAndTypeAndSubType1AndSubType2(Integer orgId, String type, String subType1, String subType2);
    @Query(value = "select * from qa_bot.common_config where org_id=?1 and type=?2 and sub_type1=?3 and sub_type2=?4 order by org_id desc limit 1", nativeQuery = true)
    QaCommonConfigEntity checkByOrgIdAndTypeAndSubType1AndSubType2(Integer orgId, String type, String subType1, String subType2);
    
    @Query(value = "select * from qa_bot.common_config where (org_id=?1 or org_id=-1) and type=?2 and sub_type1=?3 and sub_type2=?4 order by org_id asc", nativeQuery = true)
    List<QaCommonConfigEntity> findByOrgIdAndTypeAndSubType1AndSubType2(Integer orgId, String type, String subType1, String subType2);
}
