package com.wolaidai.webot.data.mysql.entity.config;

import java.util.Arrays;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;

@Entity
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
public class CombineCommonConfigEntity {
    public final static String TYPE_WORKTIME_CONFIG = "worktime";
    public final static String TYPE_WORKBENCH_CONFIG = "workbench";
    public final static String TYPE_WORKBENCH_CONFIG_STATUS = "workbench_status";
    public final static String TYPE_SERVICEUSER_CONFIG = "serviceuser";
    public final static String TYPE_AUTOREPLY_CONFIG = "autoreply";
    public final static String TYPE_SATISFACTION_CONFIG = "satisfaction";
    public final static String TYPE_RESP_TIMEOUT_CONFIG = "respTimeout";

    public final static String TYPE_BOT_WORKTIME_CONFIG = "botworktime";
    public final static String TYPE_WECHAT_QUESTION_REC_CONFIG = "wechatQuestionRec";
    public final static String TYPE_TURN_ARTIFICIAL_POLICY_CONFIG = "turnArtificialPolicy";
    public final static String TYPE_DEFAULT_REPLY_CONFIG = "defaultreply";
    public final static String TYPE_BOT_MENU_LIST_CONFIG = "botmenu";
    public final static String TYPE_LOW_SCORE = "lowScore";
    public final static String TYPE_HIGH_SCORE = "highScore";
    public final static String TYPE_CHITCHAT_SCORE = "chitchatScore";

    public static boolean isBotType(String type) {
        if (null == type) {
            return false;
        }
        return Arrays.asList(TYPE_BOT_WORKTIME_CONFIG, TYPE_WECHAT_QUESTION_REC_CONFIG, TYPE_TURN_ARTIFICIAL_POLICY_CONFIG, TYPE_DEFAULT_REPLY_CONFIG, TYPE_BOT_MENU_LIST_CONFIG, TYPE_LOW_SCORE, TYPE_HIGH_SCORE, TYPE_CHITCHAT_SCORE).contains(type);
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private Integer orgId = -1;
    private String type;
    private String subType1;
    private String subType2;
    private String content;
    private Date createTime;
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSubType1() {
        return subType1;
    }

    public void setSubType1(String subType1) {
        this.subType1 = subType1;
    }

    public String getSubType2() {
        return subType2;
    }

    public void setSubType2(String subType2) {
        this.subType2 = subType2;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}
