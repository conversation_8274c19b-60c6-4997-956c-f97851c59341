package com.wolaidai.webot.data.mysql.entity.docqa;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Type;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.data.mysql.constant.Database;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import com.wolaidai.webot.data.mysql.entity.bot.SkillEntity;

@Entity
@Table(name="doc_qa_release", catalog = Database.QA_BOT)
public class DocQaReleaseEntity extends BaseEntity {
    public static final int PROCESSING_STATUS=2;
    public static final int SUCCESS_STATUS=3;
    public static final int FAIL_STATUS=4;

    private String seqCode;
    private Integer orgId;
    @ManyToOne(fetch = FetchType.LAZY)
    private SkillEntity skill;
    private Integer status;
    private String creator;
    private String remark;
    @Type( type = "json" )
    @Column( columnDefinition = "json" )
    private JSONArray allDoc; 
    @Type( type = "json" )
    @Column( columnDefinition = "json" )
    private JSONObject changeDoc;
    private String errorMsg;
    private Date createTime;
    private Date updateTime;
    
    public String getSeqCode() {
        return seqCode;
    }
    public void setSeqCode(String seqCode) {
        this.seqCode = seqCode;
    }
    public Integer getOrgId() {
        return orgId;
    }
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
    public SkillEntity getSkill() {
        return skill;
    }
    public void setSkill(SkillEntity skill) {
        this.skill = skill;
    }
    public Integer getStatus() {
        return status;
    }
    public void setStatus(Integer status) {
        this.status = status;
    }
    public String getCreator() {
        return creator;
    }
    public void setCreator(String creator) {
        this.creator = creator;
    }
    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public JSONArray getAllDoc() {
        return allDoc;
    }
    public void setAllDoc(JSONArray allDoc) {
        this.allDoc = allDoc;
    }
    public JSONObject getChangeDoc() {
        return changeDoc;
    }
    public void setChangeDoc(JSONObject changeDoc) {
        this.changeDoc = changeDoc;
    }
    public String getErrorMsg() {
        return errorMsg;
    }
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
    public Date getCreateTime() {
        return createTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    
}
