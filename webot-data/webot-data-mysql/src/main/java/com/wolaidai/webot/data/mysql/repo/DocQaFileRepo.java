package com.wolaidai.webot.data.mysql.repo;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.wolaidai.webot.data.mysql.entity.docqa.DocQaFileEntity;
import java.util.List;


public interface DocQaFileRepo extends CrudRepository<DocQaFileEntity,Integer>{

    List<DocQaFileEntity> findByOrgIdAndSkillId(Integer orgId, Integer skillId);

    @Query(value = "select * from qa_bot.doc_qa_file where id in ?1 and json_contains(json_extract(label,'$'),json_array(?2))",nativeQuery = true)
    List<DocQaFileEntity> findByDocIdAndLabel(List<Integer> idList, String label);




}
