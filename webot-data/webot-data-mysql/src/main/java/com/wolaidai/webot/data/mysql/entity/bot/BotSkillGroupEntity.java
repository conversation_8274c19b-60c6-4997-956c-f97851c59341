package com.wolaidai.webot.data.mysql.entity.bot;

import com.wolaidai.webot.data.mysql.constant.Database;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name="bot__skill_group",catalog = Database.QA_BOT)
public class BotSkillGroupEntity {

    @EmbeddedId
    private BotSkillGroupIdEntity id;

    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("botId")
    private BotEntity bot;

    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("skillGroupId")
    private SkillGroupEntity skillGroup;

    private Integer position = 0;

    private Date createTime;

    public BotSkillGroupEntity() {
    }

    public BotSkillGroupIdEntity getId() {
        return id;
    }

    public void setId(BotSkillGroupIdEntity id) {
        this.id = id;
    }

    public BotEntity getBot() {
        return bot;
    }

    public void setBot(BotEntity bot) {
        this.bot = bot;
    }

    public SkillGroupEntity getSkillGroup() {
        return skillGroup;
    }

    public void setSkillGroup(SkillGroupEntity skillGroup) {
        this.skillGroup = skillGroup;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BotSkillGroupEntity that = (BotSkillGroupEntity) o;
        return bot.equals(that.bot) &&
                skillGroup.equals(that.skillGroup);
    }

    @Override
    public int hashCode() {
        return Objects.hash(bot, skillGroup);
    }
}
