package com.wolaidai.webot.data.mysql.entity.chat;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "user_verification_history")
public class UserVerificationHistory extends BaseEntity {

    public static final Integer STATUS_NOT_VERIFIED = 0;
    public static final Integer STATUS_VERIFIED = 1;
    public static final Integer STATUS_VERIFY_FAILED = 2;

    private String mobile;

    private String idLast4;

    private String name;

    private Integer status;

    private String msg;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "verify_id")
    private UserVerification userVerification;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getIdLast4() {
        return idLast4;
    }

    public void setIdLast4(String idLast4) {
        this.idLast4 = idLast4;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public UserVerification getUserVerification() {
        return userVerification;
    }

    public void setUserVerification(UserVerification userVerification) {
        this.userVerification = userVerification;
    }
}
