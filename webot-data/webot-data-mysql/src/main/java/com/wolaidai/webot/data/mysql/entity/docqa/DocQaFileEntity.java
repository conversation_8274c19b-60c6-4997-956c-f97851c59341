package com.wolaidai.webot.data.mysql.entity.docqa;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Type;

import com.alibaba.fastjson.JSONArray;
import com.wolaidai.webot.data.mysql.constant.Database;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import com.wolaidai.webot.data.mysql.entity.bot.SkillEntity;

@Entity
@Table(name="doc_qa_file",catalog = Database.QA_BOT)
public class DocQaFileEntity extends BaseEntity {

    public static final int NORMAL_STATUS=1;
    public static final int DELETED_STATUS=0;

    private Integer orgId;

    @ManyToOne(fetch = FetchType.LAZY)
    private SkillEntity skill;

    private String owner;

    private String fileId;

    private String filePath;

    private String txtFilePath;

    private String fileName;

    private Long fileSize;

    private String hash;

    private Integer status;

    private Date createTime;

    private Date updateTime;

    @Type( type = "json" )
    @Column( columnDefinition = "json" )
    private JSONArray label; 

    public Integer getOrgId() {
      return orgId;
    }

    public void setOrgId(Integer orgId) {
      this.orgId = orgId;
    }

    public SkillEntity getSkill() {
      return skill;
    }

    public void setSkill(SkillEntity skill) {
      this.skill = skill;
    }

    public String getOwner() {
      return owner;
    }

    public void setOwner(String owner) {
      this.owner = owner;
    }

    public String getFileId() {
      return fileId;
    }

    public void setFileId(String fileId) {
      this.fileId = fileId;
    }

    public String getFilePath() {
      return filePath;
    }

    public void setFilePath(String filePath) {
      this.filePath = filePath;
    }

    public String getTxtFilePath() {
      return txtFilePath;
    }

    public void setTxtFilePath(String txtFilePath) {
      this.txtFilePath = txtFilePath;
    }

    public String getFileName() {
      return fileName;
    }

    public void setFileName(String fileName) {
      this.fileName = fileName;
    }

    public Long getFileSize() {
      return fileSize;
    }

    public void setFileSize(Long fileSize) {
      this.fileSize = fileSize;
    }

    public String getHash() {
      return hash;
    }

    public void setHash(String hash) {
      this.hash = hash;
    }

    public Integer getStatus() {
      return status;
    }

    public void setStatus(Integer status) {
      this.status = status;
    }

    public Date getCreateTime() {
      return createTime;
    }

    public void setCreateTime(Date createTime) {
      this.createTime = createTime;
    }

    public Date getUpdateTime() {
      return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
      this.updateTime = updateTime;
    }

    public JSONArray getLabel() {
      return label;
    }

    public void setLabel(JSONArray label) {
      this.label = label;
    }

  
  
}
