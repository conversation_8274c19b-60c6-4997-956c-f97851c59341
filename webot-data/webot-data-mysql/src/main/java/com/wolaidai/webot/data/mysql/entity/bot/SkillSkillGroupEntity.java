package com.wolaidai.webot.data.mysql.entity.bot;

import javax.persistence.*;

import com.wolaidai.webot.data.mysql.constant.Database;

import java.util.Date;
import java.util.Objects;

@Entity
@Table(name="skill__skill_group",catalog = Database.QA_BOT)
public class SkillSkillGroupEntity {

    @EmbeddedId
    private SkillSkillGroupIdEntity id;

    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("skillId")
    private SkillEntity skill;

    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("skillGroupId")
    private SkillGroupEntity skillGroup;

    private Date createTime;

    public SkillSkillGroupEntity() {
    }

    public SkillSkillGroupEntity(SkillEntity skill, SkillGroupEntity skillGroup) {
        this.skill = skill;
        this.skillGroup = skillGroup;
        this.id = new SkillSkillGroupIdEntity(skill.getId(),skillGroup.getId());
    }

    public SkillSkillGroupEntity(SkillEntity skill, SkillGroupEntity skillGroup, Date createTime) {
        this(skill,skillGroup);
        this.createTime = createTime;
    }

    public SkillSkillGroupIdEntity getId() {
        return id;
    }

    public void setId(SkillSkillGroupIdEntity id) {
        this.id = id;
    }

    public SkillEntity getSkill() {
        return skill;
    }

    public void setSkill(SkillEntity skill) {
        this.skill = skill;
    }

    public SkillGroupEntity getSkillGroup() {
        return skillGroup;
    }

    public void setSkillGroup(SkillGroupEntity skillGroup) {
        this.skillGroup = skillGroup;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SkillSkillGroupEntity that = (SkillSkillGroupEntity) o;
        return skill.equals(that.skill) &&
                skillGroup.equals(that.skillGroup);
    }

    @Override
    public int hashCode() {
        return Objects.hash(skill, skillGroup);
    }
}
