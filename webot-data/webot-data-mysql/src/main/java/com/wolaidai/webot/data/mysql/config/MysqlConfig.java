package com.wolaidai.webot.data.mysql.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@EnableJpaRepositories("com.wolaidai.webot.data.mysql.repo")
@EntityScan("com.wolaidai.webot.data.mysql.entity")
@ComponentScan({"com.wolaidai.webot.data.mysql.repo", "com.welab.privacy"})
public class MysqlConfig {

}
