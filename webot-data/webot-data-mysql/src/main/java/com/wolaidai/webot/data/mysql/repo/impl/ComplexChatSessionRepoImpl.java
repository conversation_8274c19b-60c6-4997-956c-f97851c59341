package com.wolaidai.webot.data.mysql.repo.impl;

import com.wolaidai.webot.data.mysql.entity.chat.ChatMembersEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ChatSessionEntity;
import com.wolaidai.webot.data.mysql.repo.ChatSessionRepo;
import com.wolaidai.webot.data.mysql.repo.ComplexChatSessionRepo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;

import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.List;

@Repository
@AllArgsConstructor
public class ComplexChatSessionRepoImpl implements ComplexChatSessionRepo {

    private final ChatSessionRepo chatSessionRepo;

    @Override
    public List<ChatSessionEntity> findByOrgIdAndUserEmail(Integer orgId, String email) {
        return chatSessionRepo.findAll((Root<ChatSessionEntity> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder)->{
            List<Predicate> andPredicates = new ArrayList<>();
            andPredicates.add(criteriaBuilder.equal(root.get("orgId"), orgId));
            Subquery<Long> subQuery = criteriaQuery.subquery(Long.class);
            Root<ChatMembersEntity> fromChatMembers = subQuery.from(ChatMembersEntity.class);
            andPredicates.add(criteriaBuilder.in(root.get("id")).value(subQuery.select(fromChatMembers.get("session").get("id"))
                    .where(criteriaBuilder.equal(fromChatMembers.get("user").get("email"), email),
                            criteriaBuilder.equal(fromChatMembers.get("user").get("orgId"), orgId))));
            return criteriaBuilder.and(andPredicates.toArray(new Predicate[]{}));
        });
    }

    @Override
    public List<ChatSessionEntity> findByOrgIdAndUserEmailAndRoomIsNull(Integer orgId, String email) {
        return chatSessionRepo.findAll((Root<ChatSessionEntity> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder)->{
            List<Predicate> andPredicates = new ArrayList<>();
            andPredicates.add(criteriaBuilder.equal(root.get("orgId"), orgId));
            andPredicates.add(criteriaBuilder.isNull(root.get("room")));
            Subquery<Long> subQuery = criteriaQuery.subquery(Long.class);
            Root<ChatMembersEntity> fromChatMembers = subQuery.from(ChatMembersEntity.class);
            andPredicates.add(criteriaBuilder.in(root.get("id")).value(subQuery.select(fromChatMembers.get("session").get("id"))
                    .where(criteriaBuilder.equal(fromChatMembers.get("user").get("email"), email),
                            criteriaBuilder.equal(fromChatMembers.get("user").get("orgId"), orgId))));
            return criteriaBuilder.and(andPredicates.toArray(new Predicate[]{}));
        });
    }
}
