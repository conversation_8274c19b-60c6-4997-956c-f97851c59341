package com.wolaidai.webot.data.mysql.entity.bot;

import com.wolaidai.webot.data.mysql.constant.Database;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import java.util.Date;
import java.util.Set;
@Entity
@Table(name="skill_group",catalog = Database.QA_BOT)
public class SkillGroupEntity extends BaseEntity {
    public static final int ACTIVE_STATUS=1;
    public static final int DISABLE_STATUS=0;
    private String name;
    private String aliasName;
    private Integer status;
    private Integer orgId;
    @OneToMany(mappedBy = "skillGroup", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("createTime desc")
    private Set<SkillSkillGroupEntity> skillSkillGroups;
    @OneToMany(mappedBy = "skillGroup")
    private Set<BotSkillGroupEntity> botSkillGroups;
    private String creator;
    private String remark;
    private Date createTime;
    private Date updateTime;
    private Integer type;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Set<SkillSkillGroupEntity> getSkillSkillGroups() {
        return skillSkillGroups;
    }

    public void setSkillSkillGroups(Set<SkillSkillGroupEntity> skillSkillGroups) {
        this.skillSkillGroups = skillSkillGroups;
    }

    public Set<BotSkillGroupEntity> getBotSkillGroups() {
        return botSkillGroups;
    }

    public void setBotSkillGroups(Set<BotSkillGroupEntity> botSkillGroups) {
        this.botSkillGroups = botSkillGroups;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
