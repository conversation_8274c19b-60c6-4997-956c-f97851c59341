package com.wolaidai.webot.data.mysql.repo;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.alibaba.fastjson.JSONArray;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import org.springframework.transaction.annotation.Transactional;

public interface SessionListRepo extends BaseRepo<SessionListEntity, Integer> {

    @Query(value = "select client_id from session_list s where s.org_id=?1 and s.client_id=?2 and s.create_time > ?3 and s.status in (0,1) order by create_time desc limit 1",nativeQuery = true)
    String findClientIdByOnlineStatusAndCreateTime(Integer orgId, String clientId, Date createTime);

    SessionListEntity findBySessionKey(String sessionKey);

    @Query(value = "select session_key from session_list where org_id=?1 and last_service_user=?2 and create_time>=?3", nativeQuery = true)
    List<String> findSessionKeysByEmail(Integer orgId, String email, Date date);

    @Query(value = "select session_key from session_list where org_id=?1 and client_id=?2 and create_time>=?3", nativeQuery = true)
    List<String> findSessionKeysByClientId(Integer orgId, String clientId, Date date);

    List<SessionListEntity> findByStatusInAndCreateTimeAfter(List<Integer> statuses,Date createTime);

    SessionListEntity findFirstByClientIdAndClientTypeIdAndBotIdOrderByIdDesc(String clientId, Integer clientType, Integer botId);

    @Modifying
    @Transactional
    @Query("update SessionListEntity set firstRespTimeout=?2,updateTime=?3 where sessionKey=?1")
    void updateFirstRespTimeoutBySessionKey(String sessionKey,Integer firstRespTimeout,Date updateTime);

    @Modifying
    @Transactional
    @Query("update SessionListEntity set respTimeout=?2,updateTime=?3 where sessionKey=?1")
    void updateRespTimeoutBySessionKey(String sessionKey,Integer respTimeout,Date updateTime);

    @Modifying
    @Transactional
    @Query("update SessionListEntity set unreadMsgCount=?2,updateTime=?3 where sessionKey=?1")
    void updateUnreadMsgCountBySessionKey(String sessionKey,Integer unreadMsgCount,Date updateTime);

    @Modifying
    @Transactional
    @Query(value = "update session_list set status=?2,update_time=now() where id=?1", nativeQuery = true)
    void updateStatusById(Integer id, Integer status);

    @Modifying
    @Transactional
    @Query(value = "update session_list set last_msg=?2,last_msg_time=?3,update_time=now() where id=?1", nativeQuery = true)
    void updateLastMsgById(Integer id, String msg, Date msgTime);

    @Modifying
    @Transactional
    @Query(value = "update session_list set satisfaction_level=?2,update_time=now() where id=?1", nativeQuery = true)
    void updateSatisfactionLevelById(Integer id, Integer satisfactionLevel);

    @Modifying
    @Transactional
    @Query(value = "update session_list set mark=?2,from_service_user=?3,last_service_user=?4,transfer_to_users=?5,update_time=now() where id=?1", nativeQuery = true)
    void updateTransferById(Integer id, Integer mark, String fromServiceUser, String lastServiceUser, String transferToUsers);

    @Modifying
    @Transactional
    @Query(value = "update session_list set avg_resp_time=?2,avg_resp_timeout=?3,status=?4,close_type=?5,duration_second=?6,offline_time=now(),update_time=now() where id=?1", nativeQuery = true)
    void updateById(Integer id, Integer avgRespTime, Integer avgRespTimeout, Integer status, Integer closeType, Long durationSecond);

    @Modifying
    @Transactional
    @Query(value = "update session_list set customer_first_reply_time=?2,customer_last_reply_time=?3,unread_msg_count=?4,last_msg=?5,last_msg_time=?6,last_msg_sender=?7,update_time=now() where id=?1", nativeQuery = true)
    void updateId(Integer id, Date customerFirstReplyTime, Date customerLastReplyTime, Integer unreadMsgCount, String lastMsg, Date lastMsgTime, Integer lastMsgSender);

    @Modifying
    @Transactional
    @Query(value = "update session_list set customer_first_reply_time=?2,avg_resp_time=?3,avg_resp_timeout=?4,service_first_resp_time=?5,unread_msg_count=?6,last_msg=?7,last_msg_time=?8,last_msg_sender=?9,update_time=now() where id=?1", nativeQuery = true)
    void updateById(Integer id, Date customerFirstReplyTime, Integer avgRespTime, Integer avgRespTimeout, Integer serviceFirstRespTime, Integer unreadMsgCount, String lastMsg, Date lastMsgTime, Integer lastMsgSender);

    @Modifying
    @Transactional
    @Query(value = "update session_list set quality_inspection_status=?2,quality_inspection_task_id=?3,update_time=now() where id=?1", nativeQuery = true)
    void updateQualityInspectionStatus(Integer id, Integer status, String taskId);

    @Modifying
    @Transactional
    @Query(value = "update session_list set quality_inspection_status=?2,quality_inspection_score=?3,quality_inspection_time=?4,update_time=now() where id=?1", nativeQuery = true)
    void updateQualityInspectionResult(Integer id, Integer status, Integer score, Date inspectionTime);
}
