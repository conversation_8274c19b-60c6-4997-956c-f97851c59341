package com.wolaidai.webot.data.mysql.repo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.NoRepositoryBean;

@NoRepositoryBean
public interface BaseRepo<T, ID> extends CrudRepository<T, ID>, JpaSpecificationExecutor<T> {

    public default Page<T> find(Integer orgId, String[][] like, Object[][] equal, int page, int size, String timeKey, Date startTime, Date endTime, Object[]... orderBy) {
        return findAll((Root<T> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) -> {
            List<Predicate> andPredicates = new ArrayList<>();
            if (null != orgId) {
                andPredicates.add(criteriaBuilder.equal(root.get("orgId"), orgId));
            }
            if (null != like && like.length > 0) {
                List<Predicate> or = new ArrayList<>();
                for (String[] a : like) {
                    String key = a[0], keyField = a[1], o = a[2];
                    String criteriaString = "%" + key.replace("%", "/%").replace("_", "/_") + "%";
                    Predicate p = criteriaBuilder.like(root.get(keyField), criteriaString, '/');
                    if ("and".equals(o)) {
                        andPredicates.add(p);
                    } else {
                        or.add(p);
                    }
                }
                if (or.size() > 0) {
                    andPredicates.add(criteriaBuilder.or(or.toArray(new Predicate[or.size()])));
                }
            }
            if (null != equal && equal.length > 0) {
                for (Object[] pair : equal) {
                    if (null == pair || pair.length < 2) {
                        continue;
                    }
                    andPredicates.add(criteriaBuilder.equal(root.get((String) pair[0]), pair[1]));
                }
            }
            if (startTime != null) {
                andPredicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get(timeKey), startTime));
            }
            if (endTime != null) {
                andPredicates.add(criteriaBuilder.lessThan(root.get(timeKey), endTime));
            }
            if (null != orderBy && orderBy.length > 0) {
                ArrayList<javax.persistence.criteria.Order> orders = new ArrayList<>();
                for (Object[] p : orderBy) {
                    if (null == p || p.length < 2) {
                        continue;
                    }
                    Expression<?> e = p.length > 2 ? criteriaBuilder.coalesce(root.get((String) p[0]), p[2]) : root.get((String) p[0]);
                    orders.add("desc".equalsIgnoreCase((String) p[1]) ? criteriaBuilder.desc(e) : criteriaBuilder.asc(e));
                }
                if (orders.size() > 0) {
                    criteriaQuery.orderBy(orders);
                }
            }
            return criteriaBuilder.and(andPredicates.toArray(new Predicate[andPredicates.size()]));
        }, page == -1 ? Pageable.unpaged() : PageRequest.of(page, size));
    }

}
