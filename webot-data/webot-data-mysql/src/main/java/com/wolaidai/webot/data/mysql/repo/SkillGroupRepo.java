package com.wolaidai.webot.data.mysql.repo;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.wolaidai.webot.data.mysql.entity.bot.SkillGroupEntity;

public interface SkillGroupRepo extends CrudRepository<SkillGroupEntity, Integer> {
    @Query(value = "select sg.* from qa_bot.skill_group sg left join qa_bot.bot__skill_group bsg on sg.id = bsg.skill_group_id where bsg.bot_id = (select id from qa_bot.bot where bot.code = ?1) and sg.status = 1 order by bsg.position desc,bsg.create_time asc, bsg.skill_group_id asc", nativeQuery = true)
    List<SkillGroupEntity> findByAccessKey(String accessKey);
}
