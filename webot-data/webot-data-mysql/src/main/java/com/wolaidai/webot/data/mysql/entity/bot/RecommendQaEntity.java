package com.wolaidai.webot.data.mysql.entity.bot;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.data.mysql.constant.Database;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "recommend_qa", catalog = Database.QA_BOT)
public class RecommendQaEntity extends BaseEntity {
    public static final int ENABLE_STATUS = 1;
    public static final int DISABLE_STATUS = 0;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JSONObject question;

    private Integer status;

    private Date createTime;

    private Date updateTime;

    public JSONObject getQuestion() {
        return question;
    }

    public void setQuestion(JSONObject question) {
        this.question = question;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
