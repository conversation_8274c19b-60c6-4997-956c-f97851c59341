package com.wolaidai.webot.data.mysql.entity.bot;

import com.wolaidai.webot.data.mysql.constant.Database;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;
@Entity
@Table(name="material",catalog = Database.QA_BOT)
public class MaterialEntity extends BaseEntity {
    public static final int IMAGE_TYPE =1;
    public static final int VOICE_TYPE =2;

    public static final int ENABLE_STATUS=1;
    public static final int DISABLE_STATUS=0;

//    @Transient
//    private PersistentAttributeInterceptor interceptor;

    private String fileId;

    private String fileName;

    private Long fileSize;

    private Long duration;
//
//    @Lob
////    @Column(length = 1048576)
//    @Basic(fetch = FetchType.LAZY)
//    private byte[] fileData;

    private String hash;

    private Integer orgId;

    private Integer type;

    private Integer status;

    private Date createTime;

    private Date updateTime;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

//    @LazyToOne(LazyToOneOption.NO_PROXY)
//    public byte[] getFileData() {
//        if (interceptor != null) {
//            return (byte[]) interceptor.readObject(this, "fileData", fileData);
//        }
//        return fileData;
//    }
//
//    public void setFileData(byte[] fileData) {
//        this.fileData = fileData;
//    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

//    @Override
//    public PersistentAttributeInterceptor $$_hibernate_getInterceptor() {
//        return interceptor;
//    }
//
//    @Override
//    public void $$_hibernate_setInterceptor(PersistentAttributeInterceptor interceptor) {
//        this.interceptor = interceptor;
//    }
}
