package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface UserStateRepo extends BaseRepo<UserStateEntity, Integer> {
    UserStateEntity findByOrgIdAndEmail(Integer orgId, String email);

    @Query(value = "select concat(nick_name, ' ', name) from user_state us where us.org_id = ?1 and email in ?2", nativeQuery = true)
    List<String> findByOrgIdAndEmailIn(Integer orgId, List<String> emails);
}
