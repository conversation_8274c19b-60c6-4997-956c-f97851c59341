package com.wolaidai.webot.data.mysql.entity.bot;

import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

@Embeddable
public class SkillSkillGroupIdEntity implements Serializable {
    private Integer skillId;
    private Integer skillGroupId;

    public SkillSkillGroupIdEntity() {
    }

    public SkillSkillGroupIdEntity(Integer skillId, Integer skillGroupId) {
        this.skillId = skillId;
        this.skillGroupId = skillGroupId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SkillSkillGroupIdEntity that = (SkillSkillGroupIdEntity) o;
        return skillId.equals(that.skillId) &&
                skillGroupId.equals(that.skillGroupId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(skillId, skillGroupId);
    }
}
