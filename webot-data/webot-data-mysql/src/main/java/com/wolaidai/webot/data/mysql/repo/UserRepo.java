package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.account.UserEntity;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;


public interface UserRepo extends CrudRepository<UserEntity, Integer>, JpaSpecificationExecutor<UserEntity> {

    @Query(value = "select ud.extra_field_json from auth.user u left join auth.user_detail ud on u.id=ud.user_id where ud.org_id=?1 and ud.product_id=?2 and u.email = ?3", nativeQuery = true)
    String findExtraField(Integer orgId, Integer prodId, String email);

}
