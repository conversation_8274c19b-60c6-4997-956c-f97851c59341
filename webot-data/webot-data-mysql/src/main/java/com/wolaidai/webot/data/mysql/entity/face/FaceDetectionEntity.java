package com.wolaidai.webot.data.mysql.entity.face;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;

@Entity
@Table(name = "face_detection")
public class FaceDetectionEntity extends BaseEntity {

    private Integer orgId;
    @ManyToOne(fetch = FetchType.LAZY)
    private SessionListEntity session;
    private String mobile;
    private String token;
    private String fileId;
    private String serviceUser;
    private Integer code;
    private Integer failureTimes = 0;
    private String msg;
    private Date createTime;
    private Date updateTime;
    private String vendor;
    private String source;
    @OneToMany(mappedBy = "faceDetection", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<FaceDetectionHistoryEntity> histories = new ArrayList<>();

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public SessionListEntity getSession() {
        return session;
    }

    public void setSession(SessionListEntity session) {
        this.session = session;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getServiceUser() {
        return serviceUser;
    }

    public void setServiceUser(String serviceUser) {
        this.serviceUser = serviceUser;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getFailureTimes() {
        return failureTimes;
    }

    public void setFailureTimes(Integer failureTimes) {
        this.failureTimes = failureTimes;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public List<FaceDetectionHistoryEntity> getHistories() {
        return histories;
    }

    public void setHistories(List<FaceDetectionHistoryEntity> histories) {
        this.histories = histories;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

}
