package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;


public interface QueueListRepo extends BaseRepo<QueueListEntity, Integer> {
    @Query(value = "select * from queue_list s where s.org_id=?1 and s.client_id=?2 and s.create_time > ?3 and s.status =0 order by id desc limit 1",nativeQuery = true)
    QueueListEntity findIdByInQueueStatusAndCreateTime(Integer orgId, String clientId, Date createTime);

    List<QueueListEntity> findByOrgIdAndGcidAndStatus(Integer orgId, String gcid, Integer status);


}
