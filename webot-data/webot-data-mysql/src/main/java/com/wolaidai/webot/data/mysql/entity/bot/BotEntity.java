package com.wolaidai.webot.data.mysql.entity.bot;

import com.wolaidai.webot.data.mysql.constant.Database;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.*;
import java.util.Date;
import java.util.Set;

@Entity
@Table(name="bot",catalog = Database.QA_BOT)
public class BotEntity extends BaseEntity {
    public static final int ACTIVE_STATUS=1;
    public static final int DISABLE_STATUS=0;

    private String name;
    private String nickname;
    private Integer orgId;
    private Integer status;
    private String code;
    private String creator;
    private String remark;
    @OneToMany(mappedBy = "bot", cascade = CascadeType.ALL)
    private Set<BotSkillGroupEntity> botSkillGroups;
    private Date createTime;
    private Date updateTime;
    private Integer type;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Set<BotSkillGroupEntity> getBotSkillGroups() {
        return botSkillGroups;
    }

    public void setBotSkillGroups(Set<BotSkillGroupEntity> botSkillGroups) {
        this.botSkillGroups = botSkillGroups;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    
}
