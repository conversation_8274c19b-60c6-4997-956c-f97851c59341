package com.wolaidai.webot.data.mysql.entity.chat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "chat_room_users")
public class ChatRoomUsersEntity implements Serializable {

    @EmbeddedId
    private ChatRoomUsersIdEntity id;

    @ManyToOne
    @MapsId("roomId")
    private ChatRoomEntity room;

    @ManyToOne
    @MapsId("userId")
    private UserStateEntity user;

    private Date createTime;

    public ChatRoomUsersEntity() {
    }

    public ChatRoomUsersEntity(ChatRoomEntity room, UserStateEntity user) {
        this.id = new ChatRoomUsersIdEntity(room.getId(), user.getId());
        this.room = room;
        this.user = user;
        this.createTime = new Date();
    }

    public ChatRoomUsersIdEntity getId() {
        return id;
    }

    public void setId(ChatRoomUsersIdEntity id) {
        this.id = id;
    }

    public ChatRoomEntity getRoom() {
        return room;
    }

    public void setRoom(ChatRoomEntity room) {
        this.room = room;
    }

    public UserStateEntity getUser() {
        return user;
    }

    public void setUser(UserStateEntity user) {
        this.user = user;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ChatRoomUsersEntity that = (ChatRoomUsersEntity) o;
        return Objects.equals(room.getId(), that.room.getId())
                && Objects.equals(user.getId(), that.user.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(room.getId(), user.getId());
    }
}
