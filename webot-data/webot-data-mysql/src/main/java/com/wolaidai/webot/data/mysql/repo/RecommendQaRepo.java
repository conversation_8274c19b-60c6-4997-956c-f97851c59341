package com.wolaidai.webot.data.mysql.repo;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.wolaidai.webot.data.mysql.entity.bot.RecommendQaEntity;

public interface RecommendQaRepo extends CrudRepository<RecommendQaEntity, Integer> {

    @Query(value = "select * from qa_bot.recommend_qa where status = 1 and skill_id = (select ssg.skill_id from qa_bot.skill__skill_group ssg left join qa_bot.skill s on ssg.skill_id = s.id where ssg.skill_group_id = ?1 and s.type = 2 and s.status = 1)", nativeQuery = true)
    List<RecommendQaEntity> findBySkillGroupId(Integer skillGroupId);

}
