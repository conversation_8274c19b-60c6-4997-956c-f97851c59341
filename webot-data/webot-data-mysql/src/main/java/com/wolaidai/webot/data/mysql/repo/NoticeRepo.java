package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.bot.NoticeEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

public interface NoticeRepo extends CrudRepository<NoticeEntity, Integer> {

    @Query(value = "select * from qa_bot.notice n where skill_group_id=?1 and status=1 and (start_time is null or start_time<=now()) and (end_time is null or end_time>now())", nativeQuery = true)
    NoticeEntity findBySkillGroupId(Integer skillGroupId);

}
