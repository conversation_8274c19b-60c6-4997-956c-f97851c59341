package com.wolaidai.webot.data.mysql.entity.bot;

import com.wolaidai.webot.data.mysql.constant.Database;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.*;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.Set;

@Entity
@Table(name = "qa", catalog = Database.QA_BOT)
@NamedEntityGraph(name = "Category.Graph", attributeNodes = { @NamedAttributeNode("similarQuestions") })
public class QaEntity extends BaseEntity {
    public static final int TEXT_TYPE = 1;
    public static final int IMAGE_TYPE = 2;
    public static final int VOICE_TYPE = 3;

    public static final int MODEL_TYPE = 1;
    public static final int KEY_TYPE = 2;

    public static final int ENABLE_STATUS = 1;
    public static final int DISABLE_STATUS = 0;

    private String question;

    private String token;

    private String answer;

    private Integer orgId;

    @OneToMany(mappedBy = "qa", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<SimilarQuestionEntity> similarQuestions = new LinkedHashSet<>();

    private Integer upgrade;

    private Date startTime;

    private Date endTime;

    private Integer questionType;

    private Integer answerType;

    private String keyContent;

    private Integer status;

    private String creator;

    private Date createTime;

    private Date updateTime;

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Set<SimilarQuestionEntity> getSimilarQuestions() {
        return similarQuestions;
    }

    public void setSimilarQuestions(Set<SimilarQuestionEntity> similarQuestions) {
        this.similarQuestions = similarQuestions;
    }

    public Integer getUpgrade() {
        return upgrade;
    }

    public void setUpgrade(Integer upgrade) {
        this.upgrade = upgrade;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getQuestionType() {
        return questionType;
    }

    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
    }

    public Integer getAnswerType() {
        return answerType;
    }

    public void setAnswerType(Integer answerType) {
        this.answerType = answerType;
    }

    public String getKeyContent() {
        return keyContent;
    }

    public void setKeyContent(String keyContent) {
        this.keyContent = keyContent;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
