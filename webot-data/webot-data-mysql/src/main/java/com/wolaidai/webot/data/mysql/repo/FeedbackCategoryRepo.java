package com.wolaidai.webot.data.mysql.repo;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.wolaidai.webot.data.mysql.entity.feedback.FeedbackCategoryEntity;

public interface FeedbackCategoryRepo extends CrudRepository<FeedbackCategoryEntity, Integer> {
    @Query(value = "select * from qa_bot.feedback_category fbc where fbc.bot_id = (select id from qa_bot.bot where bot.code = ?1) order by fbc.position desc", nativeQuery = true)
    List<FeedbackCategoryEntity> findByAccessKey(String accessKey);

    FeedbackCategoryEntity findByIdAndBotId(Integer Id, Integer botId);
}
