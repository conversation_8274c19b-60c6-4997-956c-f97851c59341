package com.wolaidai.webot.data.mysql.entity.bot;

import com.wolaidai.webot.data.mysql.constant.Database;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name="qa_similars", catalog = Database.QA_BOT)
public class SimilarQuestionEntity extends BaseEntity {

    private String question;

    private String token;

    @ManyToOne(fetch = FetchType.LAZY)
    private QaEntity qa;

    private Integer orgId;

    private Date createTime;

    private Date updateTime;

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public QaEntity getQa() {
        return qa;
    }

    public void setQa(QaEntity qa) {
        this.qa = qa;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SimilarQuestionEntity that = (SimilarQuestionEntity) o;
        return getId()!=null&&getId().equals(that.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId());
    }
}
