package com.wolaidai.webot.data.mysql.entity.bot;

import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

@Embeddable
public class BotSkillGroupIdEntity implements Serializable {
    private Integer botId;
    private Integer skillGroupId;

    public BotSkillGroupIdEntity() {
    }

    public BotSkillGroupIdEntity(Integer botId, Integer skillGroupId) {
        this.botId = botId;
        this.skillGroupId = skillGroupId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BotSkillGroupIdEntity that = (BotSkillGroupIdEntity) o;
        return botId.equals(that.botId) &&
                skillGroupId.equals(that.skillGroupId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(botId, skillGroupId);
    }
}
