package com.wolaidai.webot.data.mysql.entity.chat;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "user_verification")
public class UserVerification extends BaseEntity {

    public static final Integer STATUS_NOT_VERIFIED = 0;
    public static final Integer STATUS_VERIFIED = 1;
    public static final Integer STATUS_VERIFY_FAILED = 2;
    public static final Integer STATUS_NON_REGISTERED = 3;

    private Integer orgId;

    private String token;

    private Integer clientType;

    private String clientId;

    private String serviceUser;

    private Integer status;

    private Integer failureTimes;

    private String msg;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @ManyToOne(fetch = FetchType.LAZY)
    private SessionListEntity session;

    @OneToMany(mappedBy = "userVerification", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<UserVerificationHistory> userVerificationHistoryList = new ArrayList<>();

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getServiceUser() {
        return serviceUser;
    }

    public void setServiceUser(String serviceUser) {
        this.serviceUser = serviceUser;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getFailureTimes() {
        return failureTimes;
    }

    public void setFailureTimes(Integer failureTimes) {
        this.failureTimes = failureTimes;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public SessionListEntity getSession() {
        return session;
    }

    public void setSession(SessionListEntity session) {
        this.session = session;
    }

    public List<UserVerificationHistory> getUserVerificationHistoryList() {
        return userVerificationHistoryList;
    }

    public void setUserVerificationHistoryList(List<UserVerificationHistory> userVerificationHistoryList) {
        this.userVerificationHistoryList = userVerificationHistoryList;
    }
}
