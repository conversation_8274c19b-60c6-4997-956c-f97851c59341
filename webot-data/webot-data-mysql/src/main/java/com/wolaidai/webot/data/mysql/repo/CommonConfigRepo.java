package com.wolaidai.webot.data.mysql.repo;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;

public interface CommonConfigRepo extends BaseRepo<CommonConfigEntity, Integer> {

    @Query(value = "select * from common_config where (org_id=?1 or org_id=-1) and type=?2 order by org_id desc limit 1", nativeQuery = true)
    CommonConfigEntity findOneByOrgIdAndType(Integer orgId, String type);
    @Query(value = "select * from common_config where org_id=?1 and type=?2 order by org_id desc limit 1", nativeQuery = true)
    CommonConfigEntity checkByOrgIdAndType(Integer orgId, String type);
    
    @Query(value = "select * from common_config where (org_id=?1 or org_id=-1) and type=?2 order by org_id asc", nativeQuery = true)
    List<CommonConfigEntity> findByOrgIdAndType(Integer orgId, String type);
    
    @Query(value = "select * from common_config where (org_id=?1 or org_id=-1) and type=?2 and sub_type1=?3 order by org_id desc limit 1", nativeQuery = true)
    CommonConfigEntity findOneByOrgIdAndTypeAndSubType1(Integer orgId, String type, String subType1);
    @Query(value = "select * from common_config where org_id=?1 and type=?2 and sub_type1=?3 order by org_id desc limit 1", nativeQuery = true)
    CommonConfigEntity checkByOrgIdAndTypeAndSubType1(Integer orgId, String type, String subType1);
    
    @Query(value = "select * from common_config where (org_id=?1 or org_id=-1) and type=?2 and sub_type1=?3 order by org_id asc", nativeQuery = true)
    List<CommonConfigEntity> findByOrgIdAndTypeAndSubType1(Integer orgId, String type, String subType1);
    
    @Query(value = "select * from common_config where (org_id=?1 or org_id=-1) and type=?2 and sub_type1=?3 and sub_type2=?4 order by org_id desc limit 1", nativeQuery = true)
    CommonConfigEntity findOneByOrgIdAndTypeAndSubType1AndSubType2(Integer orgId, String type, String subType1, String subType2);
    @Query(value = "select * from common_config where org_id=?1 and type=?2 and sub_type1=?3 and sub_type2=?4 order by org_id desc limit 1", nativeQuery = true)
    CommonConfigEntity checkByOrgIdAndTypeAndSubType1AndSubType2(Integer orgId, String type, String subType1, String subType2);
    
    @Query(value = "select * from common_config where (org_id=?1 or org_id=-1) and type=?2 and sub_type1=?3 and sub_type2=?4 order by org_id asc", nativeQuery = true)
    List<CommonConfigEntity> findByOrgIdAndTypeAndSubType1AndSubType2(Integer orgId, String type, String subType1, String subType2);
}
