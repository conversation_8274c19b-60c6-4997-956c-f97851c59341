package com.wolaidai.webot.data.mysql.entity.config;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "business_type")
//业务类型表
public class BusinessTypeEntity extends BaseEntity {
    private Integer orgId;
    private String title;
    @ManyToOne(fetch = FetchType.LAZY)
    private BusinessUnitEntity unit;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="type_id")
    private BusinessTypeEntity parentType;
    @OneToMany(mappedBy = "parentType")
    @OrderBy("position asc,id asc")
    private Set<BusinessTypeEntity> childTypes;
    private Integer level;
    private Integer position = 0;
    private Date createTime;
    private Date updateTime;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public BusinessTypeEntity getParentType() {
        return parentType;
    }

    public void setParentType(BusinessTypeEntity parentType) {
        this.parentType = parentType;
    }

    public BusinessUnitEntity getUnit() {
        return unit;
    }

    public void setUnit(BusinessUnitEntity unit) {
        this.unit = unit;
    }

    public Set<BusinessTypeEntity> getChildTypes() {
        return childTypes;
    }

    public void setChildTypes(Set<BusinessTypeEntity> childTypes) {
        this.childTypes = childTypes;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String fullPathTitle() {
        BusinessTypeEntity parentType = getParentType();
        if(parentType != null){
            return parentType.fullPathTitle() + "," + title;
        }
        return title;
    }

    public void fullPathTitle(List<String> fullPathTitles) {
        Set<BusinessTypeEntity> children = getChildTypes();
        if (!CollectionUtils.isEmpty(children)) {
            for (BusinessTypeEntity child1 : children) {
                if (!CollectionUtils.isEmpty(child1.getChildTypes())) {
                    for (BusinessTypeEntity child2 : child1.getChildTypes()) {
                        if (!CollectionUtils.isEmpty(child2.getChildTypes())) {
                            for (BusinessTypeEntity child3 : child2.getChildTypes()) {
                                fullPathTitles.add(title + "|," + child1.title + "|," + child2.title + "|," + child3.title);
                            }
                        } else {
                            fullPathTitles.add(title + "|," + child1.title + "|," + child2.title);
                        }
                    }
                } else {
                    fullPathTitles.add(title + "|," +  child1.title);
                }
            }
        } else {
            fullPathTitles.add(title);
        }
    }
}
