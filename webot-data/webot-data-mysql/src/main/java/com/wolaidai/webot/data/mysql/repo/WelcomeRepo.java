package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.bot.WelcomeEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

public interface WelcomeRepo extends CrudRepository<WelcomeEntity,Integer> {
    @Query(value = "select * from qa_bot.welcome where org_id is null or bot_id is null or bot_id = ?1 order by bot_id desc, org_id desc limit 1",nativeQuery = true)
    WelcomeEntity findByBotId(Integer botId);
}
