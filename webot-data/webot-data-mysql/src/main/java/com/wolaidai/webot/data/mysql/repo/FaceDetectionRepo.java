package com.wolaidai.webot.data.mysql.repo;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.Query;

import com.wolaidai.webot.data.mysql.entity.face.FaceDetectionEntity;

public interface FaceDetectionRepo extends BaseRepo<FaceDetectionEntity, Integer> {

    FaceDetectionEntity findByToken(String token);
    @Query(value = "select * from face_detection s where s.session_id=?1 order by id desc limit 1", nativeQuery = true)
    FaceDetectionEntity findBySessionId(Integer sessionId);
    Integer countBySessionId(Integer sessionId);

    @Query(value = "select sum(s.failure_times) from face_detection s where s.session_id=?1", nativeQuery = true)
    Integer getFailTimesBySessionId(Integer sessionId);

    Optional<FaceDetectionEntity> findFirstBySourceAndMobileOrderByCreateTimeDesc(String source, String mobile);

    List<FaceDetectionEntity> findByMobileOrderByCreateTimeDesc(String mobile);

}
