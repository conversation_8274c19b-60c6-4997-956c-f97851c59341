package com.wolaidai.webot.data.mysql.repo;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.wolaidai.webot.data.mysql.entity.bot.QaEntity;

public interface QaRepo extends CrudRepository<QaEntity, Integer> {

    @Query(value = "select * from qa_bot.qa where status=1 and question not like '%我来贷%' and skill_id in (select id from qa_bot.skill s left join qa_bot.skill__skill_group ssg on s.id=ssg.skill_id where ssg.skill_group_id in (select skill_group_id from qa_bot.bot b left join qa_bot.bot__skill_group bsg on b.id=bsg.bot_id where b.code=?1 and bsg.skill_group_id=?2))", nativeQuery = true)
    List<QaEntity> findByAccessKeyAndSkillGroupId(String accessKey, Integer skillGroupId);

}
