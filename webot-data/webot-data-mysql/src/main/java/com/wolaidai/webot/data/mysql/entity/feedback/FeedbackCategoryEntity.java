package com.wolaidai.webot.data.mysql.entity.feedback;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.wolaidai.webot.data.mysql.constant.Database;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import com.wolaidai.webot.data.mysql.entity.bot.BotEntity;

@Entity
@Table(name = "feedback_category", catalog = Database.QA_BOT)
public class FeedbackCategoryEntity extends BaseEntity {

	private String content;
	@ManyToOne(fetch = FetchType.LAZY)
	private BotEntity bot;
	private Integer orgId;
	private Integer position = 0;
	private Date createTime;
	private Date updateTime;

	public FeedbackCategoryEntity() {
		createTime = new Date();
		updateTime = createTime;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public BotEntity getBot() {
		return bot;
	}

	public void setBot(BotEntity bot) {
		this.bot = bot;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public Integer getPosition() {
		return position;
	}

	public void setPosition(Integer position) {
		this.position = position;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
