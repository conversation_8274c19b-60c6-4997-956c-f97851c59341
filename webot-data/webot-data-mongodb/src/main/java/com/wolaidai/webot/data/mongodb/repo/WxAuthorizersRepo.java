package com.wolaidai.webot.data.mongodb.repo;

import com.wolaidai.webot.data.mongodb.entity.WxAuthorizersEntity;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

public interface WxAuthorizersRepo extends MongoRepository<WxAuthorizersEntity,String> {
    @Query(value = "{'authorization_info.authorizer_appid': ?0 }", sort = "{_id:-1}")
    WxAuthorizersEntity findByAppId(String appId);
}
