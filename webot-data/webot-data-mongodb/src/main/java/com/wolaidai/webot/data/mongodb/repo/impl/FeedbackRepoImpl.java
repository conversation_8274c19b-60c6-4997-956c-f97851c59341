package com.wolaidai.webot.data.mongodb.repo.impl;

import com.wolaidai.webot.data.mongodb.entity.FeedbackEntity;
import com.wolaidai.webot.data.mongodb.repo.FeedbackRepo;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class FeedbackRepoImpl implements FeedbackRepo {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public Page<FeedbackEntity> findFeedBack(Integer botId, Integer orgId, Integer hitType, Integer categoryId, String key, Date startTime, Date endTime, Integer status, Pageable pageable) {
        Criteria criteria = Criteria.where("botId").is(botId);
        criteria.and("orgId").is(orgId);
		if (null != hitType && hitType > 0) {
			criteria.and("hitType").is(hitType);
		}
		if (null != categoryId && categoryId > 0) {
			criteria.and("categoryId").is(categoryId);
		}
        if(StringUtils.hasText(key)){
            criteria.orOperator(Criteria.where("description").regex(key,"i"),Criteria.where("mobile").is(key));
        }
        if(startTime!=null||endTime!=null) {
            Criteria date = criteria.and("date");
            if (startTime != null) {
                date.gte(startTime);
            }
            if (endTime != null) {
                date.lt(endTime);
            }
        }
        if(status!=null){
            if(status==0){
                criteria.and("status").ne(1);
            }else if(status==1){
                criteria.and("status").is(1);
            }
        }
        Query query = new Query(criteria);
        query.with(pageable);
        long count = mongoTemplate.count(query, FeedbackEntity.class);
        if(count==0){
            return new PageImpl<>(new ArrayList<>(),pageable,count);
        }else {
            List<FeedbackEntity> feedbackEntities = mongoTemplate.find(query, FeedbackEntity.class);
            return new PageImpl<>(feedbackEntities,pageable,count);
        }
    }

    @Override
    public FeedbackEntity findById(String id) {
        if(ObjectId.isValid(id)){
            return mongoTemplate.findById(new ObjectId(id),FeedbackEntity.class);
        }
        return null;
    }

    @Override
    public void saveFeedBack(FeedbackEntity feedbackEntity) {
        mongoTemplate.save(feedbackEntity);
    }

    @Override
    public List<FeedbackEntity> findAll() {
        return mongoTemplate.findAll(FeedbackEntity.class);
    }

    @Override
    public void updateMobileById(String id, String mobile) {
        mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(id)), Update.update("mobile",mobile), FeedbackEntity.class);
    }
}
