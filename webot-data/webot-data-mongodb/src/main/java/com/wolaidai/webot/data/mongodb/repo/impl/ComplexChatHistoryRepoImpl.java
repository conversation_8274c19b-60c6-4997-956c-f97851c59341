package com.wolaidai.webot.data.mongodb.repo.impl;

import com.alibaba.fastjson.JSONArray;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.repo.ComplexChatHistoryRepo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class ComplexChatHistoryRepoImpl implements ComplexChatHistoryRepo {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public void updateSatisfactionBySessionKey(String sessionKey, int level, JSONArray labels, String content) {
        mongoTemplate.updateFirst(new Query(Criteria.where("manual.sessionKey").is(sessionKey).and("eventKey").is("satisfaction")), Update.update("manual.level",level).set("manual.labels", labels).set("manual.content", content), ChatHistoryEntity.class);
    }

    @Override
    public void updateRecallByClientIdAndMsgId(String clientId, String msgId) {
        mongoTemplate.updateFirst(new Query(Criteria.where("clientId").is(clientId).and("msgId").is(msgId)), Update.update("recall",true).set("recallTime",new Date()), ChatHistoryEntity.class);
    }

    @Override
    public void updateLikeById(String id, Integer like) {
        mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(id)), Update.update("extend.like",like).set("extend.likeTime", new Date()), ChatHistoryEntity.class);
    }

    @Override
    public void updateClientIdByCid(String cid, String clientId) {
        mongoTemplate.updateMulti(new Query(Criteria.where("cid").is(cid)), Update.update("clientId",clientId), ChatHistoryEntity.class);
    }

    @Override
    public void updateClientIdAndAccountByCid(String cid, String clientId, String account) {
        mongoTemplate.updateMulti(new Query(Criteria.where("cid").is(cid)), Update.update("clientId",clientId).set("account",account), ChatHistoryEntity.class);
    }
}
