package com.wolaidai.webot.data.mongodb.repo.impl;

import com.wolaidai.webot.data.mongodb.entity.ConversationEntity;
import com.wolaidai.webot.data.mongodb.repo.ComplexConversationRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class ComplexConversationRepoImpl implements ComplexConversationRepo {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public void updateStatusByCid(String cid, Integer status) {
        Criteria criteria = Criteria.where("cid").is(cid);
        Update update = Update.update("status", status);
        if (status == ConversationEntity.END_STATUS) {
            update.set("endTime", new Date());
            criteria.and("status").is(ConversationEntity.START_STATUS);
        }
        mongoTemplate.updateFirst(new Query(criteria), update, ConversationEntity.class);
    }

    @Override
    public void updateSkillGroupIdByCid(String cid, Integer skillGroupId) {
        mongoTemplate.updateFirst(new Query(Criteria.where("cid").is(cid)), new Update().addToSet("skillGroupIds", skillGroupId), ConversationEntity.class);
    }

    @Override
    public void updateOpFlagByCid(String cid) {
        mongoTemplate.updateFirst(new Query(Criteria.where("cid").is(cid)), new Update().set("opFlag", 1), ConversationEntity.class);
    }


    @Override
    public List<ConversationEntity> getClientIdIsMobileData() {
        Criteria criteria = Criteria.where("clientId").regex("^1\\d{10}$");
        return mongoTemplate.find(new Query(criteria), ConversationEntity.class);
    }

    @Override
    public void updateClientIdById(String id, String clientId) {
        mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(id)), Update.update("clientId",clientId), ConversationEntity.class);
    }

    @Override
    public void updateClientIdAndAccountById(String id, String clientId, String account) {
        mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(id)), Update.update("clientId",clientId).set("account", account), ConversationEntity.class);
    }
}
