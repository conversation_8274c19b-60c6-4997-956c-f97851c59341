package com.wolaidai.webot.data.mongodb.repo.impl;

import com.wolaidai.webot.data.mongodb.entity.TestChatHistoryEntity;
import com.wolaidai.webot.data.mongodb.repo.ComplexTestChatHistoryRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class ComplexTestChatHistoryRepoImpl implements ComplexTestChatHistoryRepo {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public List<TestChatHistoryEntity> findByAccountAndDateBefore(String account, Integer botId, Integer skillGroupId, Integer skillId, Date date, Pageable page) {
        Criteria criteria = Criteria.where("account").is(account).and("date").lt(date);
        if(botId!=null){
            criteria.and("botId").is(botId);
        }
        if(skillGroupId!=null){
            criteria.and("skillGroupId").is(skillGroupId);
        }
        if(skillId!=null){
            criteria.and("skillId").is(skillId);
        }
        return mongoTemplate.find(new Query(criteria).with(page), TestChatHistoryEntity.class);
    }
}
