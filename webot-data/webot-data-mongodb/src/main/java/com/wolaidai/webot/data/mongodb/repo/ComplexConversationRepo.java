package com.wolaidai.webot.data.mongodb.repo;

import com.wolaidai.webot.data.mongodb.entity.ConversationEntity;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface ComplexConversationRepo {
    void updateStatusByCid(String cid,Integer status);
    void updateSkillGroupIdByCid(String cid,Integer skillGroupId);
    void updateOpFlagByCid(String cid);

    List<ConversationEntity> getClientIdIsMobileData();

    void updateClientIdById(String id, String clientId);

    void updateClientIdAndAccountById(String id, String clientId, String account);
}
