package com.wolaidai.webot.data.mongodb.repo;

import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;

import java.util.Date;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

public interface ChatHistoryRepo extends MongoRepository<ChatHistoryEntity, String> {

    List<ChatHistoryEntity> findByContentNotAndTypeNotAndEventKeyNotInAndRecallNotAndDateLessThanAndBotIdAndClientIdOrderByDateDesc(String content, String exType, List<String> exEvent, Boolean exRecall, Date date, Integer botId, String clientId, Pageable page);

    List<ChatHistoryEntity> findByContentNotAndTypeNotAndEventKeyNotInAndRecallNotAndDateLessThanAndBotIdAndAccountOrderByDateDesc(String content, String exType, List<String> exEvent, Boolean exRecall, Date date, Integer botId, String account, Pageable page);

    @Query(value = "{'eventKey': {$not: /^(queue:|service:offline)/}, 'clientId': ?0, 'date': {$lt: ?1} }", sort = "{date:-1}")
    List<ChatHistoryEntity> findByClientIdOrderByDateDesc(String clientId, Date date, Pageable page);

    @Query(value = "{'eventKey': {$not: /^(queue:|service:offline)/}, 'clientId': ?0, '$or': [{'manual.sessionKey': ?1,'date': {$lt: ?2}}, {'date': {$lte: ?3}}] }", sort = "{date:-1}")
    List<ChatHistoryEntity> findByClientIdAndDateLessThanEqualOrderByDateDesc(String clientId, String sessionKey, Date date, Date offlineTime, Pageable page);

    @Query(value = "{'clientId': ?0,'origin': ?1,'botId': ?2,'type': {'$ne': 'event'} }", sort = "{date:-1}")
    Page<ChatHistoryEntity> findLastChatHistory(String clientId, String origin, Integer botId, Pageable pageable);

    @Query(value = "{'cid': ?0, 'type':'text'}", sort = "{date:-1}")
    List<ChatHistoryEntity> findHistoryListByCid(String cid);

    ChatHistoryEntity findByMsgId(String msgId);

    @Query(value = "{'manual.sessionKey': ?0, 'type':'text'}", sort = "{date:1}")
    List<ChatHistoryEntity> findHistoryListBySessionKey(String sessionKey);
}
