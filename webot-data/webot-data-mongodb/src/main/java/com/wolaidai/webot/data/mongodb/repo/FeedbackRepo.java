package com.wolaidai.webot.data.mongodb.repo;

import com.wolaidai.webot.data.mongodb.entity.FeedbackEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface FeedbackRepo {
	Page<FeedbackEntity> findFeedBack(Integer botId, Integer orgId, Integer hitType, Integer categoryId, String key, Date startTime, Date endTime, Integer status, Pageable pageable);
	FeedbackEntity findById(String id);
	void saveFeedBack(FeedbackEntity feedbackEntity);

	List<FeedbackEntity> findAll();

	void updateMobileById(String id, String account);
}
