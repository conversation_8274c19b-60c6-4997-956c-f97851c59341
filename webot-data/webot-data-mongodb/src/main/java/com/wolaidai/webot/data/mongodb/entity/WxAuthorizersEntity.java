package com.wolaidai.webot.data.mongodb.entity;

import com.alibaba.fastjson.JSONObject;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.Map;

@Document("wx_authorizers")
public class WxAuthorizersEntity {
    @Id
    private String id;
    private String accessKey;
    private Integer status;
    private JSONObject authorizer_info;
    private JSONObject authorization_info;
    private Date createTime;
    private Date updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public JSONObject getAuthorizer_info() {
        return authorizer_info;
    }

    public void setAuthorizer_info(JSONObject authorizer_info) {
        this.authorizer_info = authorizer_info;
    }

    public JSONObject getAuthorization_info() {
        return authorization_info;
    }

    public void setAuthorization_info(JSONObject authorization_info) {
        this.authorization_info = authorization_info;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
