package com.wolaidai.webot.data.mongodb.repo;


import com.alibaba.fastjson.JSONArray;
import com.wolaidai.webot.data.mongodb.entity.ChatHistoryEntity;

import java.util.Date;
import java.util.List;

public interface ComplexChatHistoryRepo {
    void updateSatisfactionBySessionKey(String sessionKey,int level, JSONArray labels, String content);
    void updateRecallByClientIdAndMsgId(String clientId, String msgId);
    void updateLikeById(String id, Integer like);

    void updateClientIdByCid(String cid, String clientId);

    void updateClientIdAndAccountByCid(String cid, String clientId, String account);
}
