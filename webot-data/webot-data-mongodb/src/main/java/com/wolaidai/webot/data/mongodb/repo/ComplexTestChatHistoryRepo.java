package com.wolaidai.webot.data.mongodb.repo;

import com.wolaidai.webot.data.mongodb.entity.TestChatHistoryEntity;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface ComplexTestChatHistoryRepo {
    List<TestChatHistoryEntity> findByAccountAndDateBefore(String account,Integer botId,Integer skillGroupId,Integer skillId,Date date,Pageable page);
}
