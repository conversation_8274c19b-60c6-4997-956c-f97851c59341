package com.wolaidai.webot.data.redis.config;

import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@EnableConfigurationProperties({SmartRedisProperties.class})
@AutoConfigureBefore({RedisAutoConfiguration.class})
@Import(SmartRedisRegister.class)
public class SmartRedisAutoConfiguration {
}
