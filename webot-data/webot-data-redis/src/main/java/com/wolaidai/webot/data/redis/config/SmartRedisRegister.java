package com.wolaidai.webot.data.redis.config;

import com.wolaidai.webot.data.redis.model.ConnectionInfo;
import com.wolaidai.webot.data.redis.utils.RedisUtils;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.beans.factory.config.ConstructorArgumentValues;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

public class SmartRedisRegister implements EnvironmentAware, ImportBeanDefinitionRegistrar {

    private static final Logger logger = LoggerFactory.getLogger(SmartRedisRegister.class);
    private static Map<String, Object> registerBean = new ConcurrentHashMap<>();
    private Environment env;
    private Binder binder;

    /**
     * ImportBeanDefinitionRegistrar
     *
     * @param annotationMetadata     annotationMetadata
     * @param beanDefinitionRegistry beanDefinitionRegistry
     */
    @Override
    public void registerBeanDefinitions(AnnotationMetadata annotationMetadata, BeanDefinitionRegistry beanDefinitionRegistry) {
        // get all redis config
        Map<String, Map> multipleRedis;
        try {
            multipleRedis = binder.bind("smart.redis", Map.class).get();
        } catch (NoSuchElementException e) {
            logger.error("Failed to configure smart redis: 'smart.redis' attribute is not specified and no embedded redis could be configured.");
            return;
        }
        RedisProperties redisProperties = binder.bind("spring.redis",RedisProperties.class).orElse(null);
        boolean onPrimary = true;
        for (String key : multipleRedis.keySet()) {
            RedisProperties properties = binder.bind("smart.redis." + key, RedisProperties.class).get();
            int database = properties.getDatabase();
            if(redisProperties!=null){
                properties = redisProperties;
            }
            // RedisStandaloneConfiguration
            RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration();
            if(properties.getUrl()!=null){
                ConnectionInfo connectionInfo = RedisUtils.parseUrl(properties.getUrl());
                configuration.setHostName(connectionInfo.getUri().getHost());
                configuration.setPort(connectionInfo.getUri().getPort());
                configuration.setPassword(connectionInfo.getPassword());
            }else{
                configuration.setHostName(properties.getHost());
                configuration.setPort(properties.getPort());
                configuration.setPassword(properties.getPassword());
            }
            configuration.setDatabase(database);

            RedisProperties.Lettuce propertiesLettuce = properties.getLettuce();

            // GenericObjectPoolConfig
            GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
            try {
                RedisProperties.Pool pool = propertiesLettuce.getPool();
                genericObjectPoolConfig.setMaxIdle(pool.getMaxIdle());
                genericObjectPoolConfig.setMaxTotal(pool.getMaxActive());
                genericObjectPoolConfig.setMinIdle(pool.getMinIdle());
                genericObjectPoolConfig.setMaxWaitMillis(pool.getMaxWait().toMillis());
                genericObjectPoolConfig.setTimeBetweenEvictionRuns(Duration.ofSeconds(30));
            } catch (NoSuchElementException ignore) {
            }
            //LettuceConnectionFactory
            Supplier<LettuceConnectionFactory> lettuceConnectionFactorySupplier = () -> {
                LettuceConnectionFactory factory = (LettuceConnectionFactory) registerBean.get(key + "RedisConnectionFactory");
                if (factory != null) {
                    return factory;
                }
                LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder builder = LettucePoolingClientConfiguration.builder();
                try {
                    Duration shutdownTimeout = propertiesLettuce.getShutdownTimeout();
                    if (shutdownTimeout != null) {
                        builder.shutdownTimeout(shutdownTimeout);
                    }
                } catch (NoSuchElementException ignore) {
                }
                LettuceClientConfiguration clientConfiguration = builder.poolConfig(genericObjectPoolConfig).build();
                factory = new LettuceConnectionFactory(configuration, clientConfiguration);
                registerBean.put(key + "RedisConnectionFactory", factory);
                return factory;
            };
            LettuceConnectionFactory lettuceConnectionFactory = lettuceConnectionFactorySupplier.get();
            BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(LettuceConnectionFactory.class, lettuceConnectionFactorySupplier);
            AbstractBeanDefinition factoryBean = builder.getRawBeanDefinition();
            factoryBean.setPrimary(onPrimary);
            beanDefinitionRegistry.registerBeanDefinition(key + "RedisConnectionFactory", factoryBean);
            // StringRedisTemplate
            GenericBeanDefinition stringRedisTemplate = new GenericBeanDefinition();
            stringRedisTemplate.setBeanClass(StringRedisTemplate.class);
            ConstructorArgumentValues constructorArgumentValues = new ConstructorArgumentValues();
            constructorArgumentValues.addIndexedArgumentValue(0, lettuceConnectionFactory);
            stringRedisTemplate.setConstructorArgumentValues(constructorArgumentValues);
            stringRedisTemplate.setAutowireMode(AutowireCapableBeanFactory.AUTOWIRE_BY_NAME);
            stringRedisTemplate.setPrimary(false);
            beanDefinitionRegistry.registerBeanDefinition(key + "StringRedisTemplate", stringRedisTemplate);
            // RedisTemplate
            GenericBeanDefinition redisTemplate = new GenericBeanDefinition();
            redisTemplate.setBeanClass(RedisTemplate.class);
            redisTemplate.getPropertyValues().add("connectionFactory", lettuceConnectionFactory);
            redisTemplate.setAutowireMode(AutowireCapableBeanFactory.AUTOWIRE_BY_NAME);
            redisTemplate.setPrimary(false);
            beanDefinitionRegistry.registerBeanDefinition(key + "RedisTemplate", redisTemplate);
            logger.info("Registration redis ({}) !", key);
            if (onPrimary) {
                onPrimary = false;
            }
        }
        logger.info("Registration redis completed !");
    }


    /**
     * init environment
     *
     * @param environment environment
     */
    @Override
    public void setEnvironment(Environment environment) {
        this.env = environment;
        // bing binder
        binder = Binder.get(this.env);
    }

}
