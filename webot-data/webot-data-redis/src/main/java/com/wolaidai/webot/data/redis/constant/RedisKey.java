package com.wolaidai.webot.data.redis.constant;

public interface RedisKey {
    String LOCK_KEY = "lock:%s";
    String CS_SOCKET_KEY = "cs:socket:%s";
    String H5_SOCKET_KEY = "h5:socket:%s";
    String EVENT_CLIENT_KEY = "connector:event:client";
    String EVENT_SERVER_KEY = "connector:event:server";
    String GLOBAL_CONVERSATION_KEY = "connector:conversation:clientId:%s:botId:%s";
    String BOT_CONVERSATION_KEY = "connector:bot:cid:%s";
    String CS_LAST_REPLY_SESSION_KEY = "connector:cs:lastreply:sessionkey:%s";
    String CS_OFFLINE_SESSION_KEY = "connector:cs:offline:sessionkey:%s";
    String CS_FIRSTRESP_SESSION_KEY = "connector:cs:firstresp:sessionkey:%s";
    String CS_SESSIONRESP_SESSION_KEY = "connector:cs:sessionresp:sessionkey:%s";
    String CS_RESP_DETAIL = "connector:cs:respDetail:sessionkey:%s";
    String ROOM_REQUEST_ID_KEY = "connector:room:requestId:%s";
    String CS_USER_ONLINE_EMAIL_KEY = "cs:user:online:orgId:%s:%s";
    String CS_USER_ONLINE_LIST = "cs:user:online:orgId:%s";
    String QUEUE_NORMAL = "queue:orgId:%s:normal";
    String QUEUE_VIP = "queue:orgId:%s:vip";
    String CS_SESSION_TRANS_KEY = "connector:cs:session:trans:%s";
    String CS_CLIENT_STATUS_TIMEOUT = "connector:cs:client:status:sessionkey:%s";

    String BOT_WORKTIME_CONFIG = "server:worktime:%s";
    String BOT_MENU_LIST_CONFIG = "server:bot:menu:code:%s";
    String BOT_TURNARTIFICIAL_POLICY = "server:turnArtificialPolicy:org:%s";

    String CS_WORKTIME_CONFIG = "cs:config:worktime:orgId:%s";
    String WORKBENCH_CONFIG_STATUS = "cs:config:workbench:orgId:%s:status";
    String WORKBENCH_CONFIG = "cs:config:workbench:orgId:%s";
    String SERVICEUSER_CONFIG = "cs:config:serviceuser:orgId:%s";
    String AUTOREPLY_CONFIG = "cs:config:autoreply:orgId:%s:clientType:%s";
    String SATISFACTION_CONFIG = "cs:config:satisfaction:orgId:%s";
    String RESP_TIMEOUT_CONFIG = "cs:config:respTimeout:orgId:%s";

    String WECHAT_PRESATISFACTION = "connector:wechat:presatisfaction:clientId:%s:botId:%s";
    String WECHAT_PRESWITCH_SKILLGROUP = "connector:wechat:preswitch:clientId:%s:accessKey:%s";
    String WECHAT_SWITCH_SKILLGROUP = "connector:wechat:switch:clientId:%s:accessKey:%s";
    String WECHAT_CLIENTID_BOTID_APPID = "connector:wechat:clientId:%s:botId:%s:appId";
    String WECHAT_CLIENTID_BOTID_AGENTID = "connector:wechat:clientId:%s:botId:%s:agentId";

    String CS_FACE_DETECTION = "cs:faceDetection:token:%s";

    String EXPIRE_KEY_ZSET = "connector:expire:key:zset";
}
