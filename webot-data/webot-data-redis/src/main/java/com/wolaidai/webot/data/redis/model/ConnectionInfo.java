package com.wolaidai.webot.data.redis.model;

import java.net.URI;

public class ConnectionInfo {

    private final URI uri;

    private final boolean useSsl;

    private final String username;

    private final String password;

    public ConnectionInfo(URI uri, boolean useSsl, String username, String password) {
        this.uri = uri;
        this.useSsl = useSsl;
        this.username = username;
        this.password = password;
    }

    public URI getUri() {
        return uri;
    }

    public boolean isUseSsl() {
        return useSsl;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }
}
