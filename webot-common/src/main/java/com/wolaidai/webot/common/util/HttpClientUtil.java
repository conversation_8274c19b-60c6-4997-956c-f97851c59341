
package com.wolaidai.webot.common.util;

import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

public class HttpClientUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClientUtil.class);

    private static final int TIMEOUT = 10000;

    public static String get(String url) throws Exception {
        return get(url, TIMEOUT);
    }

    public static byte[] getFile(String url) throws Exception {
        return getFile(url, TIMEOUT);
    }

    public static byte[] getFile(String url, int timeOut) throws Exception {
        long start = System.currentTimeMillis();
        LOGGER.info("get url:{}",url);
        Request request = Request.Get(url);
        if (timeOut > 0) {
            request.connectTimeout(timeOut).socketTimeout(timeOut);
        }
        HttpResponse returnResponse = request.execute().returnResponse();
        byte[] result = EntityUtils.toByteArray(returnResponse.getEntity());
        LOGGER.info("get url:{},cost:{}ms",url, System.currentTimeMillis()-start);
        return result;
    }

    public static String get(String url, int timeOut) throws Exception {
        long start = System.currentTimeMillis();
        LOGGER.info("get url:{}",url);
        Request request = Request.Get(url);
        if (timeOut > 0) {
            request.connectTimeout(timeOut).socketTimeout(timeOut);
        }

        HttpResponse returnResponse = request.execute().returnResponse();
        String result = StringUtil.unicodeToString(
                EntityUtils.toString(returnResponse.getEntity(), StandardCharsets.UTF_8));
        LOGGER.info("get url:{},response:{},cost:{}ms",url,result, System.currentTimeMillis()-start);
        return result;
    }

    /**
     * 发送HTTPS的GET请求
     *
     * @param url
     * @return 目标服务返回的结果
     * @throws Exception
     */
    public static String sendHTTPSGet(String url) throws Exception {
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, new TrustManager[]{new TrustAnyTrustManager()},
                new java.security.SecureRandom());
        URL myURL = new URL(url);
        // 创建HttpsURLConnection对象，并设置其SSLSocketFactory对象
        HttpsURLConnection httpsConn = (HttpsURLConnection) myURL.openConnection();
        httpsConn.setConnectTimeout(TIMEOUT);
        httpsConn.setReadTimeout(TIMEOUT);
        httpsConn.setHostnameVerifier(new TrustAnyHostnameVerifier());
        httpsConn.setSSLSocketFactory(sc.getSocketFactory());
        // 取得该连接的输入流，以读取响应内容
        InputStreamReader insr = new InputStreamReader(httpsConn.getInputStream());
        // 读取服务器的响应内容并显示
        StringBuilder result = new StringBuilder();
        int respInt = insr.read();
        while (respInt != -1) {
            result.append((char) respInt);
            respInt = insr.read();
        }
        return result.toString();
    }

    /**
     * 发送HTTPS pos请求
     *
     * @param url
     * @param content
     * @param charset
     * @param timeOut
     * @return String
     * @throws Exception
     */
    public static String sendHTTPSPost(String url, String content, String charset, int timeOut) throws Exception {
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, new TrustManager[]{new TrustAnyTrustManager()}, new java.security.SecureRandom());

        URL console = new URL(url);
        HttpsURLConnection conn = (HttpsURLConnection) console.openConnection();
        conn.setConnectTimeout(timeOut);
        conn.addRequestProperty("Content-Type", "application/json");
        conn.setSSLSocketFactory(sc.getSocketFactory());
        conn.setHostnameVerifier(new TrustAnyHostnameVerifier());
        conn.setDoOutput(true);
        conn.setReadTimeout(timeOut);
        conn.connect();
        DataOutputStream out = new DataOutputStream(conn.getOutputStream());
        out.write(content.getBytes(charset));
        // 刷新、关闭
        out.flush();
        out.close();
        InputStream is = conn.getInputStream();
        if (is != null) {
            ByteArrayOutputStream outStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = is.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            is.close();
            return outStream.toString();
        }
        return null;
    }

    private static class TrustAnyTrustManager implements X509TrustManager {
        public void checkClientTrusted(X509Certificate[] chain, String authType)
                throws CertificateException {
            return;
        }

        public void checkServerTrusted(X509Certificate[] chain, String authType)
                throws CertificateException {
            return;
        }

        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }
    }

    private static class TrustAnyHostnameVerifier implements HostnameVerifier {
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    }

    public static String post(String url, String data) throws Exception {
        return post(url, data, TIMEOUT);
    }

    /**
     * 发送 http post 请求
     *
     * @param url
     * @param data
     * @return
     */
    public static String post(String url, String data, int timeOut) throws Exception {
        return postWithHeader(url, data, timeOut);
    }

    public static String postFormData(String url,Iterable<? extends NameValuePair> formParams) throws Exception{
        return postFormData(url,formParams,TIMEOUT);
    }

    public static String postFormData(String url,Iterable<? extends NameValuePair> formParams, int timeOut) throws Exception{
        long start = System.currentTimeMillis();
        LOGGER.info("post url:{}",url);
        if (StringUtil.isEmpty(url)) {
            return null;
        }
        Request request = Request.Post(url).bodyForm(formParams);
        if (timeOut > 0) {
            request.connectTimeout(timeOut).socketTimeout(timeOut);
        }
        HttpResponse returnResponse = request.execute().returnResponse();
        String result = StringUtil.unicodeToString(
                EntityUtils.toString(returnResponse.getEntity(), StandardCharsets.UTF_8));
        LOGGER.info("post url:{},request:{},response:{},cost:{}ms",url,formParams,result, System.currentTimeMillis()-start);
        return result;
    }

    public static String postWithHeader(String url, String data, int timeOut, Header... headers)
            throws Exception {
        long start = System.currentTimeMillis();
        LOGGER.info("post url:{}",url);
        if (StringUtil.isEmpty(url) || StringUtil.isEmpty(data)) {
            return null;
        }
        Request request = Request.Post(url).bodyString(data, ContentType.APPLICATION_JSON);
        if (timeOut > 0) {
            request.connectTimeout(timeOut).socketTimeout(timeOut);
        }
        if (headers != null & headers.length > 0) {
            for (Header header : headers) {
                request.addHeader(header);
            }
        }
        HttpResponse returnResponse = request.execute().returnResponse();
        String result = StringUtil.unicodeToString(
                EntityUtils.toString(returnResponse.getEntity(), StandardCharsets.UTF_8));
        LOGGER.info("post url:{},request:{},response:{},cost:{}ms",url,data,result, System.currentTimeMillis()-start);
        return result;
    }
}
