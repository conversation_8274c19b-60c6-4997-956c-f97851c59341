package com.wolaidai.webot.common.util;

import org.apache.commons.lang3.time.DateUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

public class DateUtil {

    public static String formatISODate(Date date){
        return DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(ZonedDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()));
    }

    public static Date parseDate(String dateString, String format) {
        try {
            return DateUtils.parseDate(dateString, format);
        } catch (Exception e) {
            return null;
        }
    }
    
    public static String formatSecondsTime(Number seconds, String def) {
        if (null == seconds) {
            return def;
        }
        long timeMillis = seconds.longValue() * 1000l;
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
        return sdf.format(new Date(timeMillis));
    }
    
    public static String formatDateTime(Date date, String def) {
        if (null == date) {
            return def;
        }
        return new SimpleDateFormat("HH:mm:ss").format(date);
    }
    public static String formatDate(Date date, String format) {
        if (null == date) {
            return "";
        }
        return new SimpleDateFormat(format).format(date);
    }
    
    
    public static boolean isNowBetween(String startTime, String endTime) {
        if (startTime != null && endTime != null) {
            String[] start = startTime.split(":");
            String[] end = endTime.split(":");
            try {
                return isNowBetween(Integer.valueOf(start[0]), Integer.valueOf(start[1]), Integer.valueOf(end[0]), Integer.valueOf(end[1]));
            } catch (Exception e) {
            }
        }
        return false;
    }

    public static boolean isNowBetween(int startHour, int startMinute, int endHour, int endMinute) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate date = now.toLocalDate();
        return now.isAfter(LocalDateTime.of(date, LocalTime.of(startHour, startMinute))) && now.isBefore(LocalDateTime.of(date, LocalTime.of(endHour, endMinute)));
    }
    
    public static ArrayList<Date[]> splitDateByDay(Date startTime, Date endTime) {
        ArrayList<Date[]> list = new ArrayList<>();
        Calendar c = Calendar.getInstance();
        c.setTime(startTime);
        int startDay = c.get(Calendar.DAY_OF_YEAR);
        c.setTime(endTime);
        int endDay = c.get(Calendar.DAY_OF_YEAR);
        if (endTime.after(DateUtils.truncate(endTime, Calendar.DATE))) {
            endDay += 1;
        }
        c.setTime(startTime);
        Date start = DateUtils.truncate(c.getTime(), Calendar.DATE);
        for (int i = startDay; i < endDay; i++) {
            c.add(Calendar.DAY_OF_YEAR, 1);
            Date end = DateUtils.truncate(c.getTime(), Calendar.DATE);
            list.add(new Date[] { start, end });
            start = end;

        }
        return list;
    }
}
