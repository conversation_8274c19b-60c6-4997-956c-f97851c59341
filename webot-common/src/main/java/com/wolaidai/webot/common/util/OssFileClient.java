package com.wolaidai.webot.common.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.Calendar;
import java.util.Date;

public class OssFileClient {
    private static OSS ossClient;

    public static void init(String endpoint, String accessKeyId, String accessKeySecret){
        if(StringUtils.isNoneBlank(endpoint,accessKeyId,accessKeySecret)) {
            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            Runtime.getRuntime().addShutdownHook(new Thread(() -> ossClient.shutdown()));
        }
    }

    public static OSSObject getObject(String bucketName, String key) {
        return ossClient.getObject(bucketName,key);
    }

    public static ObjectMetadata getObject(String bucketName, String key, String filePath) {
        return ossClient.getObject(new GetObjectRequest(bucketName,key),new File(filePath));
    }

    /**
     * 上传文件到阿里云
     *
     * @param key
     * @param inputStream
     */
    public static void putObject(String bucketName, String key, InputStream inputStream) {
        ossClient.putObject(bucketName, key, inputStream);
    }

    //默认第二天凌晨1点过期
    public static URL generatePresignedUrl(String bucketName, String key) {
        return ossClient.generatePresignedUrl(bucketName, key, DateUtils.addHours(DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH),25));
    }

    /**
     * 根据文件的唯一key和失效日期生成该key对应文件的签名url
     *
     * @param key
     * @param expiration
     * @return
     */
    public static URL generatePresignedUrl(String bucketName, String key, Date expiration) {
        return ossClient.generatePresignedUrl(bucketName, key, expiration);
    }
}

